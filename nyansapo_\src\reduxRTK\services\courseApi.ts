import { createApi, BaseQueryFn } from "@reduxjs/toolkit/query/react";
import { apicaller } from "../../api/apicaller";

// Define Interfaces
interface RetrieveCourseResponse {
  success: boolean;
  data: {
    courses: any[];
    total: number;
    page?: number;
    limit?: number;
  };
  message: string;
}

interface CreateCoursePayload {
  title: string;
  description: string;
  url: string;
}

interface UpdateCoursePayload {
  courseId: string;
  title?: string;
  description?: string;
  url?: string;
}

interface DeleteCoursePayload {
  courseId: string;
}

interface GetSingleCourseResponse {
  success: boolean;
  message: string;
  data: {
    id: string;
    title: string;
    description: string;
    url: string;
    createdAt: string;
    updatedAt: string;
    quizzes: Array<{
      id: string;
      question: string;
      options: string[];
      answer: string;
    }>;
  };
}

// New Assignment Interfaces
interface AssignCoursePayload {
  courseId: string;
  userIds: string[];
  accessToken: string;
}

interface UnassignCoursePayload {
  courseId: string;
  userIds: string[];
  accessToken: string;
}

interface AssignmentResponse {
  success: boolean;
  message: string;
  assignedCount?: number;
  duplicateCount?: number;
  unassignedCount?: number;
}

interface CourseAssignmentsResponse {
  success: boolean;
  message: string;
  data: Array<{
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    assignedAt: string;
  }>;
}

interface UserAssignmentsResponse {
  success: boolean;
  message: string;
  data: Array<{
    id: string;
    title: string;
    description: string;
    url: string;
    assignedAt: string;
  }>;
}

// Define the custom base query
const customBaseQuery: BaseQueryFn<
  { url: string; payload?: Record<string, any> },
  RetrieveCourseResponse | GetSingleCourseResponse | { success: boolean; message: string },
  { message: string }
> = async ({ url, payload }, { dispatch }) => {
  try {
    const actualPayload = payload || {};
    const response = await apicaller(actualPayload, url);

    console.log("Raw API response:", response);

    if (response.success) {
      if (response.data !== undefined && response.data !== null) {
        if (Array.isArray(response.data.courses)) {
          return {
            data: {
              success: response.success,
              message: response.message,
              data: {
                courses: response.data.courses,
                total: response.data.total,
                page: actualPayload.page || 1,
                limit: actualPayload.limit || response.data.courses.length,
              },
            },
          };
        }

        if (response.data.id && response.data.title) {
          return { data: response as GetSingleCourseResponse };
        }
      }

      return {
        data: {
          success: response.success,
          message: response.message,
        },
      };
    } else {
      if (response.message === "Invalid or expired token") {
        localStorage.removeItem("token");
        dispatch(courseApi.util.resetApiState());
        window.location.href = "/";
      }
      return { error: { message: response.message } };
    }
  } catch (error: any) {
    return { error: { message: error.message || "An unknown error occurred" } };
  }
};

// Define the API
export const courseApi = createApi({
  reducerPath: "courseApi",
  baseQuery: customBaseQuery,
  tagTypes: ['CourseAssignments'],
  endpoints: (builder) => ({
    retrieveCourse: builder.query<RetrieveCourseResponse, { page?: number; limit?: number; accessToken: string }>(
      {
        query: ({ page, limit, accessToken }) => {
          const params = new URLSearchParams();
          if (page !== undefined) params.append("page", page.toString());
          if (limit !== undefined) params.append("limit", limit.toString());

          return {
            url: `main-course-management?${params.toString()}`,
            payload: { mode: "retrieve", accessToken },
          };
        },
      }
    ),

    getCourseByUserId: builder.query<RetrieveCourseResponse, { userId: string; page?: number; limit?: number; accessToken: string }>(
      {
        query: ({ userId, page, limit, accessToken }) => {
          const params = new URLSearchParams();
          if (page !== undefined) params.append("page", page.toString());
          if (limit !== undefined) params.append("limit", limit.toString());

          return {
            url: `course-by-user?${params.toString()}`,
            payload: { mode: "getcoursesbyuser", userId, accessToken }, // Use the specific mode
          };
        },
      }
    ),

    createCourse: builder.mutation<{ success: boolean; message: string }, CreateCoursePayload & { accessToken: string }>(
      {
        query: ({ title, description, url, accessToken }) => ({
          url: "main-course-management",
          payload: {
            mode: "createnew",
            title,
            description,
            url,
            accessToken,
          },
        }),
      }
    ),

    deleteCourse: builder.mutation<{ success: boolean; message: string }, DeleteCoursePayload & { accessToken: string }>(
      {
        query: ({ courseId, accessToken }) => ({
          url: "main-course-management",
          payload: {
            mode: "delete",
            id: courseId,
            accessToken,
          },
        }),
      }
    ),

    updateCourse: builder.mutation<RetrieveCourseResponse, UpdateCoursePayload & { userIds?: string[]; accessToken: string }>(
      {
        query: ({ courseId, title, description, url, userIds, accessToken }) => ({
          url: "main-course-management",
          payload: {
            mode: "update",
            id: courseId,
            title,
            description,
            url,
            userIds,
            accessToken,
          },
        }),
      }
    ),

    getSingleCourse: builder.query<GetSingleCourseResponse, { id: string; accessToken: string }>(
      {
        query: ({ id, accessToken }) => ({
          url: "main-course-management",
          payload: {
            mode: "getsingle",
            id,
            accessToken,
          },
        }),
        transformResponse: (response: GetSingleCourseResponse) => {
          return {
            ...response,
            data: {
              ...response.data,
              quizzes: response.data.quizzes.map((quiz) => ({
                ...quiz,
                answer: quiz.answer?.trim()?.toLowerCase(),
              })),
            },
          };
        },
      }
    ),

    // New Assignment Management Endpoints
    assignCourse: builder.mutation<AssignmentResponse, AssignCoursePayload>({
      query: ({ courseId, userIds, accessToken }) => ({
        url: "main-course-management",
        payload: {
          mode: "assign",
          id: courseId,
          userIds,
          accessToken,
        },
      }),
      invalidatesTags: ['CourseAssignments'],
    }),

    unassignCourse: builder.mutation<AssignmentResponse, UnassignCoursePayload>({
      query: ({ courseId, userIds, accessToken }) => ({
        url: "main-course-management",
        payload: {
          mode: "unassign",
          id: courseId,
          userIds,
          accessToken,
        },
      }),
      invalidatesTags: ['CourseAssignments'],
    }),

    getCourseAssignments: builder.query<CourseAssignmentsResponse, { courseId: string; accessToken: string }>({
      query: ({ courseId, accessToken }) => ({
        url: "main-course-management",
        payload: {
          mode: "assignments",
          id: courseId,
          accessToken,
        },
      }),
      providesTags: ['CourseAssignments'],
    }),

    getUserAssignments: builder.query<UserAssignmentsResponse, { userId: string; accessToken: string }>({
      query: ({ userId, accessToken }) => ({
        url: "main-course-management",
        payload: {
          mode: "user-assignments",
          userId,
          accessToken,
        },
      }),
    }),
  }),
});

// Export hooks
export const {
  useRetrieveCourseQuery,
  useGetCourseByUserIdQuery,
  useCreateCourseMutation,
  useDeleteCourseMutation,
  useUpdateCourseMutation,
  useGetSingleCourseQuery,
  // New Assignment Hooks
  useAssignCourseMutation,
  useUnassignCourseMutation,
  useGetCourseAssignmentsQuery,
  useGetUserAssignmentsQuery,
} = courseApi;

"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUserSchema = void 0;
const zod_1 = __importDefault(require("zod"));
exports.updateUserSchema = zod_1.default.object({
    email: zod_1.default.string().email("Invalid email address").optional(),
    password: zod_1.default.string().min(6, "Password must be at least 6 characters").optional(),
    firstName: zod_1.default.string().min(1, "First name is required").optional(),
    lastName: zod_1.default.string().min(1, "Last name is required").optional(),
    role: zod_1.default.string().min(1, "Role is required").optional(),
});

/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/page";
exports.ids = ["app/auth/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/page.tsx */ \"(rsc)/./src/app/auth/page.tsx\")), \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/layout.tsx */ \"(rsc)/./src/app/auth/layout.tsx\")), \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/page\",\n        pathname: \"/auth\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2F%40ant-design%2Fnextjs-registry%2Fes%2FAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fjsvectormap%2Fdist%2Fjsvectormap.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fflatpickr%2Fdist%2Fflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fsatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fprovider%2FProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2F%40ant-design%2Fnextjs-registry%2Fes%2FAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fjsvectormap%2Fdist%2Fjsvectormap.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fflatpickr%2Fdist%2Fflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fsatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fprovider%2FProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js */ \"(ssr)/./node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/provider/Provider.tsx */ \"(ssr)/./src/provider/Provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2F%40ant-design%2Fnextjs-registry%2Fes%2FAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fjsvectormap%2Fdist%2Fjsvectormap.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fflatpickr%2Fdist%2Fflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fsatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fprovider%2FProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Fauth%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Fauth%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/layout.tsx */ \"(ssr)/./src/app/auth/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1udCUyRmUlMkZQUk9KRUNUUyUyRkZSQU5DSVMlMjBBU0FOVEUlMkZueWFuc2Fwb18lMkZzcmMlMkZhcHAlMkZhdXRoJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQXFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8/ZDU2NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9tbnQvZS9QUk9KRUNUUy9GUkFOQ0lTIEFTQU5URS9ueWFuc2Fwb18vc3JjL2FwcC9hdXRoL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Fauth%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Fauth%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Fauth%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/page.tsx */ \"(ssr)/./src/app/auth/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1udCUyRmUlMkZQUk9KRUNUUyUyRkZSQU5DSVMlMjBBU0FOVEUlMkZueWFuc2Fwb18lMkZzcmMlMkZhcHAlMkZhdXRoJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFtRyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvPzkwZDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvbW50L2UvUFJPSkVDVFMvRlJBTkNJUyBBU0FOVEUvbnlhbnNhcG9fL3NyYy9hcHAvYXV0aC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Fauth%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/api/apicaller.ts":
/*!******************************!*\
  !*** ./src/api/apicaller.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apicaller: () => (/* binding */ apicaller),\n/* harmony export */   loginapicaller: () => (/* binding */ loginapicaller)\n/* harmony export */ });\n// const BASEURL = 'http://localhost:5051/api/';\nconst BASEURL = \"https://nyansapo-api.vercel.app/api/\";\n//const BASEURL = 'https://nyansapoapi-production.up.railway.app/api/';\n// Function to check if the input is an object\nconst isitanobject = (text)=>{\n    try {\n        if (text == null || text == undefined || text == \"\") {\n            return false;\n        }\n        if (typeof text === \"object\" && !Array.isArray(text) && text !== null) {\n            return true;\n        } else {\n            return false;\n        }\n    } catch (error) {\n        return false;\n    }\n};\n// Client-side function to include role in headers\nconst apicaller = async (payloaddata, urlpath)=>{\n    try {\n        const headers = {\n            \"Accept\": \"application/json\",\n            \"Content-Type\": \"application/json\"\n        };\n        const options = {\n            method: \"POST\",\n            headers: headers,\n            body: JSON.stringify(payloaddata),\n            credentials: \"include\"\n        };\n        const fetchResult = await fetch(`${BASEURL}${urlpath}`, options);\n        const result = await fetchResult.json();\n        if (fetchResult.ok) {\n            return {\n                success: true,\n                message: result.message || \"Action completed successfully\",\n                data: result.data || null\n            };\n        } else {\n            return {\n                success: false,\n                message: result.message || \"Action failed\"\n            };\n        }\n    } catch (error) {\n        console.error(\"API call failed:\", error);\n        return {\n            success: false,\n            message: \"Unable to complete action. An error occurred!\"\n        };\n    }\n};\nconst loginapicaller = async (payloaddata, urlpath)=>{\n    try {\n        // Initialize the request headers (without Authorization and X-User-Role)\n        const requestheaders = {\n            Accept: \"application/json\",\n            \"Content-Type\": \"application/json\"\n        };\n        // Log the headers before sending (for debugging purposes)\n        console.log(\"Request Headers:\", requestheaders);\n        // Configure the request options, including method, headers, and body\n        const options = {\n            method: \"POST\",\n            headers: requestheaders,\n            body: JSON.stringify(payloaddata)\n        };\n        // Make the fetch request\n        const fetchResult = await fetch(`${BASEURL}${urlpath}`, options);\n        const result = await fetchResult.json();\n        // Log the response data\n        console.log(\"Response received:\", result);\n        // Check if the request was successful\n        if (fetchResult.ok) {\n            return {\n                success: true,\n                message: result.message || \"Action completed successfully\",\n                data: result.data || null\n            };\n        } else {\n            // Handle the error response\n            return {\n                success: false,\n                message: result.message || \"Action failed\"\n            };\n        }\n    } catch (error) {\n        // Handle any unexpected errors\n        console.error(\"API call failed:\", error);\n        return {\n            success: false,\n            message: \"Unable to complete action. An error occurred!\"\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/api/apicaller.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/layout.tsx":
/*!*********************************!*\
  !*** ./src/app/auth/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsvectormap_dist_jsvectormap_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsvectormap/dist/jsvectormap.css */ \"(ssr)/./node_modules/jsvectormap/dist/jsvectormap.css\");\n/* harmony import */ var flatpickr_dist_flatpickr_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! flatpickr/dist/flatpickr.min.css */ \"(ssr)/./node_modules/flatpickr/dist/flatpickr.min.css\");\n/* harmony import */ var _css_satoshi_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/css/satoshi.css */ \"(ssr)/./src/css/satoshi.css\");\n/* harmony import */ var _css_style_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/css/style.css */ \"(ssr)/./src/css/style.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(ssr)/./node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* harmony import */ var _provider_Provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/provider/Provider */ \"(ssr)/./src/provider/Provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dark:bg-boxdark-2 dark:text-bodydark\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_provider_Provider__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__.AntdRegistry, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2F1dGgvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFDMEM7QUFDQTtBQUNmO0FBQ0Y7QUFDQztBQUNpQztBQUNWO0FBRWxDLFNBQVNHLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQywwQkFBMEI7c0JBQzlCLDRFQUFDQztnQkFBSUMsV0FBVTswQkFFYiw0RUFBQ1IsMERBQWNBOzhCQUNiLDRFQUFDRCxxRUFBWUE7a0NBQUVHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU0zQiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9zcmMvYXBwL2F1dGgvbGF5b3V0LnRzeD80ODU1Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgXCJqc3ZlY3Rvcm1hcC9kaXN0L2pzdmVjdG9ybWFwLmNzc1wiO1xyXG5pbXBvcnQgXCJmbGF0cGlja3IvZGlzdC9mbGF0cGlja3IubWluLmNzc1wiO1xyXG5pbXBvcnQgXCJAL2Nzcy9zYXRvc2hpLmNzc1wiO1xyXG5pbXBvcnQgXCJAL2Nzcy9zdHlsZS5jc3NcIjtcclxuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBBbnRkUmVnaXN0cnkgfSBmcm9tIFwiQGFudC1kZXNpZ24vbmV4dGpzLXJlZ2lzdHJ5XCI7XHJcbmltcG9ydCBDbGllbnRQcm92aWRlciBmcm9tIFwiQC9wcm92aWRlci9Qcm92aWRlclwiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IFJlYWRvbmx5PHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59Pikge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPGJvZHkgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPXt0cnVlfT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRhcms6YmctYm94ZGFyay0yIGRhcms6dGV4dC1ib2R5ZGFya1wiPlxyXG4gICAgICAgICAgey8qIHtsb2FkaW5nID8gPExvYWRlciAvPiA6IGNoaWxkcmVufSAqL31cclxuICAgICAgICAgIDxDbGllbnRQcm92aWRlcj5cclxuICAgICAgICAgICAgPEFudGRSZWdpc3RyeT57Y2hpbGRyZW59PC9BbnRkUmVnaXN0cnk+XHJcbiAgICAgICAgICA8L0NsaWVudFByb3ZpZGVyPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJBbnRkUmVnaXN0cnkiLCJDbGllbnRQcm92aWRlciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/page.tsx":
/*!*******************************!*\
  !*** ./src/app/auth/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignIn)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Divider,Form,Input,Spin,message!=!antd */ \"(ssr)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Divider,Form,Input,Spin,message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Divider,Form,Input,Spin,message!=!antd */ \"(ssr)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Divider,Form,Input,Spin,message!=!antd */ \"(ssr)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Divider,Form,Input,Spin,message!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Divider,Form,Input,Spin,message!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../reduxRTK/services/authApi */ \"(ssr)/./src/reduxRTK/services/authApi.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction SignIn() {\n    const [form] = _barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].useForm();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [login, { isLoading: apiLoading, error }] = (0,_reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_3__.useLoginMutation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const showMessage = (type, content)=>{\n        if (type === \"success\") {\n            _barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success(content);\n        } else {\n            _barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error(content);\n        }\n    };\n    const onFinish = async (values)=>{\n        setIsLoading(true);\n        try {\n            const response = await login({\n                email: values.email,\n                password: values.password\n            }).unwrap();\n            if (response.success) {\n                setIsLoading(false);\n                showMessage(\"success\", response.message || \"Login successful.\");\n                const { user } = response.data || {};\n                if (user?.role === \"admin\") {\n                    router.replace(\"/dashboard\");\n                } else if (user?.role === \"user\") {\n                    router.replace(\"/user/dashboard\");\n                } else {\n                    showMessage(\"error\", \"Role not recognized.\");\n                    router.replace(\"/\");\n                }\n            } else {\n                setIsLoading(false);\n                showMessage(\"error\", response.message || \"An error occurred.\");\n            }\n        } catch (err) {\n            setIsLoading(false);\n            if (err?.data) {\n                showMessage(\"error\", err.data.message || \"An unknown error occurred.\");\n            } else if (err?.message) {\n                showMessage(\"error\", err.message || \"An error occurred. Please try again.\");\n            } else {\n                showMessage(\"error\", \"An unknown error occurred.\");\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative min-h-screen flex items-center justify-center py-6 sm:py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                src: \"/images/background.jpg\",\n                alt: \"Background\",\n                fill: true,\n                style: {\n                    objectFit: \"cover\"\n                },\n                className: \"absolute inset-0 z-0\"\n            }, void 0, false, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex flex-col-reverse lg:flex-row bg-white bg-opacity-90 shadow-lg rounded-3xl w-full max-w-4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full lg:w-1/2 p-8 lg:p-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-center lg:text-left mb-4\",\n                                children: \"Login\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                form: form,\n                                onFinish: onFinish,\n                                layout: \"vertical\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Item, {\n                                        name: \"email\",\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: \"Please enter your email address\"\n                                            },\n                                            {\n                                                type: \"email\",\n                                                message: \"Please enter a valid email address\"\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            placeholder: \"Email Address\",\n                                            className: \"h-10 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Item, {\n                                        name: \"password\",\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: \"Please enter your password\"\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Password, {\n                                            placeholder: \"Password\",\n                                            className: \"h-10 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            className: \"w-full bg-cyan-500\",\n                                            disabled: isLoading || apiLoading,\n                                            children: isLoading || apiLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Divider_Form_Input_Spin_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 44\n                                            }, this) : \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full lg:w-1/2 bg-gradient-to-r from-cyan-400 to-sky-500 text-white p-8 lg:p-12 flex flex-col justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-center\",\n                                children: \"Welcome to NyansaPo Cybersecurity Nano Learning Portal\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-center text-lg\",\n                                children: \"Empower Your Cybersecurity Skills, One Byte at a Time\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/provider/Provider.tsx":
/*!***********************************!*\
  !*** ./src/provider/Provider.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ClientProvider = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_3__.Provider, {\n        store: _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_2__.store,\n        children: children\n    }, void 0, false, {\n        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/provider/Provider.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXIvUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ2E7QUFDUztBQU1oRCxNQUFNRyxpQkFBZ0QsQ0FBQyxFQUFFQyxRQUFRLEVBQUU7SUFDakUscUJBQU8sOERBQUNILGlEQUFRQTtRQUFDQyxPQUFPQSx3REFBS0E7a0JBQUdFOzs7Ozs7QUFDbEM7QUFFQSxpRUFBZUQsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9zcmMvcHJvdmlkZXIvUHJvdmlkZXIudHN4PzRiNDIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnOyBcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IFByb3ZpZGVyIH0gZnJvbSAncmVhY3QtcmVkdXgnO1xyXG5pbXBvcnQgeyBzdG9yZSB9IGZyb20gJy4uL3JlZHV4UlRLL3N0b3JlL3N0b3JlJzsgXHJcblxyXG5pbnRlcmZhY2UgQ2xpZW50UHJvdmlkZXJQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufVxyXG5cclxuY29uc3QgQ2xpZW50UHJvdmlkZXI6IFJlYWN0LkZDPENsaWVudFByb3ZpZGVyUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIHJldHVybiA8UHJvdmlkZXIgc3RvcmU9e3N0b3JlfT57Y2hpbGRyZW59PC9Qcm92aWRlcj47XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBDbGllbnRQcm92aWRlcjtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiUHJvdmlkZXIiLCJzdG9yZSIsIkNsaWVudFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/provider/Provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/authApi.ts":
/*!******************************************!*\
  !*** ./src/reduxRTK/services/authApi.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   useCheckAuthQuery: () => (/* binding */ useCheckAuthQuery),\n/* harmony export */   useCreateUserMutation: () => (/* binding */ useCreateUserMutation),\n/* harmony export */   useDeleteUserMutation: () => (/* binding */ useDeleteUserMutation),\n/* harmony export */   useGetUsersQuery: () => (/* binding */ useGetUsersQuery),\n/* harmony export */   useLoginMutation: () => (/* binding */ useLoginMutation),\n/* harmony export */   useLogoutMutation: () => (/* binding */ useLogoutMutation),\n/* harmony export */   useResetPasswordMutation: () => (/* binding */ useResetPasswordMutation),\n/* harmony export */   useSendDetailsMutation: () => (/* binding */ useSendDetailsMutation),\n/* harmony export */   useUpdateUserMutation: () => (/* binding */ useUpdateUserMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Custom baseQuery function\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            return {\n                data: response\n            };\n        } else {\n            // Handle Unauthorized and Token Expiry\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"userRole\");\n                localStorage.removeItem(\"firstName\");\n                localStorage.removeItem(\"lastName\");\n                localStorage.removeItem(\"userId\");\n                dispatch(authApi.util.resetApiState());\n                window.location.href = \"/auth\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Create the base API service using RTK Query\nconst authApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"authApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            login: builder.mutation({\n                query: (credentials)=>({\n                        url: \"user/login\",\n                        payload: credentials\n                    }),\n                onQueryStarted: async ({ email, password }, { dispatch, queryFulfilled })=>{\n                    try {\n                        const { data } = await queryFulfilled;\n                        if (data?.success && data?.data) {\n                            const { user, accessToken } = data.data;\n                            if (user && accessToken) {\n                                localStorage.setItem(\"userRole\", user.role || \"\");\n                                localStorage.setItem(\"firstName\", user.firstName || \"\");\n                                localStorage.setItem(\"lastName\", user.lastName || \"\");\n                                localStorage.setItem(\"userId\", user.id || \"\");\n                                localStorage.setItem(\"token\", accessToken);\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Login failed\", error);\n                    }\n                }\n            }),\n            logout: builder.mutation({\n                query: ()=>({\n                        url: \"user/logout\"\n                    }),\n                onQueryStarted: async (_, { dispatch, queryFulfilled })=>{\n                    try {\n                        const { data } = await queryFulfilled;\n                        if (data?.success) {\n                            localStorage.removeItem(\"userRole\");\n                            localStorage.removeItem(\"firstName\");\n                            localStorage.removeItem(\"lastName\");\n                            localStorage.removeItem(\"userId\");\n                            localStorage.removeItem(\"token\");\n                            dispatch(authApi.util.resetApiState());\n                            window.location.href = \"/auth\";\n                        }\n                    } catch (error) {\n                        console.error(\"Logout failed\", error);\n                    }\n                }\n            }),\n            checkAuth: builder.query({\n                query: ()=>({\n                        url: \"user/check/auth\",\n                        payload: {\n                            mode: \"checkauth\"\n                        }\n                    })\n            }),\n            createUser: builder.mutation({\n                query: (newUsers)=>({\n                        url: \"user/register\",\n                        payload: {\n                            mode: \"createnew\",\n                            users: newUsers.users\n                        }\n                    })\n            }),\n            getUsers: builder.query({\n                query: ({ page, limit, accessToken })=>{\n                    const params = new URLSearchParams({\n                        page: String(page || 1),\n                        limit: String(limit || 10)\n                    });\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    return {\n                        url: `user/getusers?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                },\n                transformResponse: (response)=>({\n                        ...response,\n                        data: {\n                            ...response.data,\n                            users: response.data.users\n                        }\n                    })\n            }),\n            deleteUser: builder.mutation({\n                query: ({ mode, userId, accessToken })=>({\n                        url: \"user/delete\",\n                        payload: {\n                            mode,\n                            userId,\n                            accessToken\n                        }\n                    })\n            }),\n            updateUser: builder.mutation({\n                query: ({ userId, mode, accessToken, ...updateData })=>({\n                        url: \"user/update\",\n                        payload: {\n                            userId,\n                            mode,\n                            accessToken,\n                            ...updateData\n                        }\n                    })\n            }),\n            sendDetails: builder.mutation({\n                query: ({ mode, userId, accessToken })=>{\n                    // Ensure mode is either 'senddetails' (single) or 'sendbulkdetails' (all)\n                    if (mode !== \"senddetails\" && mode !== \"sendbulkdetails\") {\n                        throw new Error(\"Invalid mode. Use 'senddetails' for a single user or 'sendbulkdetails' for all users.\");\n                    }\n                    return {\n                        url: \"user/send-details\",\n                        payload: mode === \"senddetails\" ? {\n                            mode,\n                            userId,\n                            accessToken\n                        } : {\n                            mode,\n                            accessToken\n                        }\n                    };\n                }\n            }),\n            // sendDetails: builder.mutation<\n            //   SendDetailsResponse,\n            //   { mode: string; userIds?: string[]; accessToken: string }\n            // >({\n            //   query: ({ mode, userIds, accessToken }) => {\n            //     if (!accessToken) {\n            //       throw new Error(\"Access token is required but missing.\");\n            //     }\n            //     console.log(\"Sending Payload:\", { mode, userIds, accessToken }); // Debugging\n            //     return {\n            //       url: \"user/send-details\",\n            //       payload: mode === \"senddetails\" ? { mode, userIds, accessToken } : { mode, accessToken },\n            //     };\n            //   },\n            // }),\n            resetPassword: builder.mutation({\n                query: ({ userId, accessToken })=>({\n                        url: \"user/reset-password\",\n                        payload: {\n                            userId,\n                            accessToken\n                        }\n                    }),\n                async onQueryStarted ({ userId, accessToken }, { queryFulfilled }) {\n                    try {\n                        const { data } = await queryFulfilled;\n                        if (data?.success) {\n                            // If successful, the data contains user details\n                            const userDetails = data; // This contains the user details\n                            // Store the user details in localStorage\n                            const { id, firstName, lastName, role, email } = userDetails.data;\n                            localStorage.setItem(\"userRole\", role || \"\");\n                            localStorage.setItem(\"firstName\", firstName || \"\");\n                            localStorage.setItem(\"lastName\", lastName || \"\");\n                            localStorage.setItem(\"userId\", id || \"\");\n                            localStorage.setItem(\"userEmail\", email || \"\");\n                            console.log(\"User details retrieved and stored:\", userDetails);\n                        } else {\n                            console.warn(\"Failed to retrieve user details or user mismatch\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error retrieving user details:\", error?.message || \"Unknown error\");\n                    }\n                }\n            })\n        })\n});\nconst { useLoginMutation, useLogoutMutation, useCheckAuthQuery, useCreateUserMutation, useGetUsersQuery, useDeleteUserMutation, useUpdateUserMutation, useSendDetailsMutation, useResetPasswordMutation } = authApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/authApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/courseApi.ts":
/*!********************************************!*\
  !*** ./src/reduxRTK/services/courseApi.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   courseApi: () => (/* binding */ courseApi),\n/* harmony export */   useAssignCourseMutation: () => (/* binding */ useAssignCourseMutation),\n/* harmony export */   useCreateCourseMutation: () => (/* binding */ useCreateCourseMutation),\n/* harmony export */   useDeleteCourseMutation: () => (/* binding */ useDeleteCourseMutation),\n/* harmony export */   useGetCourseAssignmentsQuery: () => (/* binding */ useGetCourseAssignmentsQuery),\n/* harmony export */   useGetCourseByUserIdQuery: () => (/* binding */ useGetCourseByUserIdQuery),\n/* harmony export */   useGetSingleCourseQuery: () => (/* binding */ useGetSingleCourseQuery),\n/* harmony export */   useGetUserAssignmentsQuery: () => (/* binding */ useGetUserAssignmentsQuery),\n/* harmony export */   useRetrieveCourseQuery: () => (/* binding */ useRetrieveCourseQuery),\n/* harmony export */   useUnassignCourseMutation: () => (/* binding */ useUnassignCourseMutation),\n/* harmony export */   useUpdateCourseMutation: () => (/* binding */ useUpdateCourseMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const actualPayload = payload || {};\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(actualPayload, url);\n        console.log(\"Raw API response:\", response);\n        if (response.success) {\n            if (response.data !== undefined && response.data !== null) {\n                if (Array.isArray(response.data.courses)) {\n                    return {\n                        data: {\n                            success: response.success,\n                            message: response.message,\n                            data: {\n                                courses: response.data.courses,\n                                total: response.data.total,\n                                page: actualPayload.page || 1,\n                                limit: actualPayload.limit || response.data.courses.length\n                            }\n                        }\n                    };\n                }\n                if (response.data.id && response.data.title) {\n                    return {\n                        data: response\n                    };\n                }\n            }\n            return {\n                data: {\n                    success: response.success,\n                    message: response.message\n                }\n            };\n        } else {\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                dispatch(courseApi.util.resetApiState());\n                window.location.href = \"/\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API\nconst courseApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"courseApi\",\n    baseQuery: customBaseQuery,\n    tagTypes: [\n        \"CourseAssignments\"\n    ],\n    endpoints: (builder)=>({\n            retrieveCourse: builder.query({\n                query: ({ page, limit, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    return {\n                        url: `main-course-management?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                }\n            }),\n            getCourseByUserId: builder.query({\n                query: ({ userId, page, limit, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    return {\n                        url: `course-by-user?${params.toString()}`,\n                        payload: {\n                            mode: \"getcoursesbyuser\",\n                            userId,\n                            accessToken\n                        }\n                    };\n                }\n            }),\n            createCourse: builder.mutation({\n                query: ({ title, description, url, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"createnew\",\n                            title,\n                            description,\n                            url,\n                            accessToken\n                        }\n                    })\n            }),\n            deleteCourse: builder.mutation({\n                query: ({ courseId, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"delete\",\n                            id: courseId,\n                            accessToken\n                        }\n                    })\n            }),\n            updateCourse: builder.mutation({\n                query: ({ courseId, title, description, url, userIds, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"update\",\n                            id: courseId,\n                            title,\n                            description,\n                            url,\n                            userIds,\n                            accessToken\n                        }\n                    })\n            }),\n            getSingleCourse: builder.query({\n                query: ({ id, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"getsingle\",\n                            id,\n                            accessToken\n                        }\n                    }),\n                transformResponse: (response)=>{\n                    return {\n                        ...response,\n                        data: {\n                            ...response.data,\n                            quizzes: response.data.quizzes.map((quiz)=>({\n                                    ...quiz,\n                                    answer: quiz.answer?.trim()?.toLowerCase()\n                                }))\n                        }\n                    };\n                }\n            }),\n            // New Assignment Management Endpoints\n            assignCourse: builder.mutation({\n                query: ({ courseId, userIds, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"assign\",\n                            id: courseId,\n                            userIds,\n                            accessToken\n                        }\n                    }),\n                invalidatesTags: [\n                    \"CourseAssignments\"\n                ]\n            }),\n            unassignCourse: builder.mutation({\n                query: ({ courseId, userIds, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"unassign\",\n                            id: courseId,\n                            userIds,\n                            accessToken\n                        }\n                    }),\n                invalidatesTags: [\n                    \"CourseAssignments\"\n                ]\n            }),\n            getCourseAssignments: builder.query({\n                query: ({ courseId, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"assignments\",\n                            id: courseId,\n                            accessToken\n                        }\n                    }),\n                providesTags: [\n                    \"CourseAssignments\"\n                ]\n            }),\n            getUserAssignments: builder.query({\n                query: ({ userId, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"user-assignments\",\n                            userId,\n                            accessToken\n                        }\n                    })\n            })\n        })\n});\n// Export hooks\nconst { useRetrieveCourseQuery, useGetCourseByUserIdQuery, useCreateCourseMutation, useDeleteCourseMutation, useUpdateCourseMutation, useGetSingleCourseQuery, // New Assignment Hooks\nuseAssignCourseMutation, useUnassignCourseMutation, useGetCourseAssignmentsQuery, useGetUserAssignmentsQuery } = courseApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/courseApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/demoApi.ts":
/*!******************************************!*\
  !*** ./src/reduxRTK/services/demoApi.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   demoApi: () => (/* binding */ demoApi),\n/* harmony export */   useSubmitDemoRequestMutation: () => (/* binding */ useSubmitDemoRequestMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query following your existing pattern\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            return {\n                data: response\n            };\n        } else {\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API following your existing pattern\nconst demoApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"demoApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            submitDemoRequest: builder.mutation({\n                query: (demoData)=>({\n                        url: \"demo-request\",\n                        payload: demoData\n                    })\n            })\n        })\n});\n// Export hooks following your existing pattern\nconst { useSubmitDemoRequestMutation } = demoApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/demoApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/quizApi.ts":
/*!******************************************!*\
  !*** ./src/reduxRTK/services/quizApi.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   quizApi: () => (/* binding */ quizApi),\n/* harmony export */   useCreateQuizMutation: () => (/* binding */ useCreateQuizMutation),\n/* harmony export */   useDeleteQuizMutation: () => (/* binding */ useDeleteQuizMutation),\n/* harmony export */   useGetSingleQuizQuery: () => (/* binding */ useGetSingleQuizQuery),\n/* harmony export */   useRetrieveQuizQuery: () => (/* binding */ useRetrieveQuizQuery),\n/* harmony export */   useUpdateQuizMutation: () => (/* binding */ useUpdateQuizMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            if (response.data) {\n                // Handle RetrieveQuizResponse\n                if (Array.isArray(response.data.quizzes) && typeof response.data.total === \"number\") {\n                    return {\n                        data: response\n                    };\n                }\n                // Handle GetSingleQuizResponse\n                if (typeof response.data.id === \"string\" && typeof response.data.question === \"string\" && Array.isArray(response.data.options)) {\n                    return {\n                        data: response\n                    };\n                }\n            }\n            // If `data` is null or not needed, return success and message only\n            return {\n                data: {\n                    success: response.success,\n                    message: response.message\n                }\n            };\n        } else {\n            // Handle token expiration\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                dispatch(quizApi.util.resetApiState());\n                window.location.href = \"/\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API\nconst quizApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"quizApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            retrieveQuiz: builder.query({\n                query: ({ page, limit, courseId, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    if (courseId) params.append(\"courseId\", courseId);\n                    return {\n                        url: `main-quiz-management?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                },\n                transformResponse: (response)=>({\n                        ...response,\n                        data: {\n                            ...response.data,\n                            quizzes: response.data.quizzes || []\n                        }\n                    })\n            }),\n            createQuiz: builder.mutation({\n                query: ({ courseId, question, options, answer, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"createnew\",\n                            courseId,\n                            question,\n                            options,\n                            answer,\n                            accessToken\n                        }\n                    })\n            }),\n            deleteQuiz: builder.mutation({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"delete\",\n                            id,\n                            accessToken\n                        }\n                    })\n            }),\n            updateQuiz: builder.mutation({\n                query: ({ id, courseId, question, options, answer, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"update\",\n                            id,\n                            courseId,\n                            question,\n                            options,\n                            answer,\n                            accessToken\n                        }\n                    })\n            }),\n            getSingleQuiz: builder.query({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"getsingle\",\n                            id,\n                            accessToken\n                        }\n                    })\n            })\n        })\n});\n// Export hooks\nconst { useRetrieveQuizQuery, useCreateQuizMutation, useDeleteQuizMutation, useUpdateQuizMutation, useGetSingleQuizQuery } = quizApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/quizApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/reportApi.ts":
/*!********************************************!*\
  !*** ./src/reduxRTK/services/reportApi.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reportApi: () => (/* binding */ reportApi),\n/* harmony export */   useCreateReportMutation: () => (/* binding */ useCreateReportMutation),\n/* harmony export */   useDeleteReportMutation: () => (/* binding */ useDeleteReportMutation),\n/* harmony export */   useGetSingleReportQuery: () => (/* binding */ useGetSingleReportQuery),\n/* harmony export */   useRetrieveReportQuery: () => (/* binding */ useRetrieveReportQuery),\n/* harmony export */   useUpdateReportMutation: () => (/* binding */ useUpdateReportMutation),\n/* harmony export */   useUpsertReportMutation: () => (/* binding */ useUpsertReportMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            if (response.data) {\n                // Handle RetrieveReportResponse\n                if (Array.isArray(response.data.reports) && typeof response.data.total === \"number\") {\n                    return {\n                        data: response\n                    };\n                }\n                // Handle GetSingleReportResponse\n                if (typeof response.data.id === \"string\" && typeof response.data.courseId === \"string\" && typeof response.data.userId === \"string\") {\n                    return {\n                        data: response\n                    };\n                }\n            }\n            // If `data` is null or not needed, return success and message only\n            return {\n                data: {\n                    success: response.success,\n                    message: response.message\n                }\n            };\n        } else {\n            // Handle token expiration\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                dispatch(reportApi.util.resetApiState());\n                window.location.href = \"/\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API\nconst reportApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"reportApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            retrieveReport: builder.query({\n                query: ({ page, limit, courseId, userId, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    if (courseId) params.append(\"courseId\", courseId);\n                    if (userId) params.append(\"userId\", userId);\n                    return {\n                        url: `main-quiz-report?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                },\n                transformResponse: (response)=>({\n                        ...response,\n                        data: {\n                            ...response.data,\n                            reports: response.data.reports || []\n                        }\n                    })\n            }),\n            createReport: builder.mutation({\n                query: ({ courseId, userId, completed, score, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"createnew\",\n                            courseId,\n                            userId,\n                            completed,\n                            score,\n                            accessToken\n                        }\n                    })\n            }),\n            upsertReport: builder.mutation({\n                query: ({ courseId, userId, completed, score, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"upsert\",\n                            courseId,\n                            userId,\n                            completed,\n                            score,\n                            accessToken\n                        }\n                    })\n            }),\n            deleteReport: builder.mutation({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"delete\",\n                            id,\n                            accessToken\n                        }\n                    })\n            }),\n            updateReport: builder.mutation({\n                query: ({ id, courseId, userId, completed, score, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"update\",\n                            id,\n                            courseId,\n                            userId,\n                            completed,\n                            score,\n                            accessToken\n                        }\n                    })\n            }),\n            getSingleReport: builder.query({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"getsingle\",\n                            id,\n                            accessToken\n                        }\n                    })\n            })\n        })\n});\n// Export hooks\nconst { useRetrieveReportQuery, useCreateReportMutation, useUpsertReportMutation, useDeleteReportMutation, useUpdateReportMutation, useGetSingleReportQuery } = reportApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/reportApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/store/store.ts":
/*!*************************************!*\
  !*** ./src/reduxRTK/store/store.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: () => (/* binding */ store)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var _services_authApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services/authApi */ \"(ssr)/./src/reduxRTK/services/authApi.ts\");\n/* harmony import */ var _services_courseApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/courseApi */ \"(ssr)/./src/reduxRTK/services/courseApi.ts\");\n/* harmony import */ var _services_quizApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/quizApi */ \"(ssr)/./src/reduxRTK/services/quizApi.ts\");\n/* harmony import */ var _services_reportApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/reportApi */ \"(ssr)/./src/reduxRTK/services/reportApi.ts\");\n/* harmony import */ var _services_demoApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/demoApi */ \"(ssr)/./src/reduxRTK/services/demoApi.ts\");\n// store/index.ts\n\n\n\n\n\n\n// Create and configure the Redux store\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_5__.configureStore)({\n    reducer: {\n        [_services_authApi__WEBPACK_IMPORTED_MODULE_0__.authApi.reducerPath]: _services_authApi__WEBPACK_IMPORTED_MODULE_0__.authApi.reducer,\n        [_services_courseApi__WEBPACK_IMPORTED_MODULE_1__.courseApi.reducerPath]: _services_courseApi__WEBPACK_IMPORTED_MODULE_1__.courseApi.reducer,\n        [_services_quizApi__WEBPACK_IMPORTED_MODULE_2__.quizApi.reducerPath]: _services_quizApi__WEBPACK_IMPORTED_MODULE_2__.quizApi.reducer,\n        [_services_reportApi__WEBPACK_IMPORTED_MODULE_3__.reportApi.reducerPath]: _services_reportApi__WEBPACK_IMPORTED_MODULE_3__.reportApi.reducer,\n        [_services_demoApi__WEBPACK_IMPORTED_MODULE_4__.demoApi.reducerPath]: _services_demoApi__WEBPACK_IMPORTED_MODULE_4__.demoApi.reducer\n    },\n    middleware: (getDefaultMiddleware)=>getDefaultMiddleware().concat(_services_authApi__WEBPACK_IMPORTED_MODULE_0__.authApi.middleware, _services_courseApi__WEBPACK_IMPORTED_MODULE_1__.courseApi.middleware, _services_quizApi__WEBPACK_IMPORTED_MODULE_2__.quizApi.middleware, _services_reportApi__WEBPACK_IMPORTED_MODULE_3__.reportApi.middleware, _services_demoApi__WEBPACK_IMPORTED_MODULE_4__.demoApi.middleware)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/store/store.ts\n");

/***/ }),

/***/ "(rsc)/./src/css/satoshi.css":
/*!*****************************!*\
  !*** ./src/css/satoshi.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2216757f3ab5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY3NzL3NhdG9zaGkuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8uL3NyYy9jc3Mvc2F0b3NoaS5jc3M/NGI5OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIyMTY3NTdmM2FiNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/css/satoshi.css\n");

/***/ }),

/***/ "(ssr)/./src/css/satoshi.css":
/*!*****************************!*\
  !*** ./src/css/satoshi.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2216757f3ab5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY3NzL3NhdG9zaGkuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8uL3NyYy9jc3Mvc2F0b3NoaS5jc3M/NThlMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIyMTY3NTdmM2FiNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/css/satoshi.css\n");

/***/ }),

/***/ "(rsc)/./src/css/style.css":
/*!***************************!*\
  !*** ./src/css/style.css ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d5f3da26a5e8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY3NzL3N0eWxlLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9zcmMvY3NzL3N0eWxlLmNzcz85YzYzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZDVmM2RhMjZhNWU4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/css/style.css\n");

/***/ }),

/***/ "(ssr)/./src/css/style.css":
/*!***************************!*\
  !*** ./src/css/style.css ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d5f3da26a5e8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY3NzL3N0eWxlLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9zcmMvY3NzL3N0eWxlLmNzcz9hODcwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZDVmM2RhMjZhNWU4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/css/style.css\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/layout.tsx":
/*!*********************************!*\
  !*** ./src/app/auth/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/auth/page.tsx":
/*!*******************************!*\
  !*** ./src/app/auth/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/auth/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsvectormap_dist_jsvectormap_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsvectormap/dist/jsvectormap.css */ \"(rsc)/./node_modules/jsvectormap/dist/jsvectormap.css\");\n/* harmony import */ var flatpickr_dist_flatpickr_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! flatpickr/dist/flatpickr.min.css */ \"(rsc)/./node_modules/flatpickr/dist/flatpickr.min.css\");\n/* harmony import */ var _css_satoshi_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/css/satoshi.css */ \"(rsc)/./src/css/satoshi.css\");\n/* harmony import */ var _css_style_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/css/style.css */ \"(rsc)/./src/css/style.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(rsc)/./node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* harmony import */ var _provider_Provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/provider/Provider */ \"(rsc)/./src/provider/Provider.tsx\");\n\n\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dark:bg-boxdark-2 dark:text-bodydark\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_provider_Provider__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__.AntdRegistry, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQTBDO0FBQ0E7QUFDZjtBQUNGO0FBQ0M7QUFDaUM7QUFDVjtBQUVsQyxTQUFTRyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQ0MsNEVBQUNDO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDUCwwREFBY0E7OEJBQ2IsNEVBQUNELHFFQUFZQTtrQ0FBRUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBcImpzdmVjdG9ybWFwL2Rpc3QvanN2ZWN0b3JtYXAuY3NzXCI7XHJcbmltcG9ydCBcImZsYXRwaWNrci9kaXN0L2ZsYXRwaWNrci5taW4uY3NzXCI7XHJcbmltcG9ydCBcIkAvY3NzL3NhdG9zaGkuY3NzXCI7XHJcbmltcG9ydCBcIkAvY3NzL3N0eWxlLmNzc1wiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IEFudGRSZWdpc3RyeSB9IGZyb20gXCJAYW50LWRlc2lnbi9uZXh0anMtcmVnaXN0cnlcIjtcclxuaW1wb3J0IENsaWVudFByb3ZpZGVyIGZyb20gXCJAL3Byb3ZpZGVyL1Byb3ZpZGVyXCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufTogUmVhZG9ubHk8e1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0+KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRhcms6YmctYm94ZGFyay0yIGRhcms6dGV4dC1ib2R5ZGFya1wiPlxyXG4gICAgICAgICAgPENsaWVudFByb3ZpZGVyPlxyXG4gICAgICAgICAgICA8QW50ZFJlZ2lzdHJ5PntjaGlsZHJlbn08L0FudGRSZWdpc3RyeT5cclxuICAgICAgICAgIDwvQ2xpZW50UHJvdmlkZXI+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkFudGRSZWdpc3RyeSIsIkNsaWVudFByb3ZpZGVyIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/provider/Provider.tsx":
/*!***********************************!*\
  !*** ./src/provider/Provider.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/provider/Provider.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"32x32\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLy4vc3JjL2FwcC9mYXZpY29uLmljbz8xNGE2Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjMyeDMyXCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@ant-design","vendor-chunks/@reduxjs","vendor-chunks/rc-util","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/@babel","vendor-chunks/reselect","vendor-chunks/redux","vendor-chunks/stylis","vendor-chunks/use-sync-external-store","vendor-chunks/@emotion","vendor-chunks/@swc","vendor-chunks/redux-thunk","vendor-chunks/jsvectormap","vendor-chunks/flatpickr","vendor-chunks/antd","vendor-chunks/@rc-component","vendor-chunks/rc-field-form","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-motion","vendor-chunks/rc-notification","vendor-chunks/rc-pagination","vendor-chunks/rc-textarea","vendor-chunks/rc-input","vendor-chunks/rc-collapse","vendor-chunks/rc-resize-observer","vendor-chunks/rc-tooltip","vendor-chunks/throttle-debounce","vendor-chunks/classnames","vendor-chunks/rc-picker","vendor-chunks/scroll-into-view-if-needed","vendor-chunks/compute-scroll-into-view"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.demoRequestController = exports.mainReportManagementController = exports.mainQuizManagementController = exports.mainCourseManagementController = exports.updateUserController = exports.deleteUserController = exports.getUserController = exports.checkAuthController = exports.LoginController = exports.Logout = exports.CreateUserController = exports.sendDetailsController = exports.resetPasswordController = exports.getCoursesByUserController = void 0;
const authController_1 = require("./authController");
Object.defineProperty(exports, "CreateUserController", { enumerable: true, get: function () { return authController_1.CreateUserController; } });
Object.defineProperty(exports, "LoginController", { enumerable: true, get: function () { return authController_1.LoginController; } });
Object.defineProperty(exports, "checkAuthController", { enumerable: true, get: function () { return authController_1.checkAuthController; } });
Object.defineProperty(exports, "deleteUserController", { enumerable: true, get: function () { return authController_1.deleteUserController; } });
Object.defineProperty(exports, "sendDetailsController", { enumerable: true, get: function () { return authController_1.sendDetailsController; } });
Object.defineProperty(exports, "resetPasswordController", { enumerable: true, get: function () { return authController_1.resetPasswordController; } });
const logout_1 = require("./logout");
Object.defineProperty(exports, "Logout", { enumerable: true, get: function () { return logout_1.Logout; } });
const getUserController_1 = require("./getUserController");
Object.defineProperty(exports, "getUserController", { enumerable: true, get: function () { return getUserController_1.getUserController; } });
const updateUserController_1 = require("./updateUserController");
Object.defineProperty(exports, "updateUserController", { enumerable: true, get: function () { return updateUserController_1.updateUserController; } });
const courseController_1 = require("./courseController");
Object.defineProperty(exports, "mainCourseManagementController", { enumerable: true, get: function () { return courseController_1.mainCourseManagementController; } });
const quizController_1 = require("./quizController");
Object.defineProperty(exports, "mainQuizManagementController", { enumerable: true, get: function () { return quizController_1.mainQuizManagementController; } });
const reportController_1 = require("./reportController");
Object.defineProperty(exports, "mainReportManagementController", { enumerable: true, get: function () { return reportController_1.mainReportManagementController; } });
const getCoursesByUserController_1 = require("./getCoursesByUserController");
Object.defineProperty(exports, "getCoursesByUserController", { enumerable: true, get: function () { return getCoursesByUserController_1.getCoursesByUserController; } });
const demoController_1 = require("./demoController");
Object.defineProperty(exports, "demoRequestController", { enumerable: true, get: function () { return demoController_1.demoRequestController; } });

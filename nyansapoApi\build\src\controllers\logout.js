"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logout = void 0;
const Logout = (req, res) => {
    try {
        // Log the incoming request (useful for debugging)
        console.log("Logout request received:", req.cookies);
        // Attempt to clear the session cookie
        res.clearCookie('accessToken');
        res.clearCookie('refreshToken');
        console.log('Cookie cleared');
        // Send a success response if the logout process is successful
        return res.status(200).json({
            success: true,
            message: 'Logged out successfully',
        });
    }
    catch (error) {
        // Log the error if something goes wrong
        console.error('Logout error:', error);
        return res.status(500).json({
            success: false,
            message: 'An error occurred while logging out',
        });
    }
};
exports.Logout = Logout;

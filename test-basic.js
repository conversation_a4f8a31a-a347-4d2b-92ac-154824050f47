// Test basic connectivity
async function testBasic() {
  try {
    console.log('Testing basic connectivity...');
    const response = await fetch('http://localhost:5051/');
    const result = await response.text();
    console.log('Response Status:', response.status);
    console.log('Response Body:', result);
  } catch (error) {
    console.error('❌ Network error:', error.message);
  }
}

testBasic();

import {createUser, loginUser, getUsers, deleteUser, updateUser, sendDetails, getUserDetailsFromToken} from './auth'
import { addCourse, getAllCourses, updateCourse, deleteCourse, getCoursesByUserId, getSingleCourseWithQuiz, assignCourseToUsers, unassignCourseFromUsers, getCourseAssignments, getUserAssignments, getAdminAnalytics } from './course'
import {addQuiz, getQuizzes, updateQuiz, deleteQuiz} from './quiz'
import {addReport,deleteReport,getReports,updateReport, getReportsByUserId, upsertReport, deleteReportByUserAndCourse} from "./report"
import { sendDemoRequest } from './demoService'

export {
    getUserDetailsFromToken,
    sendDetails,
    createUser,
    loginUser,
    getUsers,
    deleteUser,
    updateUser,
    addCourse,
    getAllCourses,
    updateCourse,
    deleteCourse,
    addQuiz,
    getQuizzes,
    updateQuiz,
    deleteQuiz,
    getCoursesByUserId,
    getSingleCourseWithQuiz,
    assignCourseToUsers,
    unassignCourseFromUsers,
    getCourseAssignments,
    getUserAssignments,
    getAdminAnalytics,
    addReport,
    deleteReport,
    getReports,
    updateReport,
    getReportsByUserId,
    upsertReport,
    deleteReportByUserAndCourse,
    sendDemoRequest
}
"use client";

import React, { useState, useEffect } from "react";
import {
  Input,
  Button,
  Pagination,
  Spin,
  Alert,
  message,
  Popconfirm,
} from "antd";
import {
  SearchOutlined,
  EditOutlined,
  DeleteFilled,
  PlusOutlined,
  EyeOutlined,
  LeftOutlined,
  RightOutlined,
} from "@ant-design/icons";
import Image from "next/image";
import AddCourse from "./AddCourse";
import EditCourse from "./EditCourse";
import DOMPurify from "dompurify";
import {
  useRetrieveCourseQuery,
  useDeleteCourseMutation,
} from "../../reduxRTK/services/courseApi";

interface CourseData {
  id: string;
  title: string;
  description: string;
  url: string;
  userIds: string[];
  createdAt: string;
  updatedAt: string;
}

const sanitizeHtml = (html: string) => {
  return DOMPurify.sanitize(html);
};

const truncateHtml = (html: string, maxLength: number) => {
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = sanitizeHtml(html);
  const textContent = tempDiv.textContent || tempDiv.innerText || "";

  const truncatedText =
    textContent.length > maxLength
      ? `${textContent.slice(0, maxLength)}...`
      : textContent;

  return DOMPurify.sanitize(truncatedText);
};

const Courses: React.FC = () => {
  const [page, setPage] = useState(1);
  const [pageSize] = useState(10);
  const [isAddingCourse, setIsAddingCourse] = useState(false);
  const [isEditingCourse, setIsEditingCourse] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCourse, setSelectedCourse] = useState<CourseData | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);

  useEffect(() => {
    const token = localStorage.getItem("token");
    setAccessToken(token);
  }, []);

  const { data, isLoading, isError, refetch, isFetching } = useRetrieveCourseQuery(
    { page, limit: pageSize, accessToken: accessToken || "" },
    { refetchOnMountOrArgChange: true, skip: !accessToken } 
  );

  const [deleteCourse] = useDeleteCourseMutation();

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    refetch();
  };

  const renderPaginationButtons = () => {
    const totalPages = Math.ceil(totalCourses / pageSize);
    const visiblePages = 3; // Number of visible page numbers to show dynamically
    const pages: (number | string)[] = [];
  
    if (totalPages <= visiblePages + 2) {
      // If total pages are small, show all pages
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (page > 2) pages.push(1); // Always show first page
  
      const start = Math.max(2, page - Math.floor(visiblePages / 2));
      const end = Math.min(totalPages - 1, page + Math.floor(visiblePages / 2));
  
      if (start > 2) pages.push("..."); // Show leading ellipsis
  
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
  
      if (end < totalPages - 1) pages.push("..."); // Show trailing ellipsis
  
      pages.push(totalPages); // Always show last page
    }
  
    return pages.map((p, index) =>
      p === "..." ? (
        <span key={index} className="mx-2 text-gray-500">
          ...
        </span>
      ) : (
        <Button
          key={p}
          type={p === page ? "primary" : "default"}
          onClick={() => handlePageChange(Number(p))}
        >
          {p}
        </Button>
      )
    );
  };
  
  

  // const handlePaginationChange = (direction: "next" | "prev") => {
  //   if (direction === "next" && page < Math.ceil((data?.data?.total || 0) / pageSize)) {
  //     setPage((prev) => prev + 1);
  //   } else if (direction === "prev" && page > 1) {
  //     setPage((prev) => prev - 1);
  //   }
  //   refetch();
  // };

  const handleAddCourseClick = () => {
    setIsAddingCourse(true);
    setIsEditingCourse(false);
  };

  const handleViewCoursesClick = () => {
    setIsAddingCourse(false);
    setIsEditingCourse(false);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setSearchQuery(e.target.value);

  const handleDeleteCourse = async (courseId: string) => {
    if (!accessToken) {
      message.error("Access token is missing. Please log in again.");
      return;
    }

    try {
      const response = await deleteCourse({
        courseId,
        accessToken,
      }).unwrap();

      if (response.success) {
        message.success(response.message || "Course deleted successfully");
      } else {
        message.error(response.message || "Failed to delete course");
      }

      refetch();
    } catch (error: any) {
      message.error(
        error?.data?.message || "An error occurred while deleting the course"
      );
    }
  };

  const handleEditCourse = (course: CourseData) => {
    setSelectedCourse(course);
    setIsEditingCourse(true);
    setIsAddingCourse(false);
  };

  const handleAddCourseSuccess = () => {
    setIsAddingCourse(false);
    refetch();
  };

  const coursesData = data?.data?.courses || [];

  const filteredData = coursesData.filter(
    (course: { title: string; description: string }) =>
      course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (!accessToken) {
    return (
      <div className="p-4">
        <Alert
          message="Unauthorized"
          description="Access token is missing. Please log in again."
          type="error"
          showIcon
        />
      </div>
    );
  }


  const totalCourses = data?.data?.total ?? 0;

  return (
    <div className="p-4">
      <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-col gap-2 sm:flex-row sm:gap-2">
          <Button
            onClick={handleAddCourseClick}
            icon={<PlusOutlined />}
            type="primary"
            className="w-full sm:w-auto"
          >
            Add Course
          </Button>
          <Button
            onClick={handleViewCoursesClick}
            icon={<EyeOutlined />}
            type="default"
            className="w-full sm:w-auto"
          >
            View Courses
          </Button>
        </div>
        <Input
          placeholder="Search by title or description"
          prefix={<SearchOutlined style={{ color: "rgba(0,0,0,.25)" }} />}
          className="w-full sm:w-72"
          value={searchQuery}
          onChange={handleSearchChange}
        />
      </div>

      {isLoading || isFetching ? (
        <div className="flex w-full items-center justify-center">
          <Spin size="large" tip="Loading data..." />
        </div>
      ) : isError ? (
        <Alert
          message="Error"
          description="There was an issue fetching the data."
          type="error"
          showIcon
        />
      ) : isAddingCourse ? (
        <AddCourse onSuccess={handleAddCourseSuccess} />
      ) : isEditingCourse && selectedCourse ? (
        <EditCourse course={selectedCourse} onSuccess={handleAddCourseSuccess} />
      ) : (
        <div>
          <div className="grid grid-cols-1 gap-4">
            <div className="border-gray-300 overflow-hidden rounded-lg border">
              <div className="overflow-x-auto" style={{ overflowY: "auto" }}>
                <table className="min-w-full table-auto border-collapse">
                  <thead className="bg-gray-200 border-gray-300 border-b">
                    <tr>
                      <th className="text-gray-700 border-gray-300 border-r px-4 py-2 text-left text-sm font-semibold">
                        Image
                      </th>
                      <th className="text-gray-700 border-gray-300 border-r px-4 py-2 text-left text-sm font-semibold">
                        Title
                      </th>
                      <th className="text-gray-700 border-gray-300 border-r px-4 py-2 text-left text-sm font-semibold">
                        Description
                      </th>
                      <th className="text-gray-700 border-gray-300 border-r px-4 py-2 text-left text-sm font-semibold">
                        Created At
                      </th>
                      <th className="text-gray-700 px-4 py-2 text-left text-sm font-semibold">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredData.length > 0 ? (
                      filteredData.map((course, index) => (
                        <tr
                          key={course.id}
                          className={index % 2 === 0 ? "bg-gray-100" : "bg-white"}
                        >
                          <td className="border-gray-300 border-r px-4 py-2">
                            <div className="flex h-12 w-12 items-center justify-center overflow-hidden rounded-full">
                              <Image
                                src={course.url}
                                alt="Course Image"
                                width={25}
                                height={25}
                                style={{ objectFit: "cover" }}
                                placeholder="blur"
                                blurDataURL="/placeholder-image.png"
                              />
                            </div>
                          </td>
                          <td className="border-gray-300 border-r px-4 py-2">
                            {course.title}
                          </td>
                          <td
                            className="border-gray-300 border-r px-4 py-2"
                            dangerouslySetInnerHTML={{
                              __html: truncateHtml(course.description, 90),
                            }}
                          />
                          <td className="border-gray-300 border-r px-4 py-2">
                            {new Date(course.createdAt).toLocaleDateString()}
                          </td>
                          <td className="flex gap-2 px-4 py-2">
                            <Button
                              icon={<EditOutlined />}
                              type="link"
                              onClick={() => handleEditCourse(course)}
                            />
                            <Popconfirm
                              title="Are you sure you want to delete this course?"
                              onConfirm={() => handleDeleteCourse(course.id)}
                              okText="Yes"
                              cancelText="No"
                            >
                              <Button
                                icon={<DeleteFilled />}
                                type="link"
                                danger
                              />
                            </Popconfirm>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={5}
                          className="text-gray-500 px-4 py-2 text-center"
                        >
                          No courses found.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {totalCourses > 0 && (
              <div className="mt-4 flex items-center justify-center gap-2">
              <Button
                type="default"
                disabled={page <= 1}
                onClick={() => handlePageChange(page - 1)}
                icon={<LeftOutlined />}
              />
              {renderPaginationButtons()}
              <Button
                type="default"
                disabled={page >= Math.ceil(totalCourses / pageSize)}
                onClick={() => handlePageChange(page + 1)}
                icon={<RightOutlined />}
              />
            </div>
            
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Courses;

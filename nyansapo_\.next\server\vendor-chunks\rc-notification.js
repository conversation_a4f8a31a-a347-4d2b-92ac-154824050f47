"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-notification";
exports.ids = ["vendor-chunks/rc-notification"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-notification/es/Notice.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-notification/es/Notice.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/pickAttrs */ \"(ssr)/./node_modules/rc-util/es/pickAttrs.js\");\n\n\n\n\n\n\n\n\nvar Notify = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    className = props.className,\n    _props$duration = props.duration,\n    duration = _props$duration === void 0 ? 4.5 : _props$duration,\n    showProgress = props.showProgress,\n    _props$pauseOnHover = props.pauseOnHover,\n    pauseOnHover = _props$pauseOnHover === void 0 ? true : _props$pauseOnHover,\n    eventKey = props.eventKey,\n    content = props.content,\n    closable = props.closable,\n    _props$closeIcon = props.closeIcon,\n    closeIcon = _props$closeIcon === void 0 ? 'x' : _props$closeIcon,\n    divProps = props.props,\n    onClick = props.onClick,\n    onNoticeClose = props.onNoticeClose,\n    times = props.times,\n    forcedHovering = props.hovering;\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_6__.useState(false),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState, 2),\n    hovering = _React$useState2[0],\n    setHovering = _React$useState2[1];\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_6__.useState(0),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState3, 2),\n    percent = _React$useState4[0],\n    setPercent = _React$useState4[1];\n  var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_6__.useState(0),\n    _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState5, 2),\n    spentTime = _React$useState6[0],\n    setSpentTime = _React$useState6[1];\n  var mergedHovering = forcedHovering || hovering;\n  var mergedShowProgress = duration > 0 && showProgress;\n\n  // ======================== Close =========================\n  var onInternalClose = function onInternalClose() {\n    onNoticeClose(eventKey);\n  };\n  var onCloseKeyDown = function onCloseKeyDown(e) {\n    if (e.key === 'Enter' || e.code === 'Enter' || e.keyCode === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_5__[\"default\"].ENTER) {\n      onInternalClose();\n    }\n  };\n\n  // ======================== Effect ========================\n  react__WEBPACK_IMPORTED_MODULE_6__.useEffect(function () {\n    if (!mergedHovering && duration > 0) {\n      var start = Date.now() - spentTime;\n      var timeout = setTimeout(function () {\n        onInternalClose();\n      }, duration * 1000 - spentTime);\n      return function () {\n        if (pauseOnHover) {\n          clearTimeout(timeout);\n        }\n        setSpentTime(Date.now() - start);\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [duration, mergedHovering, times]);\n  react__WEBPACK_IMPORTED_MODULE_6__.useEffect(function () {\n    if (!mergedHovering && mergedShowProgress && (pauseOnHover || spentTime === 0)) {\n      var start = performance.now();\n      var animationFrame;\n      var calculate = function calculate() {\n        cancelAnimationFrame(animationFrame);\n        animationFrame = requestAnimationFrame(function (timestamp) {\n          var runtime = timestamp + spentTime - start;\n          var progress = Math.min(runtime / (duration * 1000), 1);\n          setPercent(progress * 100);\n          if (progress < 1) {\n            calculate();\n          }\n        });\n      };\n      calculate();\n      return function () {\n        if (pauseOnHover) {\n          cancelAnimationFrame(animationFrame);\n        }\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [duration, spentTime, mergedHovering, mergedShowProgress, times]);\n\n  // ======================== Closable ========================\n  var closableObj = react__WEBPACK_IMPORTED_MODULE_6__.useMemo(function () {\n    if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(closable) === 'object' && closable !== null) {\n      return closable;\n    }\n    if (closable) {\n      return {\n        closeIcon: closeIcon\n      };\n    }\n    return {};\n  }, [closable, closeIcon]);\n  var ariaProps = (0,rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(closableObj, true);\n\n  // ======================== Progress ========================\n  var validPercent = 100 - (!percent || percent < 0 ? 0 : percent > 100 ? 100 : percent);\n\n  // ======================== Render ========================\n  var noticePrefixCls = \"\".concat(prefixCls, \"-notice\");\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, divProps, {\n    ref: ref,\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(noticePrefixCls, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(noticePrefixCls, \"-closable\"), closable)),\n    style: style,\n    onMouseEnter: function onMouseEnter(e) {\n      var _divProps$onMouseEnte;\n      setHovering(true);\n      divProps === null || divProps === void 0 || (_divProps$onMouseEnte = divProps.onMouseEnter) === null || _divProps$onMouseEnte === void 0 || _divProps$onMouseEnte.call(divProps, e);\n    },\n    onMouseLeave: function onMouseLeave(e) {\n      var _divProps$onMouseLeav;\n      setHovering(false);\n      divProps === null || divProps === void 0 || (_divProps$onMouseLeav = divProps.onMouseLeave) === null || _divProps$onMouseLeav === void 0 || _divProps$onMouseLeav.call(divProps, e);\n    },\n    onClick: onClick\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", {\n    className: \"\".concat(noticePrefixCls, \"-content\")\n  }, content), closable && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"a\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    tabIndex: 0,\n    className: \"\".concat(noticePrefixCls, \"-close\"),\n    onKeyDown: onCloseKeyDown,\n    \"aria-label\": \"Close\"\n  }, ariaProps, {\n    onClick: function onClick(e) {\n      e.preventDefault();\n      e.stopPropagation();\n      onInternalClose();\n    }\n  }), closableObj.closeIcon), mergedShowProgress && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"progress\", {\n    className: \"\".concat(noticePrefixCls, \"-progress\"),\n    max: \"100\",\n    value: validPercent\n  }, validPercent + '%'));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Notify);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/Notice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/NoticeList.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-notification/es/NoticeList.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var rc_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-motion */ \"(ssr)/./node_modules/rc-motion/es/index.js\");\n/* harmony import */ var _Notice__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Notice */ \"(ssr)/./node_modules/rc-notification/es/Notice.js\");\n/* harmony import */ var _NotificationProvider__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./NotificationProvider */ \"(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\");\n/* harmony import */ var _hooks_useStack__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useStack */ \"(ssr)/./node_modules/rc-notification/es/hooks/useStack.js\");\n\n\n\n\n\n\nvar _excluded = [\"className\", \"style\", \"classNames\", \"styles\"];\n\n\n\n\n\n\nvar NoticeList = function NoticeList(props) {\n  var configList = props.configList,\n    placement = props.placement,\n    prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    motion = props.motion,\n    onAllNoticeRemoved = props.onAllNoticeRemoved,\n    onNoticeClose = props.onNoticeClose,\n    stackConfig = props.stack;\n  var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_6__.useContext)(_NotificationProvider__WEBPACK_IMPORTED_MODULE_10__.NotificationContext),\n    ctxCls = _useContext.classNames;\n  var dictRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)({});\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useState, 2),\n    latestNotice = _useState2[0],\n    setLatestNotice = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useState3, 2),\n    hoverKeys = _useState4[0],\n    setHoverKeys = _useState4[1];\n  var keys = configList.map(function (config) {\n    return {\n      config: config,\n      key: String(config.key)\n    };\n  });\n  var _useStack = (0,_hooks_useStack__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(stackConfig),\n    _useStack2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useStack, 2),\n    stack = _useStack2[0],\n    _useStack2$ = _useStack2[1],\n    offset = _useStack2$.offset,\n    threshold = _useStack2$.threshold,\n    gap = _useStack2$.gap;\n  var expanded = stack && (hoverKeys.length > 0 || keys.length <= threshold);\n  var placementMotion = typeof motion === 'function' ? motion(placement) : motion;\n\n  // Clean hover key\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    if (stack && hoverKeys.length > 1) {\n      setHoverKeys(function (prev) {\n        return prev.filter(function (key) {\n          return keys.some(function (_ref) {\n            var dataKey = _ref.key;\n            return key === dataKey;\n          });\n        });\n      });\n    }\n  }, [hoverKeys, keys, stack]);\n\n  // Force update latest notice\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    var _keys;\n    if (stack && dictRef.current[(_keys = keys[keys.length - 1]) === null || _keys === void 0 ? void 0 : _keys.key]) {\n      var _keys2;\n      setLatestNotice(dictRef.current[(_keys2 = keys[keys.length - 1]) === null || _keys2 === void 0 ? void 0 : _keys2.key]);\n    }\n  }, [keys, stack]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6___default().createElement(rc_motion__WEBPACK_IMPORTED_MODULE_8__.CSSMotionList, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    key: placement,\n    className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.list, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({}, \"\".concat(prefixCls, \"-stack\"), !!stack), \"\".concat(prefixCls, \"-stack-expanded\"), expanded)),\n    style: style,\n    keys: keys,\n    motionAppear: true\n  }, placementMotion, {\n    onAllRemoved: function onAllRemoved() {\n      onAllNoticeRemoved(placement);\n    }\n  }), function (_ref2, nodeRef) {\n    var config = _ref2.config,\n      motionClassName = _ref2.className,\n      motionStyle = _ref2.style,\n      motionIndex = _ref2.index;\n    var _ref3 = config,\n      key = _ref3.key,\n      times = _ref3.times;\n    var strKey = String(key);\n    var _ref4 = config,\n      configClassName = _ref4.className,\n      configStyle = _ref4.style,\n      configClassNames = _ref4.classNames,\n      configStyles = _ref4.styles,\n      restConfig = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref4, _excluded);\n    var dataIndex = keys.findIndex(function (item) {\n      return item.key === strKey;\n    });\n\n    // If dataIndex is -1, that means this notice has been removed in data, but still in dom\n    // Should minus (motionIndex - 1) to get the correct index because keys.length is not the same as dom length\n    var stackStyle = {};\n    if (stack) {\n      var index = keys.length - 1 - (dataIndex > -1 ? dataIndex : motionIndex - 1);\n      var transformX = placement === 'top' || placement === 'bottom' ? '-50%' : '0';\n      if (index > 0) {\n        var _dictRef$current$strK, _dictRef$current$strK2, _dictRef$current$strK3;\n        stackStyle.height = expanded ? (_dictRef$current$strK = dictRef.current[strKey]) === null || _dictRef$current$strK === void 0 ? void 0 : _dictRef$current$strK.offsetHeight : latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetHeight;\n\n        // Transform\n        var verticalOffset = 0;\n        for (var i = 0; i < index; i++) {\n          var _dictRef$current$keys;\n          verticalOffset += ((_dictRef$current$keys = dictRef.current[keys[keys.length - 1 - i].key]) === null || _dictRef$current$keys === void 0 ? void 0 : _dictRef$current$keys.offsetHeight) + gap;\n        }\n        var transformY = (expanded ? verticalOffset : index * offset) * (placement.startsWith('top') ? 1 : -1);\n        var scaleX = !expanded && latestNotice !== null && latestNotice !== void 0 && latestNotice.offsetWidth && (_dictRef$current$strK2 = dictRef.current[strKey]) !== null && _dictRef$current$strK2 !== void 0 && _dictRef$current$strK2.offsetWidth ? ((latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetWidth) - offset * 2 * (index < 3 ? index : 3)) / ((_dictRef$current$strK3 = dictRef.current[strKey]) === null || _dictRef$current$strK3 === void 0 ? void 0 : _dictRef$current$strK3.offsetWidth) : 1;\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", \").concat(transformY, \"px, 0) scaleX(\").concat(scaleX, \")\");\n      } else {\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", 0, 0)\");\n      }\n    }\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6___default().createElement(\"div\", {\n      ref: nodeRef,\n      className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(\"\".concat(prefixCls, \"-notice-wrapper\"), motionClassName, configClassNames === null || configClassNames === void 0 ? void 0 : configClassNames.wrapper),\n      style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, motionStyle), stackStyle), configStyles === null || configStyles === void 0 ? void 0 : configStyles.wrapper),\n      onMouseEnter: function onMouseEnter() {\n        return setHoverKeys(function (prev) {\n          return prev.includes(strKey) ? prev : [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(prev), [strKey]);\n        });\n      },\n      onMouseLeave: function onMouseLeave() {\n        return setHoverKeys(function (prev) {\n          return prev.filter(function (k) {\n            return k !== strKey;\n          });\n        });\n      }\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6___default().createElement(_Notice__WEBPACK_IMPORTED_MODULE_9__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restConfig, {\n      ref: function ref(node) {\n        if (dataIndex > -1) {\n          dictRef.current[strKey] = node;\n        } else {\n          delete dictRef.current[strKey];\n        }\n      },\n      prefixCls: prefixCls,\n      classNames: configClassNames,\n      styles: configStyles,\n      className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(configClassName, ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.notice),\n      style: configStyle,\n      times: times,\n      key: key,\n      eventKey: key,\n      onNoticeClose: onNoticeClose,\n      hovering: stack && hoverKeys.length > 0\n    })));\n  });\n};\nif (true) {\n  NoticeList.displayName = 'NoticeList';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NoticeList);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/NoticeList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/NotificationProvider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/rc-notification/es/NotificationProvider.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationContext: () => (/* binding */ NotificationContext),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar NotificationContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext({});\nvar NotificationProvider = function NotificationProvider(_ref) {\n  var children = _ref.children,\n    classNames = _ref.classNames;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NotificationContext.Provider, {\n    value: {\n      classNames: classNames\n    }\n  }, children);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationProvider);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL05vdGlmaWNhdGlvblByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFDbkIsdUNBQXVDLDBEQUFtQixHQUFHO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBbUI7QUFDekM7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsaUVBQWUsb0JBQW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8uL25vZGVfbW9kdWxlcy9yYy1ub3RpZmljYXRpb24vZXMvTm90aWZpY2F0aW9uUHJvdmlkZXIuanM/ZTk4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuZXhwb3J0IHZhciBOb3RpZmljYXRpb25Db250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQoe30pO1xudmFyIE5vdGlmaWNhdGlvblByb3ZpZGVyID0gZnVuY3Rpb24gTm90aWZpY2F0aW9uUHJvdmlkZXIoX3JlZikge1xuICB2YXIgY2hpbGRyZW4gPSBfcmVmLmNoaWxkcmVuLFxuICAgIGNsYXNzTmFtZXMgPSBfcmVmLmNsYXNzTmFtZXM7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChOb3RpZmljYXRpb25Db250ZXh0LlByb3ZpZGVyLCB7XG4gICAgdmFsdWU6IHtcbiAgICAgIGNsYXNzTmFtZXM6IGNsYXNzTmFtZXNcbiAgICB9XG4gIH0sIGNoaWxkcmVuKTtcbn07XG5leHBvcnQgZGVmYXVsdCBOb3RpZmljYXRpb25Qcm92aWRlcjsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/Notifications.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-notification/es/Notifications.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _NoticeList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoticeList */ \"(ssr)/./node_modules/rc-notification/es/NoticeList.js\");\n\n\n\n\n\n\n// ant-notification ant-notification-topRight\nvar Notifications = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-notification' : _props$prefixCls,\n    container = props.container,\n    motion = props.motion,\n    maxCount = props.maxCount,\n    className = props.className,\n    style = props.style,\n    onAllRemoved = props.onAllRemoved,\n    stack = props.stack,\n    renderNotifications = props.renderNotifications;\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_3__.useState([]),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState, 2),\n    configList = _React$useState2[0],\n    setConfigList = _React$useState2[1];\n\n  // ======================== Close =========================\n  var onNoticeClose = function onNoticeClose(key) {\n    var _config$onClose;\n    // Trigger close event\n    var config = configList.find(function (item) {\n      return item.key === key;\n    });\n    config === null || config === void 0 || (_config$onClose = config.onClose) === null || _config$onClose === void 0 || _config$onClose.call(config);\n    setConfigList(function (list) {\n      return list.filter(function (item) {\n        return item.key !== key;\n      });\n    });\n  };\n\n  // ========================= Refs =========================\n  react__WEBPACK_IMPORTED_MODULE_3__.useImperativeHandle(ref, function () {\n    return {\n      open: function open(config) {\n        setConfigList(function (list) {\n          var clone = (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(list);\n\n          // Replace if exist\n          var index = clone.findIndex(function (item) {\n            return item.key === config.key;\n          });\n          var innerConfig = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, config);\n          if (index >= 0) {\n            var _list$index;\n            innerConfig.times = (((_list$index = list[index]) === null || _list$index === void 0 ? void 0 : _list$index.times) || 0) + 1;\n            clone[index] = innerConfig;\n          } else {\n            innerConfig.times = 0;\n            clone.push(innerConfig);\n          }\n          if (maxCount > 0 && clone.length > maxCount) {\n            clone = clone.slice(-maxCount);\n          }\n          return clone;\n        });\n      },\n      close: function close(key) {\n        onNoticeClose(key);\n      },\n      destroy: function destroy() {\n        setConfigList([]);\n      }\n    };\n  });\n\n  // ====================== Placements ======================\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_3__.useState({}),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState3, 2),\n    placements = _React$useState4[0],\n    setPlacements = _React$useState4[1];\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    var nextPlacements = {};\n    configList.forEach(function (config) {\n      var _config$placement = config.placement,\n        placement = _config$placement === void 0 ? 'topRight' : _config$placement;\n      if (placement) {\n        nextPlacements[placement] = nextPlacements[placement] || [];\n        nextPlacements[placement].push(config);\n      }\n    });\n\n    // Fill exist placements to avoid empty list causing remove without motion\n    Object.keys(placements).forEach(function (placement) {\n      nextPlacements[placement] = nextPlacements[placement] || [];\n    });\n    setPlacements(nextPlacements);\n  }, [configList]);\n\n  // Clean up container if all notices fade out\n  var onAllNoticeRemoved = function onAllNoticeRemoved(placement) {\n    setPlacements(function (originPlacements) {\n      var clone = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, originPlacements);\n      var list = clone[placement] || [];\n      if (!list.length) {\n        delete clone[placement];\n      }\n      return clone;\n    });\n  };\n\n  // Effect tell that placements is empty now\n  var emptyRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(false);\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    if (Object.keys(placements).length > 0) {\n      emptyRef.current = true;\n    } else if (emptyRef.current) {\n      // Trigger only when from exist to empty\n      onAllRemoved === null || onAllRemoved === void 0 || onAllRemoved();\n      emptyRef.current = false;\n    }\n  }, [placements]);\n  // ======================== Render ========================\n  if (!container) {\n    return null;\n  }\n  var placementList = Object.keys(placements);\n  return /*#__PURE__*/(0,react_dom__WEBPACK_IMPORTED_MODULE_4__.createPortal)( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, null, placementList.map(function (placement) {\n    var placementConfigList = placements[placement];\n    var list = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_NoticeList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n      key: placement,\n      configList: placementConfigList,\n      placement: placement,\n      prefixCls: prefixCls,\n      className: className === null || className === void 0 ? void 0 : className(placement),\n      style: style === null || style === void 0 ? void 0 : style(placement),\n      motion: motion,\n      onNoticeClose: onNoticeClose,\n      onAllNoticeRemoved: onAllNoticeRemoved,\n      stack: stack\n    });\n    return renderNotifications ? renderNotifications(list, {\n      prefixCls: prefixCls,\n      key: placement\n    }) : list;\n  })), container);\n});\nif (true) {\n  Notifications.displayName = 'Notifications';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Notifications);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL05vdGlmaWNhdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXFFO0FBQ1M7QUFDUjtBQUN2QztBQUNVO0FBQ0g7QUFDdEM7QUFDQSxpQ0FBaUMsNkNBQWdCO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLDJDQUFjO0FBQ3RDLHVCQUF1QixvRkFBYztBQUNyQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7O0FBRUE7QUFDQSxFQUFFLHNEQUF5QjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxzQkFBc0Isd0ZBQWtCOztBQUV4QztBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsNEJBQTRCLG9GQUFhLEdBQUc7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EseUJBQXlCLDJDQUFjLEdBQUc7QUFDMUMsdUJBQXVCLG9GQUFjO0FBQ3JDO0FBQ0E7QUFDQSxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLG9GQUFhLEdBQUc7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBLGlCQUFpQix5Q0FBWTtBQUM3QixFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQix1REFBWSxlQUFlLGdEQUFtQixDQUFDLDJDQUFjO0FBQ25GO0FBQ0EsNEJBQTRCLGdEQUFtQixDQUFDLG1EQUFVO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0gsQ0FBQztBQUNELElBQUksSUFBcUM7QUFDekM7QUFDQTtBQUNBLGlFQUFlLGFBQWEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLy4vbm9kZV9tb2R1bGVzL3JjLW5vdGlmaWNhdGlvbi9lcy9Ob3RpZmljYXRpb25zLmpzPzdkMzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbmltcG9ydCBfdG9Db25zdW1hYmxlQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5XCI7XG5pbXBvcnQgX3NsaWNlZFRvQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXlcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGNyZWF0ZVBvcnRhbCB9IGZyb20gJ3JlYWN0LWRvbSc7XG5pbXBvcnQgTm90aWNlTGlzdCBmcm9tIFwiLi9Ob3RpY2VMaXN0XCI7XG4vLyBhbnQtbm90aWZpY2F0aW9uIGFudC1ub3RpZmljYXRpb24tdG9wUmlnaHRcbnZhciBOb3RpZmljYXRpb25zID0gLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoZnVuY3Rpb24gKHByb3BzLCByZWYpIHtcbiAgdmFyIF9wcm9wcyRwcmVmaXhDbHMgPSBwcm9wcy5wcmVmaXhDbHMsXG4gICAgcHJlZml4Q2xzID0gX3Byb3BzJHByZWZpeENscyA9PT0gdm9pZCAwID8gJ3JjLW5vdGlmaWNhdGlvbicgOiBfcHJvcHMkcHJlZml4Q2xzLFxuICAgIGNvbnRhaW5lciA9IHByb3BzLmNvbnRhaW5lcixcbiAgICBtb3Rpb24gPSBwcm9wcy5tb3Rpb24sXG4gICAgbWF4Q291bnQgPSBwcm9wcy5tYXhDb3VudCxcbiAgICBjbGFzc05hbWUgPSBwcm9wcy5jbGFzc05hbWUsXG4gICAgc3R5bGUgPSBwcm9wcy5zdHlsZSxcbiAgICBvbkFsbFJlbW92ZWQgPSBwcm9wcy5vbkFsbFJlbW92ZWQsXG4gICAgc3RhY2sgPSBwcm9wcy5zdGFjayxcbiAgICByZW5kZXJOb3RpZmljYXRpb25zID0gcHJvcHMucmVuZGVyTm90aWZpY2F0aW9ucztcbiAgdmFyIF9SZWFjdCR1c2VTdGF0ZSA9IFJlYWN0LnVzZVN0YXRlKFtdKSxcbiAgICBfUmVhY3QkdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX1JlYWN0JHVzZVN0YXRlLCAyKSxcbiAgICBjb25maWdMaXN0ID0gX1JlYWN0JHVzZVN0YXRlMlswXSxcbiAgICBzZXRDb25maWdMaXN0ID0gX1JlYWN0JHVzZVN0YXRlMlsxXTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT0gQ2xvc2UgPT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgb25Ob3RpY2VDbG9zZSA9IGZ1bmN0aW9uIG9uTm90aWNlQ2xvc2Uoa2V5KSB7XG4gICAgdmFyIF9jb25maWckb25DbG9zZTtcbiAgICAvLyBUcmlnZ2VyIGNsb3NlIGV2ZW50XG4gICAgdmFyIGNvbmZpZyA9IGNvbmZpZ0xpc3QuZmluZChmdW5jdGlvbiAoaXRlbSkge1xuICAgICAgcmV0dXJuIGl0ZW0ua2V5ID09PSBrZXk7XG4gICAgfSk7XG4gICAgY29uZmlnID09PSBudWxsIHx8IGNvbmZpZyA9PT0gdm9pZCAwIHx8IChfY29uZmlnJG9uQ2xvc2UgPSBjb25maWcub25DbG9zZSkgPT09IG51bGwgfHwgX2NvbmZpZyRvbkNsb3NlID09PSB2b2lkIDAgfHwgX2NvbmZpZyRvbkNsb3NlLmNhbGwoY29uZmlnKTtcbiAgICBzZXRDb25maWdMaXN0KGZ1bmN0aW9uIChsaXN0KSB7XG4gICAgICByZXR1cm4gbGlzdC5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgICAgcmV0dXJuIGl0ZW0ua2V5ICE9PSBrZXk7XG4gICAgICB9KTtcbiAgICB9KTtcbiAgfTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09IFJlZnMgPT09PT09PT09PT09PT09PT09PT09PT09PVxuICBSZWFjdC51c2VJbXBlcmF0aXZlSGFuZGxlKHJlZiwgZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiB7XG4gICAgICBvcGVuOiBmdW5jdGlvbiBvcGVuKGNvbmZpZykge1xuICAgICAgICBzZXRDb25maWdMaXN0KGZ1bmN0aW9uIChsaXN0KSB7XG4gICAgICAgICAgdmFyIGNsb25lID0gX3RvQ29uc3VtYWJsZUFycmF5KGxpc3QpO1xuXG4gICAgICAgICAgLy8gUmVwbGFjZSBpZiBleGlzdFxuICAgICAgICAgIHZhciBpbmRleCA9IGNsb25lLmZpbmRJbmRleChmdW5jdGlvbiAoaXRlbSkge1xuICAgICAgICAgICAgcmV0dXJuIGl0ZW0ua2V5ID09PSBjb25maWcua2V5O1xuICAgICAgICAgIH0pO1xuICAgICAgICAgIHZhciBpbm5lckNvbmZpZyA9IF9vYmplY3RTcHJlYWQoe30sIGNvbmZpZyk7XG4gICAgICAgICAgaWYgKGluZGV4ID49IDApIHtcbiAgICAgICAgICAgIHZhciBfbGlzdCRpbmRleDtcbiAgICAgICAgICAgIGlubmVyQ29uZmlnLnRpbWVzID0gKCgoX2xpc3QkaW5kZXggPSBsaXN0W2luZGV4XSkgPT09IG51bGwgfHwgX2xpc3QkaW5kZXggPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9saXN0JGluZGV4LnRpbWVzKSB8fCAwKSArIDE7XG4gICAgICAgICAgICBjbG9uZVtpbmRleF0gPSBpbm5lckNvbmZpZztcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgaW5uZXJDb25maWcudGltZXMgPSAwO1xuICAgICAgICAgICAgY2xvbmUucHVzaChpbm5lckNvbmZpZyk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmIChtYXhDb3VudCA+IDAgJiYgY2xvbmUubGVuZ3RoID4gbWF4Q291bnQpIHtcbiAgICAgICAgICAgIGNsb25lID0gY2xvbmUuc2xpY2UoLW1heENvdW50KTtcbiAgICAgICAgICB9XG4gICAgICAgICAgcmV0dXJuIGNsb25lO1xuICAgICAgICB9KTtcbiAgICAgIH0sXG4gICAgICBjbG9zZTogZnVuY3Rpb24gY2xvc2Uoa2V5KSB7XG4gICAgICAgIG9uTm90aWNlQ2xvc2Uoa2V5KTtcbiAgICAgIH0sXG4gICAgICBkZXN0cm95OiBmdW5jdGlvbiBkZXN0cm95KCkge1xuICAgICAgICBzZXRDb25maWdMaXN0KFtdKTtcbiAgICAgIH1cbiAgICB9O1xuICB9KTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09IFBsYWNlbWVudHMgPT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgX1JlYWN0JHVzZVN0YXRlMyA9IFJlYWN0LnVzZVN0YXRlKHt9KSxcbiAgICBfUmVhY3QkdXNlU3RhdGU0ID0gX3NsaWNlZFRvQXJyYXkoX1JlYWN0JHVzZVN0YXRlMywgMiksXG4gICAgcGxhY2VtZW50cyA9IF9SZWFjdCR1c2VTdGF0ZTRbMF0sXG4gICAgc2V0UGxhY2VtZW50cyA9IF9SZWFjdCR1c2VTdGF0ZTRbMV07XG4gIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgdmFyIG5leHRQbGFjZW1lbnRzID0ge307XG4gICAgY29uZmlnTGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChjb25maWcpIHtcbiAgICAgIHZhciBfY29uZmlnJHBsYWNlbWVudCA9IGNvbmZpZy5wbGFjZW1lbnQsXG4gICAgICAgIHBsYWNlbWVudCA9IF9jb25maWckcGxhY2VtZW50ID09PSB2b2lkIDAgPyAndG9wUmlnaHQnIDogX2NvbmZpZyRwbGFjZW1lbnQ7XG4gICAgICBpZiAocGxhY2VtZW50KSB7XG4gICAgICAgIG5leHRQbGFjZW1lbnRzW3BsYWNlbWVudF0gPSBuZXh0UGxhY2VtZW50c1twbGFjZW1lbnRdIHx8IFtdO1xuICAgICAgICBuZXh0UGxhY2VtZW50c1twbGFjZW1lbnRdLnB1c2goY29uZmlnKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIEZpbGwgZXhpc3QgcGxhY2VtZW50cyB0byBhdm9pZCBlbXB0eSBsaXN0IGNhdXNpbmcgcmVtb3ZlIHdpdGhvdXQgbW90aW9uXG4gICAgT2JqZWN0LmtleXMocGxhY2VtZW50cykuZm9yRWFjaChmdW5jdGlvbiAocGxhY2VtZW50KSB7XG4gICAgICBuZXh0UGxhY2VtZW50c1twbGFjZW1lbnRdID0gbmV4dFBsYWNlbWVudHNbcGxhY2VtZW50XSB8fCBbXTtcbiAgICB9KTtcbiAgICBzZXRQbGFjZW1lbnRzKG5leHRQbGFjZW1lbnRzKTtcbiAgfSwgW2NvbmZpZ0xpc3RdKTtcblxuICAvLyBDbGVhbiB1cCBjb250YWluZXIgaWYgYWxsIG5vdGljZXMgZmFkZSBvdXRcbiAgdmFyIG9uQWxsTm90aWNlUmVtb3ZlZCA9IGZ1bmN0aW9uIG9uQWxsTm90aWNlUmVtb3ZlZChwbGFjZW1lbnQpIHtcbiAgICBzZXRQbGFjZW1lbnRzKGZ1bmN0aW9uIChvcmlnaW5QbGFjZW1lbnRzKSB7XG4gICAgICB2YXIgY2xvbmUgPSBfb2JqZWN0U3ByZWFkKHt9LCBvcmlnaW5QbGFjZW1lbnRzKTtcbiAgICAgIHZhciBsaXN0ID0gY2xvbmVbcGxhY2VtZW50XSB8fCBbXTtcbiAgICAgIGlmICghbGlzdC5sZW5ndGgpIHtcbiAgICAgICAgZGVsZXRlIGNsb25lW3BsYWNlbWVudF07XG4gICAgICB9XG4gICAgICByZXR1cm4gY2xvbmU7XG4gICAgfSk7XG4gIH07XG5cbiAgLy8gRWZmZWN0IHRlbGwgdGhhdCBwbGFjZW1lbnRzIGlzIGVtcHR5IG5vd1xuICB2YXIgZW1wdHlSZWYgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmIChPYmplY3Qua2V5cyhwbGFjZW1lbnRzKS5sZW5ndGggPiAwKSB7XG4gICAgICBlbXB0eVJlZi5jdXJyZW50ID0gdHJ1ZTtcbiAgICB9IGVsc2UgaWYgKGVtcHR5UmVmLmN1cnJlbnQpIHtcbiAgICAgIC8vIFRyaWdnZXIgb25seSB3aGVuIGZyb20gZXhpc3QgdG8gZW1wdHlcbiAgICAgIG9uQWxsUmVtb3ZlZCA9PT0gbnVsbCB8fCBvbkFsbFJlbW92ZWQgPT09IHZvaWQgMCB8fCBvbkFsbFJlbW92ZWQoKTtcbiAgICAgIGVtcHR5UmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICB9XG4gIH0sIFtwbGFjZW1lbnRzXSk7XG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PSBSZW5kZXIgPT09PT09PT09PT09PT09PT09PT09PT09XG4gIGlmICghY29udGFpbmVyKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgdmFyIHBsYWNlbWVudExpc3QgPSBPYmplY3Qua2V5cyhwbGFjZW1lbnRzKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9jcmVhdGVQb3J0YWwoIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFJlYWN0LkZyYWdtZW50LCBudWxsLCBwbGFjZW1lbnRMaXN0Lm1hcChmdW5jdGlvbiAocGxhY2VtZW50KSB7XG4gICAgdmFyIHBsYWNlbWVudENvbmZpZ0xpc3QgPSBwbGFjZW1lbnRzW3BsYWNlbWVudF07XG4gICAgdmFyIGxpc3QgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChOb3RpY2VMaXN0LCB7XG4gICAgICBrZXk6IHBsYWNlbWVudCxcbiAgICAgIGNvbmZpZ0xpc3Q6IHBsYWNlbWVudENvbmZpZ0xpc3QsXG4gICAgICBwbGFjZW1lbnQ6IHBsYWNlbWVudCxcbiAgICAgIHByZWZpeENsczogcHJlZml4Q2xzLFxuICAgICAgY2xhc3NOYW1lOiBjbGFzc05hbWUgPT09IG51bGwgfHwgY2xhc3NOYW1lID09PSB2b2lkIDAgPyB2b2lkIDAgOiBjbGFzc05hbWUocGxhY2VtZW50KSxcbiAgICAgIHN0eWxlOiBzdHlsZSA9PT0gbnVsbCB8fCBzdHlsZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogc3R5bGUocGxhY2VtZW50KSxcbiAgICAgIG1vdGlvbjogbW90aW9uLFxuICAgICAgb25Ob3RpY2VDbG9zZTogb25Ob3RpY2VDbG9zZSxcbiAgICAgIG9uQWxsTm90aWNlUmVtb3ZlZDogb25BbGxOb3RpY2VSZW1vdmVkLFxuICAgICAgc3RhY2s6IHN0YWNrXG4gICAgfSk7XG4gICAgcmV0dXJuIHJlbmRlck5vdGlmaWNhdGlvbnMgPyByZW5kZXJOb3RpZmljYXRpb25zKGxpc3QsIHtcbiAgICAgIHByZWZpeENsczogcHJlZml4Q2xzLFxuICAgICAga2V5OiBwbGFjZW1lbnRcbiAgICB9KSA6IGxpc3Q7XG4gIH0pKSwgY29udGFpbmVyKTtcbn0pO1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgTm90aWZpY2F0aW9ucy5kaXNwbGF5TmFtZSA9ICdOb3RpZmljYXRpb25zJztcbn1cbmV4cG9ydCBkZWZhdWx0IE5vdGlmaWNhdGlvbnM7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/Notifications.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js":
/*!******************************************************************!*\
  !*** ./node_modules/rc-notification/es/hooks/useNotification.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Notifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Notifications */ \"(ssr)/./node_modules/rc-notification/es/Notifications.js\");\n\n\n\nvar _excluded = [\"getContainer\", \"motion\", \"prefixCls\", \"maxCount\", \"className\", \"style\", \"onAllRemoved\", \"stack\", \"renderNotifications\"];\n\n\nvar defaultGetContainer = function defaultGetContainer() {\n  return document.body;\n};\nvar uniqueKey = 0;\nfunction mergeConfig() {\n  var clone = {};\n  for (var _len = arguments.length, objList = new Array(_len), _key = 0; _key < _len; _key++) {\n    objList[_key] = arguments[_key];\n  }\n  objList.forEach(function (obj) {\n    if (obj) {\n      Object.keys(obj).forEach(function (key) {\n        var val = obj[key];\n        if (val !== undefined) {\n          clone[key] = val;\n        }\n      });\n    }\n  });\n  return clone;\n}\nfunction useNotification() {\n  var rootConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _rootConfig$getContai = rootConfig.getContainer,\n    getContainer = _rootConfig$getContai === void 0 ? defaultGetContainer : _rootConfig$getContai,\n    motion = rootConfig.motion,\n    prefixCls = rootConfig.prefixCls,\n    maxCount = rootConfig.maxCount,\n    className = rootConfig.className,\n    style = rootConfig.style,\n    onAllRemoved = rootConfig.onAllRemoved,\n    stack = rootConfig.stack,\n    renderNotifications = rootConfig.renderNotifications,\n    shareConfig = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(rootConfig, _excluded);\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_3__.useState(),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState, 2),\n    container = _React$useState2[0],\n    setContainer = _React$useState2[1];\n  var notificationsRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef();\n  var contextHolder = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_Notifications__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n    container: container,\n    ref: notificationsRef,\n    prefixCls: prefixCls,\n    motion: motion,\n    maxCount: maxCount,\n    className: className,\n    style: style,\n    onAllRemoved: onAllRemoved,\n    stack: stack,\n    renderNotifications: renderNotifications\n  });\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_3__.useState([]),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState3, 2),\n    taskQueue = _React$useState4[0],\n    setTaskQueue = _React$useState4[1];\n\n  // ========================= Refs =========================\n  var api = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(function () {\n    return {\n      open: function open(config) {\n        var mergedConfig = mergeConfig(shareConfig, config);\n        if (mergedConfig.key === null || mergedConfig.key === undefined) {\n          mergedConfig.key = \"rc-notification-\".concat(uniqueKey);\n          uniqueKey += 1;\n        }\n        setTaskQueue(function (queue) {\n          return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [{\n            type: 'open',\n            config: mergedConfig\n          }]);\n        });\n      },\n      close: function close(key) {\n        setTaskQueue(function (queue) {\n          return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [{\n            type: 'close',\n            key: key\n          }]);\n        });\n      },\n      destroy: function destroy() {\n        setTaskQueue(function (queue) {\n          return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [{\n            type: 'destroy'\n          }]);\n        });\n      }\n    };\n  }, []);\n\n  // ======================= Container ======================\n  // React 18 should all in effect that we will check container in each render\n  // Which means getContainer should be stable.\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    setContainer(getContainer());\n  });\n\n  // ======================== Effect ========================\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    // Flush task when node ready\n    if (notificationsRef.current && taskQueue.length) {\n      taskQueue.forEach(function (task) {\n        switch (task.type) {\n          case 'open':\n            notificationsRef.current.open(task.config);\n            break;\n          case 'close':\n            notificationsRef.current.close(task.key);\n            break;\n          case 'destroy':\n            notificationsRef.current.destroy();\n            break;\n        }\n      });\n\n      // https://github.com/ant-design/ant-design/issues/52590\n      // React `startTransition` will run once `useEffect` but many times `setState`,\n      // So `setTaskQueue` with filtered array will cause infinite loop.\n      // We cache the first match queue instead.\n      var oriTaskQueue;\n      var tgtTaskQueue;\n\n      // React 17 will mix order of effect & setState in async\n      // - open: setState[0]\n      // - effect[0]\n      // - open: setState[1]\n      // - effect setState([]) * here will clean up [0, 1] in React 17\n      setTaskQueue(function (oriQueue) {\n        if (oriTaskQueue !== oriQueue || !tgtTaskQueue) {\n          oriTaskQueue = oriQueue;\n          tgtTaskQueue = oriQueue.filter(function (task) {\n            return !taskQueue.includes(task);\n          });\n        }\n        return tgtTaskQueue;\n      });\n    }\n  }, [taskQueue]);\n\n  // ======================== Return ========================\n  return [api, contextHolder];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/hooks/useStack.js":
/*!***********************************************************!*\
  !*** ./node_modules/rc-notification/es/hooks/useStack.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n\nvar DEFAULT_OFFSET = 8;\nvar DEFAULT_THRESHOLD = 3;\nvar DEFAULT_GAP = 16;\nvar useStack = function useStack(config) {\n  var result = {\n    offset: DEFAULT_OFFSET,\n    threshold: DEFAULT_THRESHOLD,\n    gap: DEFAULT_GAP\n  };\n  if (config && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(config) === 'object') {\n    var _config$offset, _config$threshold, _config$gap;\n    result.offset = (_config$offset = config.offset) !== null && _config$offset !== void 0 ? _config$offset : DEFAULT_OFFSET;\n    result.threshold = (_config$threshold = config.threshold) !== null && _config$threshold !== void 0 ? _config$threshold : DEFAULT_THRESHOLD;\n    result.gap = (_config$gap = config.gap) !== null && _config$gap !== void 0 ? _config$gap : DEFAULT_GAP;\n  }\n  return [!!config, result];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useStack);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL2hvb2tzL3VzZVN0YWNrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiw2RUFBTztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFlLFFBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLy4vbm9kZV9tb2R1bGVzL3JjLW5vdGlmaWNhdGlvbi9lcy9ob29rcy91c2VTdGFjay5qcz82N2MzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfdHlwZW9mIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS90eXBlb2ZcIjtcbnZhciBERUZBVUxUX09GRlNFVCA9IDg7XG52YXIgREVGQVVMVF9USFJFU0hPTEQgPSAzO1xudmFyIERFRkFVTFRfR0FQID0gMTY7XG52YXIgdXNlU3RhY2sgPSBmdW5jdGlvbiB1c2VTdGFjayhjb25maWcpIHtcbiAgdmFyIHJlc3VsdCA9IHtcbiAgICBvZmZzZXQ6IERFRkFVTFRfT0ZGU0VULFxuICAgIHRocmVzaG9sZDogREVGQVVMVF9USFJFU0hPTEQsXG4gICAgZ2FwOiBERUZBVUxUX0dBUFxuICB9O1xuICBpZiAoY29uZmlnICYmIF90eXBlb2YoY29uZmlnKSA9PT0gJ29iamVjdCcpIHtcbiAgICB2YXIgX2NvbmZpZyRvZmZzZXQsIF9jb25maWckdGhyZXNob2xkLCBfY29uZmlnJGdhcDtcbiAgICByZXN1bHQub2Zmc2V0ID0gKF9jb25maWckb2Zmc2V0ID0gY29uZmlnLm9mZnNldCkgIT09IG51bGwgJiYgX2NvbmZpZyRvZmZzZXQgIT09IHZvaWQgMCA/IF9jb25maWckb2Zmc2V0IDogREVGQVVMVF9PRkZTRVQ7XG4gICAgcmVzdWx0LnRocmVzaG9sZCA9IChfY29uZmlnJHRocmVzaG9sZCA9IGNvbmZpZy50aHJlc2hvbGQpICE9PSBudWxsICYmIF9jb25maWckdGhyZXNob2xkICE9PSB2b2lkIDAgPyBfY29uZmlnJHRocmVzaG9sZCA6IERFRkFVTFRfVEhSRVNIT0xEO1xuICAgIHJlc3VsdC5nYXAgPSAoX2NvbmZpZyRnYXAgPSBjb25maWcuZ2FwKSAhPT0gbnVsbCAmJiBfY29uZmlnJGdhcCAhPT0gdm9pZCAwID8gX2NvbmZpZyRnYXAgOiBERUZBVUxUX0dBUDtcbiAgfVxuICByZXR1cm4gWyEhY29uZmlnLCByZXN1bHRdO1xufTtcbmV4cG9ydCBkZWZhdWx0IHVzZVN0YWNrOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/hooks/useStack.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/index.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-notification/es/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Notice: () => (/* reexport safe */ _Notice__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   NotificationProvider: () => (/* reexport safe */ _NotificationProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   useNotification: () => (/* reexport safe */ _hooks_useNotification__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _hooks_useNotification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hooks/useNotification */ \"(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js\");\n/* harmony import */ var _Notice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Notice */ \"(ssr)/./node_modules/rc-notification/es/Notice.js\");\n/* harmony import */ var _NotificationProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationProvider */ \"(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFzRDtBQUN4QjtBQUM0QiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL2luZGV4LmpzPzBkZWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHVzZU5vdGlmaWNhdGlvbiBmcm9tIFwiLi9ob29rcy91c2VOb3RpZmljYXRpb25cIjtcbmltcG9ydCBOb3RpY2UgZnJvbSBcIi4vTm90aWNlXCI7XG5pbXBvcnQgTm90aWZpY2F0aW9uUHJvdmlkZXIgZnJvbSBcIi4vTm90aWZpY2F0aW9uUHJvdmlkZXJcIjtcbmV4cG9ydCB7IHVzZU5vdGlmaWNhdGlvbiwgTm90aWNlLCBOb3RpZmljYXRpb25Qcm92aWRlciB9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/index.js\n");

/***/ })

};
;
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2F%40ant-design%2Fnextjs-registry%2Fes%2FAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fjsvectormap%2Fdist%2Fjsvectormap.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fflatpickr%2Fdist%2Fflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fsatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fprovider%2FProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2F%40ant-design%2Fnextjs-registry%2Fes%2FAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fjsvectormap%2Fdist%2Fjsvectormap.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fflatpickr%2Fdist%2Fflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fsatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fprovider%2FProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js */ \"(ssr)/./node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/provider/Provider.tsx */ \"(ssr)/./src/provider/Provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1udCUyRmUlMkZQUk9KRUNUUyUyRkZSQU5DSVMlMjBBU0FOVEUlMkZueWFuc2Fwb18lMkZub2RlX21vZHVsZXMlMkYlNDBhbnQtZGVzaWduJTJGbmV4dGpzLXJlZ2lzdHJ5JTJGZXMlMkZBbnRkUmVnaXN0cnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1udCUyRmUlMkZQUk9KRUNUUyUyRkZSQU5DSVMlMjBBU0FOVEUlMkZueWFuc2Fwb18lMkZub2RlX21vZHVsZXMlMkZqc3ZlY3Rvcm1hcCUyRmRpc3QlMkZqc3ZlY3Rvcm1hcC5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGbW50JTJGZSUyRlBST0pFQ1RTJTJGRlJBTkNJUyUyMEFTQU5URSUyRm55YW5zYXBvXyUyRm5vZGVfbW9kdWxlcyUyRmZsYXRwaWNrciUyRmRpc3QlMkZmbGF0cGlja3IubWluLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZtbnQlMkZlJTJGUFJPSkVDVFMlMkZGUkFOQ0lTJTIwQVNBTlRFJTJGbnlhbnNhcG9fJTJGc3JjJTJGY3NzJTJGc2F0b3NoaS5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGbW50JTJGZSUyRlBST0pFQ1RTJTJGRlJBTkNJUyUyMEFTQU5URSUyRm55YW5zYXBvXyUyRnNyYyUyRmNzcyUyRnN0eWxlLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZtbnQlMkZlJTJGUFJPSkVDVFMlMkZGUkFOQ0lTJTIwQVNBTlRFJTJGbnlhbnNhcG9fJTJGc3JjJTJGcHJvdmlkZXIlMkZQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzT0FBc0s7QUFDdEs7QUFDQSxrS0FBb0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLz9jMzdhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9tbnQvZS9QUk9KRUNUUy9GUkFOQ0lTIEFTQU5URS9ueWFuc2Fwb18vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL25leHRqcy1yZWdpc3RyeS9lcy9BbnRkUmVnaXN0cnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvbW50L2UvUFJPSkVDVFMvRlJBTkNJUyBBU0FOVEUvbnlhbnNhcG9fL3NyYy9wcm92aWRlci9Qcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2F%40ant-design%2Fnextjs-registry%2Fes%2FAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fjsvectormap%2Fdist%2Fjsvectormap.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fflatpickr%2Fdist%2Fflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fsatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fcss%2Fstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fprovider%2FProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1udCUyRmUlMkZQUk9KRUNUUyUyRkZSQU5DSVMlMjBBU0FOVEUlMkZueWFuc2Fwb18lMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQThGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8/MGYyMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9tbnQvZS9QUk9KRUNUUy9GUkFOQ0lTIEFTQU5URS9ueWFuc2Fwb18vc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/api/apicaller.ts":
/*!******************************!*\
  !*** ./src/api/apicaller.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apicaller: () => (/* binding */ apicaller),\n/* harmony export */   loginapicaller: () => (/* binding */ loginapicaller)\n/* harmony export */ });\n// const BASEURL = 'http://localhost:5051/api/';\nconst BASEURL = \"https://nyansapo-api.vercel.app/api/\";\n//const BASEURL = 'https://nyansapoapi-production.up.railway.app/api/';\n// Function to check if the input is an object\nconst isitanobject = (text)=>{\n    try {\n        if (text == null || text == undefined || text == \"\") {\n            return false;\n        }\n        if (typeof text === \"object\" && !Array.isArray(text) && text !== null) {\n            return true;\n        } else {\n            return false;\n        }\n    } catch (error) {\n        return false;\n    }\n};\n// Client-side function to include role in headers\nconst apicaller = async (payloaddata, urlpath)=>{\n    try {\n        const headers = {\n            \"Accept\": \"application/json\",\n            \"Content-Type\": \"application/json\"\n        };\n        const options = {\n            method: \"POST\",\n            headers: headers,\n            body: JSON.stringify(payloaddata),\n            credentials: \"include\"\n        };\n        const fetchResult = await fetch(`${BASEURL}${urlpath}`, options);\n        const result = await fetchResult.json();\n        if (fetchResult.ok) {\n            return {\n                success: true,\n                message: result.message || \"Action completed successfully\",\n                data: result.data || null\n            };\n        } else {\n            return {\n                success: false,\n                message: result.message || \"Action failed\"\n            };\n        }\n    } catch (error) {\n        console.error(\"API call failed:\", error);\n        return {\n            success: false,\n            message: \"Unable to complete action. An error occurred!\"\n        };\n    }\n};\nconst loginapicaller = async (payloaddata, urlpath)=>{\n    try {\n        // Initialize the request headers (without Authorization and X-User-Role)\n        const requestheaders = {\n            Accept: \"application/json\",\n            \"Content-Type\": \"application/json\"\n        };\n        // Log the headers before sending (for debugging purposes)\n        console.log(\"Request Headers:\", requestheaders);\n        // Configure the request options, including method, headers, and body\n        const options = {\n            method: \"POST\",\n            headers: requestheaders,\n            body: JSON.stringify(payloaddata)\n        };\n        // Make the fetch request\n        const fetchResult = await fetch(`${BASEURL}${urlpath}`, options);\n        const result = await fetchResult.json();\n        // Log the response data\n        console.log(\"Response received:\", result);\n        // Check if the request was successful\n        if (fetchResult.ok) {\n            return {\n                success: true,\n                message: result.message || \"Action completed successfully\",\n                data: result.data || null\n            };\n        } else {\n            // Handle the error response\n            return {\n                success: false,\n                message: result.message || \"Action failed\"\n            };\n        }\n    } catch (error) {\n        // Handle any unexpected errors\n        console.error(\"API call failed:\", error);\n        return {\n            success: false,\n            message: \"Unable to complete action. An error occurred!\"\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/api/apicaller.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [showBackToTop, setShowBackToTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle back-to-top visibility based on scroll position\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            if (window.scrollY > 200) {\n                setShowBackToTop(true);\n            } else {\n                setShowBackToTop(false);\n            }\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    // Intersection Observer to trigger animations for each section\n    const [sectionInView, setSectionInView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        hero: true,\n        features: false,\n        about: false,\n        stats: false,\n        cta: false,\n        footer: false\n    });\n    const observerOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            threshold: 0.1,\n            rootMargin: \"0px 0px -100px 0px\"\n        }), []);\n    const observerCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entries)=>{\n        entries.forEach((entry)=>{\n            if (entry.isIntersecting) {\n                setSectionInView((prev)=>({\n                        ...prev,\n                        [entry.target.id]: true\n                    }));\n            }\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const observer = new IntersectionObserver(observerCallback, observerOptions);\n        const sections = document.querySelectorAll(\".section\");\n        sections.forEach((section)=>observer.observe(section));\n        return ()=>{\n            observer.disconnect();\n        };\n    }, [\n        observerCallback,\n        observerOptions\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.nav, {\n                className: \"fixed left-0 top-0 z-50 w-full bg-white/95 backdrop-blur-md border-b border-gray-200 shadow-sm\",\n                initial: {\n                    y: -100,\n                    opacity: 0\n                },\n                animate: {\n                    y: 0,\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.6\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-16 items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/\",\n                                className: \"group flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/images/clogo.png\",\n                                        alt: \"Nyansapo Logo\",\n                                        width: 40,\n                                        height: 40,\n                                        className: \"h-10 w-10 transition-transform duration-300 group-hover:scale-105\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"NyansaPo\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-gray-600 hover:text-primary transition-colors duration-200 font-medium\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#about\",\n                                        className: \"text-gray-600 hover:text-primary transition-colors duration-200 font-medium\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            const contactSection = document.getElementById(\"contact\");\n                                            if (contactSection) {\n                                                contactSection.scrollIntoView({\n                                                    behavior: \"smooth\"\n                                                });\n                                            }\n                                        },\n                                        className: \"text-gray-600 hover:text-primary transition-colors duration-200 font-medium\",\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/auth\",\n                                    className: \"group relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"relative inline-flex items-center justify-center rounded-lg bg-primary px-6 py-2.5 text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:bg-primary/90 hover:shadow-md\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.section, {\n                className: \"section relative bg-gradient-to-br from-gray-50 to-white pt-20 pb-16 lg:pt-24 lg:pb-20\",\n                id: \"hero\",\n                initial: {\n                    opacity: 1\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"text-center lg:text-left\",\n                                initial: {\n                                    x: -50,\n                                    opacity: 0\n                                },\n                                animate: sectionInView.hero ? {\n                                    x: 0,\n                                    opacity: 1\n                                } : {\n                                    x: -50,\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2\",\n                                                children: \"\\uD83D\\uDEE1️\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cybersecurity Made Simple\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl\",\n                                        children: [\n                                            \"Master Cybersecurity with\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-primary\",\n                                                children: \"Nano-Learning\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-6 text-xl text-gray-600 leading-relaxed\",\n                                        children: \"Transform your cybersecurity knowledge with bite-sized lessons designed for busy professionals. Learn critical security skills in just minutes a day.\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 flex flex-wrap gap-4 justify-center lg:justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-green-500 mr-2\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"5-minute lessons\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-green-500 mr-2\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Practical skills\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-green-500 mr-2\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Expert-designed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-10 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                href: \"/auth\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                                    className: \"inline-flex items-center justify-center rounded-lg bg-primary px-8 py-4 text-lg font-semibold text-white shadow-lg transition-all duration-200 hover:bg-primary/90 hover:shadow-xl\",\n                                                    whileHover: {\n                                                        scale: 1.02\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.98\n                                                    },\n                                                    children: [\n                                                        \"Get Started\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"ml-2 w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                href: \"/demo\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"inline-flex items-center justify-center rounded-lg border-2 border-gray-300 px-8 py-4 text-lg font-semibold text-gray-700 transition-all duration-200 hover:border-primary hover:text-primary\",\n                                                    children: [\n                                                        \"Request Demo\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"ml-2 w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"relative\",\n                                initial: {\n                                    x: 50,\n                                    opacity: 0\n                                },\n                                animate: sectionInView.hero ? {\n                                    x: 0,\n                                    opacity: 1\n                                } : {\n                                    x: 50,\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-full h-auto rounded-2xl shadow-2xl\",\n                                            src: \"https://bootstrapmade.com/demo/templates/FlexStart/assets/img/hero-img.png\",\n                                            alt: \"Cybersecurity Learning Platform\",\n                                            width: \"600\",\n                                            height: \"500\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 -right-4 bg-white rounded-lg shadow-lg p-4 hidden lg:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Live Learning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-4 -left-4 bg-white rounded-lg shadow-lg p-4 hidden lg:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-primary\",\n                                                        children: \"5min\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: \"Average Lesson\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.section, {\n                className: \"section py-16 lg:py-20 bg-white\",\n                id: \"features\",\n                initial: {\n                    opacity: 1,\n                    y: 0\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 sm:text-4xl lg:text-5xl\",\n                                    children: \"Why Choose NyansaPo?\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Our innovative nano-learning approach makes cybersecurity education accessible, engaging, and incredibly effective for professionals at every level.\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"text-center p-6 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors duration-200\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: sectionInView.features ? {\n                                        opacity: 1,\n                                        y: 0\n                                    } : {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-primary\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                            children: \"5-Minute Lessons\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Bite-sized learning modules that fit perfectly into your busy schedule. Master complex concepts in just minutes.\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"text-center p-6 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors duration-200\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: sectionInView.features ? {\n                                        opacity: 1,\n                                        y: 0\n                                    } : {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-primary\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                            children: \"Practical Skills\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Learn actionable cybersecurity techniques you can implement immediately in your work environment.\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"text-center p-6 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors duration-200\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: sectionInView.features ? {\n                                        opacity: 1,\n                                        y: 0\n                                    } : {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-primary\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                            children: \"Expert Content\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Curriculum designed by cybersecurity professionals with real-world experience in threat prevention.\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"text-center p-6 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors duration-200\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: sectionInView.features ? {\n                                        opacity: 1,\n                                        y: 0\n                                    } : {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.4\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-primary\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                            children: \"Instant Application\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Apply what you learn immediately with hands-on exercises and real-world scenarios.\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"text-center p-6 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors duration-200\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: sectionInView.features ? {\n                                        opacity: 1,\n                                        y: 0\n                                    } : {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-primary\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                            children: \"Progress Tracking\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Monitor your learning journey with detailed analytics and achievement badges.\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"text-center p-6 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors duration-200\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: sectionInView.features ? {\n                                        opacity: 1,\n                                        y: 0\n                                    } : {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.6\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-primary\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                            children: \"Team Learning\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Perfect for organizations looking to upskill their entire workforce in cybersecurity best practices.\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.section, {\n                className: \"section py-16 lg:py-20 bg-gray-50\",\n                id: \"about\",\n                initial: {\n                    opacity: 1,\n                    y: 0\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    x: -50,\n                                    opacity: 0\n                                },\n                                animate: sectionInView.about ? {\n                                    x: 0,\n                                    opacity: 1\n                                } : {\n                                    x: -50,\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-gray-900 sm:text-4xl lg:text-5xl mb-6\",\n                                        children: [\n                                            \"Empower Your Workforce with\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-primary\",\n                                                children: \"Nano-Learning\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                        children: \"NyansaPo's innovative nano-learning platform represents a transformative opportunity to enhance cybersecurity awareness and skills among your employees.\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-primary\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                                children: \"Proactive Security\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Address cybersecurity challenges before they become critical threats to your organization.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-primary\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                                children: \"Digital Asset Protection\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Safeguard your company's valuable digital assets with comprehensive security training.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-primary\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                                children: \"Resilient Culture\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Foster a security-conscious organizational culture that adapts to evolving threats.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"relative\",\n                                initial: {\n                                    x: 50,\n                                    opacity: 0\n                                },\n                                animate: sectionInView.about ? {\n                                    x: 0,\n                                    opacity: 1\n                                } : {\n                                    x: 50,\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-full h-auto rounded-2xl shadow-xl\",\n                                            alt: \"Cybersecurity team collaboration\",\n                                            src: \"/images/image3.png\",\n                                            width: \"600\",\n                                            height: \"500\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-6 -left-6 bg-white rounded-xl shadow-lg p-6 hidden lg:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-primary\",\n                                                        children: \"98%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Success Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.section, {\n                className: \"section py-16 bg-primary\",\n                id: \"stats\",\n                initial: {\n                    opacity: 1\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"text-center text-white\",\n                                initial: {\n                                    y: 30,\n                                    opacity: 0\n                                },\n                                animate: sectionInView.stats ? {\n                                    y: 0,\n                                    opacity: 1\n                                } : {\n                                    y: 30,\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold mb-2\",\n                                        children: \"10,000+\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100\",\n                                        children: \"Professionals Trained\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"text-center text-white\",\n                                initial: {\n                                    y: 30,\n                                    opacity: 0\n                                },\n                                animate: sectionInView.stats ? {\n                                    y: 0,\n                                    opacity: 1\n                                } : {\n                                    y: 30,\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold mb-2\",\n                                        children: \"500+\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100\",\n                                        children: \"Companies Trust Us\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"text-center text-white\",\n                                initial: {\n                                    y: 30,\n                                    opacity: 0\n                                },\n                                animate: sectionInView.stats ? {\n                                    y: 0,\n                                    opacity: 1\n                                } : {\n                                    y: 30,\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold mb-2\",\n                                        children: \"98%\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100\",\n                                        children: \"Completion Rate\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"text-center text-white\",\n                                initial: {\n                                    y: 30,\n                                    opacity: 0\n                                },\n                                animate: sectionInView.stats ? {\n                                    y: 0,\n                                    opacity: 1\n                                } : {\n                                    y: 30,\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.4\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold mb-2\",\n                                        children: \"24/7\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100\",\n                                        children: \"Learning Access\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                lineNumber: 485,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.section, {\n                className: \"section py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-white\",\n                id: \"cta\",\n                initial: {\n                    opacity: 1,\n                    y: 0\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900 sm:text-4xl lg:text-5xl mb-6\",\n                            children: \"Ready to Transform Your Cybersecurity Knowledge?\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-10 max-w-2xl mx-auto\",\n                            children: \"Join thousands of professionals who have already strengthened their cybersecurity skills with our innovative nano-learning approach.\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/auth\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        className: \"inline-flex items-center justify-center rounded-lg bg-primary px-8 py-4 text-lg font-semibold text-white shadow-lg transition-all duration-200 hover:bg-primary/90 hover:shadow-xl\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            \"Get Started\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"ml-2 w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/demo\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"inline-flex items-center justify-center rounded-lg border-2 border-gray-300 px-8 py-4 text-lg font-semibold text-gray-700 transition-all duration-200 hover:border-primary hover:text-primary\",\n                                        children: \"Schedule Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                    lineNumber: 542,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                lineNumber: 535,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"w-full bg-black text-white min-h-[400px]\",\n                id: \"footer\",\n                style: {\n                    backgroundColor: \"#000000\",\n                    color: \"#ffffff\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"contact\",\n                        className: \"absolute -mt-16\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                href: \"/\",\n                                                className: \"group flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: \"/images/clogo.png\",\n                                                        alt: \"Nyansapo Logo\",\n                                                        className: \"h-10 w-10 transition-transform duration-300 group-hover:scale-105\",\n                                                        width: 40,\n                                                        height: 40\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: \"NyansaPo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 mb-6 max-w-md leading-relaxed\",\n                                                children: \"Empowering professionals with innovative nano-learning cybersecurity education. Learn critical security skills in just minutes a day.\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                                        \"aria-label\": \"Twitter\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                                        \"aria-label\": \"LinkedIn\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                                        \"aria-label\": \"Facebook\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-4 text-white\",\n                                                children: \"Quick Links\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#features\",\n                                                            className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                            children: \"Features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#about\",\n                                                            className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                            children: \"About\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/auth\",\n                                                            className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                            children: \"Get Started\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                            children: \"Pricing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-4 text-white\",\n                                                children: \"Contact Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-blue-400 mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                    lineNumber: 629,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        href: \"tel:+233241136885\",\n                                                                        className: \"text-gray-300 hover:text-white transition-colors duration-200 block\",\n                                                                        children: \"+233 (24) 113-6885\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        href: \"tel:+233205261895\",\n                                                                        className: \"text-gray-300 hover:text-white transition-colors duration-200 block\",\n                                                                        children: \"+233 (20) 526-1895\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-blue-400 mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                    lineNumber: 642,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                href: \"mailto:<EMAIL>\",\n                                                                className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                                children: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: [\n                                            \"\\xa9 \",\n                                            new Date().getFullYear(),\n                                            \" NyansaPo. All rights reserved.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap justify-center md:justify-end gap-6 mt-4 md:mt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                href: \"/privacy-policy\",\n                                                className: \"text-gray-400 hover:text-white text-sm transition-colors duration-200\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                href: \"/terms-of-service\",\n                                                className: \"text-gray-400 hover:text-white text-sm transition-colors duration-200\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                href: \"/cookie-policy\",\n                                                className: \"text-gray-400 hover:text-white text-sm transition-colors duration-200\",\n                                                children: \"Cookie Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                lineNumber: 573,\n                columnNumber: 7\n            }, this),\n            showBackToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                onClick: ()=>window.scrollTo({\n                        top: 0,\n                        behavior: \"smooth\"\n                    }),\n                className: \"fixed bottom-8 right-8 w-12 h-12 rounded-full bg-primary text-white shadow-lg hover:bg-primary/90 transition-all duration-200 flex items-center justify-center z-50\",\n                initial: {\n                    opacity: 0,\n                    scale: 0\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.9\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-6 h-6\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                        lineNumber: 677,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                    lineNumber: 676,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n                lineNumber: 668,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/provider/Provider.tsx":
/*!***********************************!*\
  !*** ./src/provider/Provider.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ClientProvider = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_3__.Provider, {\n        store: _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_2__.store,\n        children: children\n    }, void 0, false, {\n        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/provider/Provider.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXIvUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ2E7QUFDUztBQU1oRCxNQUFNRyxpQkFBZ0QsQ0FBQyxFQUFFQyxRQUFRLEVBQUU7SUFDakUscUJBQU8sOERBQUNILGlEQUFRQTtRQUFDQyxPQUFPQSx3REFBS0E7a0JBQUdFOzs7Ozs7QUFDbEM7QUFFQSxpRUFBZUQsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9zcmMvcHJvdmlkZXIvUHJvdmlkZXIudHN4PzRiNDIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnOyBcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IFByb3ZpZGVyIH0gZnJvbSAncmVhY3QtcmVkdXgnO1xyXG5pbXBvcnQgeyBzdG9yZSB9IGZyb20gJy4uL3JlZHV4UlRLL3N0b3JlL3N0b3JlJzsgXHJcblxyXG5pbnRlcmZhY2UgQ2xpZW50UHJvdmlkZXJQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufVxyXG5cclxuY29uc3QgQ2xpZW50UHJvdmlkZXI6IFJlYWN0LkZDPENsaWVudFByb3ZpZGVyUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIHJldHVybiA8UHJvdmlkZXIgc3RvcmU9e3N0b3JlfT57Y2hpbGRyZW59PC9Qcm92aWRlcj47XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBDbGllbnRQcm92aWRlcjtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiUHJvdmlkZXIiLCJzdG9yZSIsIkNsaWVudFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/provider/Provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/authApi.ts":
/*!******************************************!*\
  !*** ./src/reduxRTK/services/authApi.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   useCheckAuthQuery: () => (/* binding */ useCheckAuthQuery),\n/* harmony export */   useCreateUserMutation: () => (/* binding */ useCreateUserMutation),\n/* harmony export */   useDeleteUserMutation: () => (/* binding */ useDeleteUserMutation),\n/* harmony export */   useGetUsersQuery: () => (/* binding */ useGetUsersQuery),\n/* harmony export */   useLoginMutation: () => (/* binding */ useLoginMutation),\n/* harmony export */   useLogoutMutation: () => (/* binding */ useLogoutMutation),\n/* harmony export */   useResetPasswordMutation: () => (/* binding */ useResetPasswordMutation),\n/* harmony export */   useSendDetailsMutation: () => (/* binding */ useSendDetailsMutation),\n/* harmony export */   useUpdateUserMutation: () => (/* binding */ useUpdateUserMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Custom baseQuery function\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            return {\n                data: response\n            };\n        } else {\n            // Handle Unauthorized and Token Expiry\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"userRole\");\n                localStorage.removeItem(\"firstName\");\n                localStorage.removeItem(\"lastName\");\n                localStorage.removeItem(\"userId\");\n                dispatch(authApi.util.resetApiState());\n                window.location.href = \"/auth\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Create the base API service using RTK Query\nconst authApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"authApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            login: builder.mutation({\n                query: (credentials)=>({\n                        url: \"user/login\",\n                        payload: credentials\n                    }),\n                onQueryStarted: async ({ email, password }, { dispatch, queryFulfilled })=>{\n                    try {\n                        const { data } = await queryFulfilled;\n                        if (data?.success && data?.data) {\n                            const { user, accessToken } = data.data;\n                            if (user && accessToken) {\n                                localStorage.setItem(\"userRole\", user.role || \"\");\n                                localStorage.setItem(\"firstName\", user.firstName || \"\");\n                                localStorage.setItem(\"lastName\", user.lastName || \"\");\n                                localStorage.setItem(\"userId\", user.id || \"\");\n                                localStorage.setItem(\"token\", accessToken);\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Login failed\", error);\n                    }\n                }\n            }),\n            logout: builder.mutation({\n                query: ()=>({\n                        url: \"user/logout\"\n                    }),\n                onQueryStarted: async (_, { dispatch, queryFulfilled })=>{\n                    try {\n                        const { data } = await queryFulfilled;\n                        if (data?.success) {\n                            localStorage.removeItem(\"userRole\");\n                            localStorage.removeItem(\"firstName\");\n                            localStorage.removeItem(\"lastName\");\n                            localStorage.removeItem(\"userId\");\n                            localStorage.removeItem(\"token\");\n                            dispatch(authApi.util.resetApiState());\n                            window.location.href = \"/auth\";\n                        }\n                    } catch (error) {\n                        console.error(\"Logout failed\", error);\n                    }\n                }\n            }),\n            checkAuth: builder.query({\n                query: ()=>({\n                        url: \"user/check/auth\",\n                        payload: {\n                            mode: \"checkauth\"\n                        }\n                    })\n            }),\n            createUser: builder.mutation({\n                query: (newUsers)=>({\n                        url: \"user/register\",\n                        payload: {\n                            mode: \"createnew\",\n                            users: newUsers.users\n                        }\n                    })\n            }),\n            getUsers: builder.query({\n                query: ({ page, limit, accessToken })=>{\n                    const params = new URLSearchParams({\n                        page: String(page || 1),\n                        limit: String(limit || 10)\n                    });\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    return {\n                        url: `user/getusers?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                },\n                transformResponse: (response)=>({\n                        ...response,\n                        data: {\n                            ...response.data,\n                            users: response.data.users\n                        }\n                    })\n            }),\n            deleteUser: builder.mutation({\n                query: ({ mode, userId, accessToken })=>({\n                        url: \"user/delete\",\n                        payload: {\n                            mode,\n                            userId,\n                            accessToken\n                        }\n                    })\n            }),\n            updateUser: builder.mutation({\n                query: ({ userId, mode, accessToken, ...updateData })=>({\n                        url: \"user/update\",\n                        payload: {\n                            userId,\n                            mode,\n                            accessToken,\n                            ...updateData\n                        }\n                    })\n            }),\n            sendDetails: builder.mutation({\n                query: ({ mode, userId, accessToken })=>{\n                    // Ensure mode is either 'senddetails' (single) or 'sendbulkdetails' (all)\n                    if (mode !== \"senddetails\" && mode !== \"sendbulkdetails\") {\n                        throw new Error(\"Invalid mode. Use 'senddetails' for a single user or 'sendbulkdetails' for all users.\");\n                    }\n                    return {\n                        url: \"user/send-details\",\n                        payload: mode === \"senddetails\" ? {\n                            mode,\n                            userId,\n                            accessToken\n                        } : {\n                            mode,\n                            accessToken\n                        }\n                    };\n                }\n            }),\n            // sendDetails: builder.mutation<\n            //   SendDetailsResponse,\n            //   { mode: string; userIds?: string[]; accessToken: string }\n            // >({\n            //   query: ({ mode, userIds, accessToken }) => {\n            //     if (!accessToken) {\n            //       throw new Error(\"Access token is required but missing.\");\n            //     }\n            //     console.log(\"Sending Payload:\", { mode, userIds, accessToken }); // Debugging\n            //     return {\n            //       url: \"user/send-details\",\n            //       payload: mode === \"senddetails\" ? { mode, userIds, accessToken } : { mode, accessToken },\n            //     };\n            //   },\n            // }),\n            resetPassword: builder.mutation({\n                query: ({ userId, accessToken })=>({\n                        url: \"user/reset-password\",\n                        payload: {\n                            userId,\n                            accessToken\n                        }\n                    }),\n                async onQueryStarted ({ userId, accessToken }, { queryFulfilled }) {\n                    try {\n                        const { data } = await queryFulfilled;\n                        if (data?.success) {\n                            // If successful, the data contains user details\n                            const userDetails = data; // This contains the user details\n                            // Store the user details in localStorage\n                            const { id, firstName, lastName, role, email } = userDetails.data;\n                            localStorage.setItem(\"userRole\", role || \"\");\n                            localStorage.setItem(\"firstName\", firstName || \"\");\n                            localStorage.setItem(\"lastName\", lastName || \"\");\n                            localStorage.setItem(\"userId\", id || \"\");\n                            localStorage.setItem(\"userEmail\", email || \"\");\n                            console.log(\"User details retrieved and stored:\", userDetails);\n                        } else {\n                            console.warn(\"Failed to retrieve user details or user mismatch\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error retrieving user details:\", error?.message || \"Unknown error\");\n                    }\n                }\n            })\n        })\n});\nconst { useLoginMutation, useLogoutMutation, useCheckAuthQuery, useCreateUserMutation, useGetUsersQuery, useDeleteUserMutation, useUpdateUserMutation, useSendDetailsMutation, useResetPasswordMutation } = authApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/authApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/courseApi.ts":
/*!********************************************!*\
  !*** ./src/reduxRTK/services/courseApi.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   courseApi: () => (/* binding */ courseApi),\n/* harmony export */   useAssignCourseMutation: () => (/* binding */ useAssignCourseMutation),\n/* harmony export */   useCreateCourseMutation: () => (/* binding */ useCreateCourseMutation),\n/* harmony export */   useDeleteCourseMutation: () => (/* binding */ useDeleteCourseMutation),\n/* harmony export */   useGetCourseAssignmentsQuery: () => (/* binding */ useGetCourseAssignmentsQuery),\n/* harmony export */   useGetCourseByUserIdQuery: () => (/* binding */ useGetCourseByUserIdQuery),\n/* harmony export */   useGetSingleCourseQuery: () => (/* binding */ useGetSingleCourseQuery),\n/* harmony export */   useGetUserAssignmentsQuery: () => (/* binding */ useGetUserAssignmentsQuery),\n/* harmony export */   useRetrieveCourseQuery: () => (/* binding */ useRetrieveCourseQuery),\n/* harmony export */   useUnassignCourseMutation: () => (/* binding */ useUnassignCourseMutation),\n/* harmony export */   useUpdateCourseMutation: () => (/* binding */ useUpdateCourseMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const actualPayload = payload || {};\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(actualPayload, url);\n        console.log(\"Raw API response:\", response);\n        if (response.success) {\n            if (response.data !== undefined && response.data !== null) {\n                if (Array.isArray(response.data.courses)) {\n                    return {\n                        data: {\n                            success: response.success,\n                            message: response.message,\n                            data: {\n                                courses: response.data.courses,\n                                total: response.data.total,\n                                page: actualPayload.page || 1,\n                                limit: actualPayload.limit || response.data.courses.length\n                            }\n                        }\n                    };\n                }\n                if (response.data.id && response.data.title) {\n                    return {\n                        data: response\n                    };\n                }\n            }\n            return {\n                data: {\n                    success: response.success,\n                    message: response.message\n                }\n            };\n        } else {\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                dispatch(courseApi.util.resetApiState());\n                window.location.href = \"/\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API\nconst courseApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"courseApi\",\n    baseQuery: customBaseQuery,\n    tagTypes: [\n        \"CourseAssignments\"\n    ],\n    endpoints: (builder)=>({\n            retrieveCourse: builder.query({\n                query: ({ page, limit, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    return {\n                        url: `main-course-management?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                }\n            }),\n            getCourseByUserId: builder.query({\n                query: ({ userId, page, limit, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    return {\n                        url: `course-by-user?${params.toString()}`,\n                        payload: {\n                            mode: \"getcoursesbyuser\",\n                            userId,\n                            accessToken\n                        }\n                    };\n                }\n            }),\n            createCourse: builder.mutation({\n                query: ({ title, description, url, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"createnew\",\n                            title,\n                            description,\n                            url,\n                            accessToken\n                        }\n                    })\n            }),\n            deleteCourse: builder.mutation({\n                query: ({ courseId, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"delete\",\n                            id: courseId,\n                            accessToken\n                        }\n                    })\n            }),\n            updateCourse: builder.mutation({\n                query: ({ courseId, title, description, url, userIds, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"update\",\n                            id: courseId,\n                            title,\n                            description,\n                            url,\n                            userIds,\n                            accessToken\n                        }\n                    })\n            }),\n            getSingleCourse: builder.query({\n                query: ({ id, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"getsingle\",\n                            id,\n                            accessToken\n                        }\n                    }),\n                transformResponse: (response)=>{\n                    return {\n                        ...response,\n                        data: {\n                            ...response.data,\n                            quizzes: response.data.quizzes.map((quiz)=>({\n                                    ...quiz,\n                                    answer: quiz.answer?.trim()?.toLowerCase()\n                                }))\n                        }\n                    };\n                }\n            }),\n            // New Assignment Management Endpoints\n            assignCourse: builder.mutation({\n                query: ({ courseId, userIds, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"assign\",\n                            id: courseId,\n                            userIds,\n                            accessToken\n                        }\n                    }),\n                invalidatesTags: [\n                    \"CourseAssignments\"\n                ]\n            }),\n            unassignCourse: builder.mutation({\n                query: ({ courseId, userIds, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"unassign\",\n                            id: courseId,\n                            userIds,\n                            accessToken\n                        }\n                    }),\n                invalidatesTags: [\n                    \"CourseAssignments\"\n                ]\n            }),\n            getCourseAssignments: builder.query({\n                query: ({ courseId, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"assignments\",\n                            id: courseId,\n                            accessToken\n                        }\n                    }),\n                providesTags: [\n                    \"CourseAssignments\"\n                ]\n            }),\n            getUserAssignments: builder.query({\n                query: ({ userId, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"user-assignments\",\n                            userId,\n                            accessToken\n                        }\n                    })\n            })\n        })\n});\n// Export hooks\nconst { useRetrieveCourseQuery, useGetCourseByUserIdQuery, useCreateCourseMutation, useDeleteCourseMutation, useUpdateCourseMutation, useGetSingleCourseQuery, // New Assignment Hooks\nuseAssignCourseMutation, useUnassignCourseMutation, useGetCourseAssignmentsQuery, useGetUserAssignmentsQuery } = courseApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/courseApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/demoApi.ts":
/*!******************************************!*\
  !*** ./src/reduxRTK/services/demoApi.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   demoApi: () => (/* binding */ demoApi),\n/* harmony export */   useSubmitDemoRequestMutation: () => (/* binding */ useSubmitDemoRequestMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query following your existing pattern\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            return {\n                data: response\n            };\n        } else {\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API following your existing pattern\nconst demoApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"demoApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            submitDemoRequest: builder.mutation({\n                query: (demoData)=>({\n                        url: \"demo-request\",\n                        payload: demoData\n                    })\n            })\n        })\n});\n// Export hooks following your existing pattern\nconst { useSubmitDemoRequestMutation } = demoApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/demoApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/quizApi.ts":
/*!******************************************!*\
  !*** ./src/reduxRTK/services/quizApi.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   quizApi: () => (/* binding */ quizApi),\n/* harmony export */   useCreateQuizMutation: () => (/* binding */ useCreateQuizMutation),\n/* harmony export */   useDeleteQuizMutation: () => (/* binding */ useDeleteQuizMutation),\n/* harmony export */   useGetSingleQuizQuery: () => (/* binding */ useGetSingleQuizQuery),\n/* harmony export */   useRetrieveQuizQuery: () => (/* binding */ useRetrieveQuizQuery),\n/* harmony export */   useUpdateQuizMutation: () => (/* binding */ useUpdateQuizMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            if (response.data) {\n                // Handle RetrieveQuizResponse\n                if (Array.isArray(response.data.quizzes) && typeof response.data.total === \"number\") {\n                    return {\n                        data: response\n                    };\n                }\n                // Handle GetSingleQuizResponse\n                if (typeof response.data.id === \"string\" && typeof response.data.question === \"string\" && Array.isArray(response.data.options)) {\n                    return {\n                        data: response\n                    };\n                }\n            }\n            // If `data` is null or not needed, return success and message only\n            return {\n                data: {\n                    success: response.success,\n                    message: response.message\n                }\n            };\n        } else {\n            // Handle token expiration\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                dispatch(quizApi.util.resetApiState());\n                window.location.href = \"/\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API\nconst quizApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"quizApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            retrieveQuiz: builder.query({\n                query: ({ page, limit, courseId, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    if (courseId) params.append(\"courseId\", courseId);\n                    return {\n                        url: `main-quiz-management?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                },\n                transformResponse: (response)=>({\n                        ...response,\n                        data: {\n                            ...response.data,\n                            quizzes: response.data.quizzes || []\n                        }\n                    })\n            }),\n            createQuiz: builder.mutation({\n                query: ({ courseId, question, options, answer, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"createnew\",\n                            courseId,\n                            question,\n                            options,\n                            answer,\n                            accessToken\n                        }\n                    })\n            }),\n            deleteQuiz: builder.mutation({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"delete\",\n                            id,\n                            accessToken\n                        }\n                    })\n            }),\n            updateQuiz: builder.mutation({\n                query: ({ id, courseId, question, options, answer, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"update\",\n                            id,\n                            courseId,\n                            question,\n                            options,\n                            answer,\n                            accessToken\n                        }\n                    })\n            }),\n            getSingleQuiz: builder.query({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"getsingle\",\n                            id,\n                            accessToken\n                        }\n                    })\n            })\n        })\n});\n// Export hooks\nconst { useRetrieveQuizQuery, useCreateQuizMutation, useDeleteQuizMutation, useUpdateQuizMutation, useGetSingleQuizQuery } = quizApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/quizApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/reportApi.ts":
/*!********************************************!*\
  !*** ./src/reduxRTK/services/reportApi.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reportApi: () => (/* binding */ reportApi),\n/* harmony export */   useCreateReportMutation: () => (/* binding */ useCreateReportMutation),\n/* harmony export */   useDeleteReportMutation: () => (/* binding */ useDeleteReportMutation),\n/* harmony export */   useGetSingleReportQuery: () => (/* binding */ useGetSingleReportQuery),\n/* harmony export */   useRetrieveReportQuery: () => (/* binding */ useRetrieveReportQuery),\n/* harmony export */   useUpdateReportMutation: () => (/* binding */ useUpdateReportMutation),\n/* harmony export */   useUpsertReportMutation: () => (/* binding */ useUpsertReportMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            if (response.data) {\n                // Handle RetrieveReportResponse\n                if (Array.isArray(response.data.reports) && typeof response.data.total === \"number\") {\n                    return {\n                        data: response\n                    };\n                }\n                // Handle GetSingleReportResponse\n                if (typeof response.data.id === \"string\" && typeof response.data.courseId === \"string\" && typeof response.data.userId === \"string\") {\n                    return {\n                        data: response\n                    };\n                }\n            }\n            // If `data` is null or not needed, return success and message only\n            return {\n                data: {\n                    success: response.success,\n                    message: response.message\n                }\n            };\n        } else {\n            // Handle token expiration\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                dispatch(reportApi.util.resetApiState());\n                window.location.href = \"/\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API\nconst reportApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"reportApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            retrieveReport: builder.query({\n                query: ({ page, limit, courseId, userId, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    if (courseId) params.append(\"courseId\", courseId);\n                    if (userId) params.append(\"userId\", userId);\n                    return {\n                        url: `main-quiz-report?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                },\n                transformResponse: (response)=>({\n                        ...response,\n                        data: {\n                            ...response.data,\n                            reports: response.data.reports || []\n                        }\n                    })\n            }),\n            createReport: builder.mutation({\n                query: ({ courseId, userId, completed, score, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"createnew\",\n                            courseId,\n                            userId,\n                            completed,\n                            score,\n                            accessToken\n                        }\n                    })\n            }),\n            upsertReport: builder.mutation({\n                query: ({ courseId, userId, completed, score, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"upsert\",\n                            courseId,\n                            userId,\n                            completed,\n                            score,\n                            accessToken\n                        }\n                    })\n            }),\n            deleteReport: builder.mutation({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"delete\",\n                            id,\n                            accessToken\n                        }\n                    })\n            }),\n            updateReport: builder.mutation({\n                query: ({ id, courseId, userId, completed, score, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"update\",\n                            id,\n                            courseId,\n                            userId,\n                            completed,\n                            score,\n                            accessToken\n                        }\n                    })\n            }),\n            getSingleReport: builder.query({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"getsingle\",\n                            id,\n                            accessToken\n                        }\n                    })\n            })\n        })\n});\n// Export hooks\nconst { useRetrieveReportQuery, useCreateReportMutation, useUpsertReportMutation, useDeleteReportMutation, useUpdateReportMutation, useGetSingleReportQuery } = reportApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/reportApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/store/store.ts":
/*!*************************************!*\
  !*** ./src/reduxRTK/store/store.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: () => (/* binding */ store)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var _services_authApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services/authApi */ \"(ssr)/./src/reduxRTK/services/authApi.ts\");\n/* harmony import */ var _services_courseApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/courseApi */ \"(ssr)/./src/reduxRTK/services/courseApi.ts\");\n/* harmony import */ var _services_quizApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/quizApi */ \"(ssr)/./src/reduxRTK/services/quizApi.ts\");\n/* harmony import */ var _services_reportApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/reportApi */ \"(ssr)/./src/reduxRTK/services/reportApi.ts\");\n/* harmony import */ var _services_demoApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/demoApi */ \"(ssr)/./src/reduxRTK/services/demoApi.ts\");\n// store/index.ts\n\n\n\n\n\n\n// Create and configure the Redux store\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_5__.configureStore)({\n    reducer: {\n        [_services_authApi__WEBPACK_IMPORTED_MODULE_0__.authApi.reducerPath]: _services_authApi__WEBPACK_IMPORTED_MODULE_0__.authApi.reducer,\n        [_services_courseApi__WEBPACK_IMPORTED_MODULE_1__.courseApi.reducerPath]: _services_courseApi__WEBPACK_IMPORTED_MODULE_1__.courseApi.reducer,\n        [_services_quizApi__WEBPACK_IMPORTED_MODULE_2__.quizApi.reducerPath]: _services_quizApi__WEBPACK_IMPORTED_MODULE_2__.quizApi.reducer,\n        [_services_reportApi__WEBPACK_IMPORTED_MODULE_3__.reportApi.reducerPath]: _services_reportApi__WEBPACK_IMPORTED_MODULE_3__.reportApi.reducer,\n        [_services_demoApi__WEBPACK_IMPORTED_MODULE_4__.demoApi.reducerPath]: _services_demoApi__WEBPACK_IMPORTED_MODULE_4__.demoApi.reducer\n    },\n    middleware: (getDefaultMiddleware)=>getDefaultMiddleware().concat(_services_authApi__WEBPACK_IMPORTED_MODULE_0__.authApi.middleware, _services_courseApi__WEBPACK_IMPORTED_MODULE_1__.courseApi.middleware, _services_quizApi__WEBPACK_IMPORTED_MODULE_2__.quizApi.middleware, _services_reportApi__WEBPACK_IMPORTED_MODULE_3__.reportApi.middleware, _services_demoApi__WEBPACK_IMPORTED_MODULE_4__.demoApi.middleware)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/store/store.ts\n");

/***/ }),

/***/ "(rsc)/./src/css/satoshi.css":
/*!*****************************!*\
  !*** ./src/css/satoshi.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2216757f3ab5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY3NzL3NhdG9zaGkuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8uL3NyYy9jc3Mvc2F0b3NoaS5jc3M/NGI5OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIyMTY3NTdmM2FiNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/css/satoshi.css\n");

/***/ }),

/***/ "(rsc)/./src/css/style.css":
/*!***************************!*\
  !*** ./src/css/style.css ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d5f3da26a5e8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY3NzL3N0eWxlLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9zcmMvY3NzL3N0eWxlLmNzcz85YzYzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZDVmM2RhMjZhNWU4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/css/style.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsvectormap_dist_jsvectormap_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsvectormap/dist/jsvectormap.css */ \"(rsc)/./node_modules/jsvectormap/dist/jsvectormap.css\");\n/* harmony import */ var flatpickr_dist_flatpickr_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! flatpickr/dist/flatpickr.min.css */ \"(rsc)/./node_modules/flatpickr/dist/flatpickr.min.css\");\n/* harmony import */ var _css_satoshi_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/css/satoshi.css */ \"(rsc)/./src/css/satoshi.css\");\n/* harmony import */ var _css_style_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/css/style.css */ \"(rsc)/./src/css/style.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(rsc)/./node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* harmony import */ var _provider_Provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/provider/Provider */ \"(rsc)/./src/provider/Provider.tsx\");\n\n\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dark:bg-boxdark-2 dark:text-bodydark\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_provider_Provider__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__.AntdRegistry, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQTBDO0FBQ0E7QUFDZjtBQUNGO0FBQ0M7QUFDaUM7QUFDVjtBQUVsQyxTQUFTRyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQ0MsNEVBQUNDO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDUCwwREFBY0E7OEJBQ2IsNEVBQUNELHFFQUFZQTtrQ0FBRUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBcImpzdmVjdG9ybWFwL2Rpc3QvanN2ZWN0b3JtYXAuY3NzXCI7XHJcbmltcG9ydCBcImZsYXRwaWNrci9kaXN0L2ZsYXRwaWNrci5taW4uY3NzXCI7XHJcbmltcG9ydCBcIkAvY3NzL3NhdG9zaGkuY3NzXCI7XHJcbmltcG9ydCBcIkAvY3NzL3N0eWxlLmNzc1wiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IEFudGRSZWdpc3RyeSB9IGZyb20gXCJAYW50LWRlc2lnbi9uZXh0anMtcmVnaXN0cnlcIjtcclxuaW1wb3J0IENsaWVudFByb3ZpZGVyIGZyb20gXCJAL3Byb3ZpZGVyL1Byb3ZpZGVyXCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufTogUmVhZG9ubHk8e1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0+KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRhcms6YmctYm94ZGFyay0yIGRhcms6dGV4dC1ib2R5ZGFya1wiPlxyXG4gICAgICAgICAgPENsaWVudFByb3ZpZGVyPlxyXG4gICAgICAgICAgICA8QW50ZFJlZ2lzdHJ5PntjaGlsZHJlbn08L0FudGRSZWdpc3RyeT5cclxuICAgICAgICAgIDwvQ2xpZW50UHJvdmlkZXI+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkFudGRSZWdpc3RyeSIsIkNsaWVudFByb3ZpZGVyIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/app/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/provider/Provider.tsx":
/*!***********************************!*\
  !*** ./src/provider/Provider.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/PROJECTS/FRANCIS ASANTE/nyansapo_/src/provider/Provider.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"32x32\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLy4vc3JjL2FwcC9mYXZpY29uLmljbz8xNGE2Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjMyeDMyXCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@ant-design","vendor-chunks/@reduxjs","vendor-chunks/rc-util","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/@babel","vendor-chunks/reselect","vendor-chunks/redux","vendor-chunks/stylis","vendor-chunks/use-sync-external-store","vendor-chunks/@emotion","vendor-chunks/@swc","vendor-chunks/redux-thunk","vendor-chunks/jsvectormap","vendor-chunks/flatpickr","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
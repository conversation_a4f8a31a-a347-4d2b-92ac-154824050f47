"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateQuizSchema = exports.addQuizSchema = void 0;
const zod_1 = require("zod");
exports.addQuizSchema = zod_1.z.object({
    courseId: zod_1.z.string().uuid(),
    question: zod_1.z.string().min(1, "Question is required"),
    options: zod_1.z
        .array(zod_1.z.string())
        .min(2, "At least two options are required")
        .max(5, "No more than five options allowed"),
    answer: zod_1.z.string().min(1, "Answer is required"),
});
exports.updateQuizSchema = zod_1.z.object({
    id: zod_1.z.string().uuid({ message: "Quiz ID must be a valid UUID" }),
    courseId: zod_1.z.string().uuid({ message: "Course ID must be a valid UUID" }).optional(),
    question: zod_1.z.string({ message: "Question must be a string" }).optional(),
    options: zod_1.z.array(zod_1.z.string({ message: "Each option must be a string" })).optional(),
    answer: zod_1.z.string({ message: "Answer must be a string" }).optional(),
});

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.demoRequestController = void 0;
const demoService_1 = require("../services/demoService");
const demoRequestController = async (req, res) => {
    try {
        const { firstName, lastName, email, company, jobTitle, phone, employees, message } = req.body;
        // Validate required fields
        if (!firstName || !lastName || !email || !company || !jobTitle) {
            return res.status(400).json({
                success: false,
                message: "Missing required fields: firstName, lastName, email, company, and jobTitle are required.",
            });
        }
        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                success: false,
                message: "Invalid email format.",
            });
        }
        // Call the service to process demo request
        await (0, demoService_1.sendDemoRequest)({
            firstName,
            lastName,
            email,
            company,
            jobTitle,
            phone,
            employees,
            message
        });
        return res.status(200).json({
            success: true,
            message: "Demo request submitted successfully! We will contact you within 24 hours to schedule your personalized demonstration.",
        });
    }
    catch (error) {
        console.error("Error processing demo request:", error.message);
        // Provide user-friendly error messages
        let userMessage = "Failed to submit demo request. Please try again later.";
        if (error.message.includes("rate limiting")) {
            userMessage = "Demo request received! Due to high volume, email confirmations may be delayed. Our team will contact you within 24 hours.";
        }
        else if (error.message.includes("Failed to send any emails")) {
            userMessage = "Demo request received! Email notifications are temporarily unavailable, but our team will contact you within 24 hours.";
        }
        return res.status(500).json({
            success: false,
            message: userMessage,
        });
    }
};
exports.demoRequestController = demoRequestController;

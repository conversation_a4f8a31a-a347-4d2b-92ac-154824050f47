"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/css/style.css":
/*!***************************!*\
  !*** ./src/css/style.css ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"cffcbbcd7414\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jc3Mvc3R5bGUuY3NzIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY3NzL3N0eWxlLmNzcz8zOTVhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2ZmY2JiY2Q3NDE0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/css/style.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RootLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var jsvectormap_dist_jsvectormap_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsvectormap/dist/jsvectormap.css */ \"(app-pages-browser)/./node_modules/jsvectormap/dist/jsvectormap.css\");\n/* harmony import */ var flatpickr_dist_flatpickr_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! flatpickr/dist/flatpickr.min.css */ \"(app-pages-browser)/./node_modules/flatpickr/dist/flatpickr.min.css\");\n/* harmony import */ var _css_satoshi_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/css/satoshi.css */ \"(app-pages-browser)/./src/css/satoshi.css\");\n/* harmony import */ var _css_style_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/css/style.css */ \"(app-pages-browser)/./src/css/style.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(app-pages-browser)/./node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n // Import Badge and Alert from Ant Design\n\nfunction RootLayout(param) {\n    let { children } = param;\n    _s();\n    const [hydrated, setHydrated] = react__WEBPACK_IMPORTED_MODULE_5___default().useState(false);\n    react__WEBPACK_IMPORTED_MODULE_5___default().useEffect(()=>{\n        setHydrated(true);\n    }, []);\n    if (!hydrated) return null;\n    // All other hooks and logic go here, after hydration is confirmed\n    const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_5___default().useState(true);\n    const [loadingAuth, setLoadingAuth] = react__WEBPACK_IMPORTED_MODULE_5___default().useState(true);\n    const [isAuthorized, setIsAuthorized] = react__WEBPACK_IMPORTED_MODULE_5___default().useState(true); // Assuming authorized by default\n    react__WEBPACK_IMPORTED_MODULE_5___default().useEffect(()=>{\n        const token = localStorage.getItem(\"token\");\n        const userRole = localStorage.getItem(\"userRole\");\n        if (!token || userRole !== \"admin\") {\n            setIsAuthorized(false);\n        }\n        setTimeout(()=>setLoading(false), 1000);\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_5___default().useEffect(()=>{\n        const timer = setTimeout(()=>{\n            setLoadingAuth(false);\n        }, 500);\n        return ()=>clearTimeout(timer);\n    }, []);\n    if (loadingAuth || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this);\n    }\n    const handleRedirect = ()=>{\n        window.location.href = \"/\";\n    };\n    if (!isAuthorized) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen w-full flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Ribbon, {\n                    text: \"Unauthorized\",\n                    color: \"red\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        message: \"You are not authorized. Please log in.\",\n                        type: \"error\",\n                        showIcon: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    type: \"primary\",\n                    onClick: handleRedirect,\n                    className: \"mt-4\",\n                    children: \"Go to Login\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dark:bg-boxdark-2 dark:text-bodydark\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__.AntdRegistry, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(RootLayout, \"JcMeOFI5GFHMphEMvybgpZUL+eM=\");\n_c = RootLayout;\nvar _c;\n$RefreshReg$(_c, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/layout.tsx\n"));

/***/ })

});
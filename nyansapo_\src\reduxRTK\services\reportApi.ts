import { createApi, BaseQueryFn } from '@reduxjs/toolkit/query/react';
import { apicaller } from '../../api/apicaller';

// Define Interfaces
interface RetrieveReportResponse {
  success: boolean;
  data: {
    total: number;
    reports: any[]; // Replace `any` with a specific report interface if available
    page: number;
    limit: number;
  };
  message: string;
}

interface CreateReportPayload {
  courseId: string;
  userId: string;
  completed: boolean;
  score: number | null;
}

interface UpdateReportPayload {
  id: string;
  courseId?: string;
  userId?: string;
  completed?: boolean;
  score?: number;
}

interface DeleteReportPayload {
  id: string;
}

interface GetSingleReportResponse {
  success: boolean;
  message: string;
  data: {
    id: string;
    courseId: string;
    userId: string;
    completed: boolean;
    score: number;
    createdAt: string;
    updatedAt: string;
  };
}

// Define the custom base query
const customBaseQuery: BaseQueryFn<
  { url: string; payload?: Record<string, any> },
  RetrieveReportResponse | GetSingleReportResponse | { success: boolean; message: string },
  { message: string }
> = async ({ url, payload }, { dispatch }) => {
  try {
    const response = await apicaller(payload || {}, url);

    if (response.success) {
      if (response.data) {
        // Handle RetrieveReportResponse
        if (Array.isArray(response.data.reports) && typeof response.data.total === 'number') {
          return { data: response as RetrieveReportResponse };
        }

        // Handle GetSingleReportResponse
        if (
          typeof response.data.id === 'string' &&
          typeof response.data.courseId === 'string' &&
          typeof response.data.userId === 'string'
        ) {
          return { data: response as GetSingleReportResponse };
        }
      }

      // If `data` is null or not needed, return success and message only
      return {
        data: {
          success: response.success,
          message: response.message,
        },
      };
    } else {
      // Handle token expiration
      if (response.message === 'Invalid or expired token') {
        localStorage.removeItem('token');
        dispatch(reportApi.util.resetApiState());
        window.location.href = '/';
      }
      return { error: { message: response.message } };
    }
  } catch (error: any) {
    return { error: { message: error.message || 'An unknown error occurred' } };
  }
};

// Define the API
export const reportApi = createApi({
  reducerPath: 'reportApi',
  baseQuery: customBaseQuery,
  endpoints: (builder) => ({
    retrieveReport: builder.query<
      RetrieveReportResponse,
      { page?: number; limit?: number; courseId?: string; userId?: string; accessToken: string }
    >({
      query: ({ page, limit, courseId, userId, accessToken }) => {
        const params = new URLSearchParams();

        if (page !== undefined) params.append('page', page.toString());
        if (limit !== undefined) params.append('limit', limit.toString());
        if (courseId) params.append('courseId', courseId);
        if (userId) params.append('userId', userId);

        return {
          url: `main-quiz-report?${params.toString()}`,
          payload: { mode: 'retrieve', accessToken },
        };
      },
      transformResponse: (response: RetrieveReportResponse) => ({
        ...response,
        data: {
          ...response.data,
          reports: response.data.reports || [], // Default to an empty array if reports are undefined
        },
      }),
    }),

    createReport: builder.mutation<{ success: boolean; message: string }, CreateReportPayload & { accessToken: string }>({
      query: ({ courseId, userId, completed, score, accessToken }) => ({
        url: 'main-quiz-report',
        payload: {
          mode: 'createnew',
          courseId,
          userId,
          completed,
          score,
          accessToken,
        },
      }),
    }),

    upsertReport: builder.mutation<{ success: boolean; message: string }, CreateReportPayload & { accessToken: string }>({
      query: ({ courseId, userId, completed, score, accessToken }) => ({
        url: 'main-quiz-report',
        payload: {
          mode: 'upsert',
          courseId,
          userId,
          completed,
          score,
          accessToken,
        },
      }),
    }),

    deleteReport: builder.mutation<{ success: boolean; message: string }, DeleteReportPayload & { accessToken: string }>({
      query: ({ id, accessToken }) => ({
        url: 'main-quiz-report',
        payload: {
          mode: 'delete',
          id,
          accessToken,
        },
      }),
    }),

    updateReport: builder.mutation<RetrieveReportResponse, UpdateReportPayload & { accessToken: string }>({
      query: ({ id, courseId, userId, completed, score, accessToken }) => ({
        url: 'main-quiz-report',
        payload: {
          mode: 'update',
          id,
          courseId,
          userId,
          completed,
          score,
          accessToken,
        },
      }),
    }),

    getSingleReport: builder.query<GetSingleReportResponse, { id: string; accessToken: string }>({
      query: ({ id, accessToken }) => ({
        url: 'main-quiz-report',
        payload: {
          mode: 'getsingle',
          id,
          accessToken,
        },
      }),
    }),
  }),
});

// Export hooks
export const {
  useRetrieveReportQuery,
  useCreateReportMutation,
  useUpsertReportMutation,
  useDeleteReportMutation,
  useUpdateReportMutation,
  useGetSingleReportQuery,
} = reportApi;

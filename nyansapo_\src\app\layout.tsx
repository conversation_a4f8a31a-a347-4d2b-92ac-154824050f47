"use client";
import "jsvectormap/dist/jsvectormap.css";
import "flatpickr/dist/flatpickr.min.css";
import "@/css/satoshi.css";
import "@/css/style.css";
import React from "react";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import ClientProvider from "@/provider/Provider";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [hydrated, setHydrated] = React.useState(false);
  React.useEffect(() => {
    setHydrated(true);
  }, []);
  if (!hydrated) return null;
  return (
    <html lang="en">
      <body>
        <div className="dark:bg-boxdark-2 dark:text-bodydark">
          {/* {loading ? <Loader /> : children} */}
          <ClientProvider>
            <AntdRegistry>{children}</AntdRegistry>
          </ClientProvider>
        </div>
      </body>
    </html>
  );
}

import "jsvectormap/dist/jsvectormap.css";
import "flatpickr/dist/flatpickr.min.css";
import "@/css/satoshi.css";
import "@/css/style.css";
import React from "react";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import ClientProvider from "@/provider/Provider";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <div className="dark:bg-boxdark-2 dark:text-bodydark">
          <ClientProvider>
            <AntdRegistry>{children}</AntdRegistry>
          </ClientProvider>
        </div>
      </body>
    </html>
  );
}

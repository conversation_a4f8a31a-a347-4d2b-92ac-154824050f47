/**
 * This file was generated by kysely-codegen.
 * Please do not edit it manually.
 */

import type { ColumnType } from "kysely";

export type Generated<T> = T extends ColumnType<infer S, infer I, infer U>
  ? ColumnType<S, I | undefined, U>
  : ColumnType<T, T | undefined, T>;

export type Timestamp = ColumnType<Date, Date | string, Date | string>;

export interface _PrismaMigrations {
  applied_steps_count: Generated<number>;
  checksum: string;
  finished_at: Timestamp | null;
  id: string;
  logs: string | null;
  migration_name: string;
  rolled_back_at: Timestamp | null;
  started_at: Generated<Timestamp>;
}

export interface _UserCourses {
  A: string;
  B: string;
}

export interface Course {
  createdAt: Generated<Timestamp>;
  description: string;
  id: string;
  title: string;
  updatedAt: Timestamp;
  url: string;
}

export interface Pgmigrations {
  id: Generated<number>;
  name: string;
  run_on: Timestamp;
}

export interface Quiz {
  answer: string;
  courseId: string;
  createdAt: Generated<Timestamp>;
  id: string;
  options: string[] | null;
  question: string;
  updatedAt: Timestamp;
}

export interface Report {
  completed: Generated<boolean>;
  courseId: string;
  createdAt: Generated<Timestamp>;
  id: string;
  score: number | null;
  updatedAt: Timestamp;
  userId: string;
}

export interface User {
  createdAt: Generated<Timestamp>;
  email: string;
  firstName: string;
  id: string;
  lastName: string;
  password: string;
  role: string;
  updatedAt: Timestamp;
}

export interface UserCourse {
  assignedBy: string | null;
  courseId: string;
  createdAt: Generated<Timestamp>;
  id: string;
  userId: string;
}

export interface DB {
  _prisma_migrations: _PrismaMigrations;
  _UserCourses: _UserCourses;
  Course: Course;
  pgmigrations: Pgmigrations;
  Quiz: Quiz;
  Report: Report;
  User: User;
  UserCourse: UserCourse;
}

"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.db = void 0;
const dotenv = __importStar(require("dotenv"));
const kysely_1 = require("kysely");
const pg_1 = require("pg");
const envalid_1 = require("envalid");
dotenv.config();
const env = (0, envalid_1.cleanEnv)(process.env, {
    DATABASE_URL: (0, envalid_1.str)(),
    NODE_ENV: (0, envalid_1.str)({ choices: ['development', 'test', 'production'] }),
    DB_PORT: (0, envalid_1.port)({ default: 5432 }),
});
const pool = new pg_1.Pool({
    connectionString: env.DATABASE_URL,
    ssl: env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : undefined,
});
exports.db = new kysely_1.Kysely({
    dialect: new kysely_1.PostgresDialect({
        pool: pool,
    }),
});
// import * as dotenv from 'dotenv';
// import { Kysely, MysqlDialect } from 'kysely';
// import { createPool } from 'mysql2';
// import { cleanEnv, str, port } from 'envalid';
// import { DB } from './types';
// dotenv.config();
// const env = cleanEnv(process.env, {
//   DATABASE_URL: str(), // Format: mysql://user:password@host:port/database
//   NODE_ENV: str({ choices: ['development', 'test', 'production'] }),
//   DB_PORT: port({ default: 3306 }), // Default MySQL port
// });
// const pool = createPool({
//   uri: env.DATABASE_URL,
//   ssl: env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : undefined,
//   waitForConnections: true,
//   connectionLimit: 10,
//   queueLimit: 0,
// });
// export const db = new Kysely<DB>({
//   dialect: new MysqlDialect({
//     pool: pool,
//   }),
// });

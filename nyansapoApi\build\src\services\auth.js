"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendDetails = exports.getUserDetailsFromToken = exports.updateUser = exports.deleteUser = exports.getUsers = exports.refreshTokens = exports.loginUser = exports.createUser = void 0;
const db_1 = require("../db/db");
const uuid_1 = require("uuid");
const bcrypt_1 = __importDefault(require("bcrypt"));
const schema_1 = require("../schema");
const jwtService_1 = require("../services/jwtService");
const nodemailer_1 = __importDefault(require("nodemailer"));
// export const createUser = async (
//   input: CreateUserInput
// ): Promise<{
//   user: Omit<DB["User"], "password">;
//   accessToken: string;
//   refreshToken: string;
// }> => {
//   const parsedInput = createUserSchema.safeParse(input);
//   if (!parsedInput.success) {
//     throw new Error(
//       "Invalid input: " +
//         parsedInput.error.errors.map((err) => err.message).join(", ")
//     );
//   }
//   const userId = uuidv4();
//   const hashedPassword = await bcrypt.hash(parsedInput.data.password, 10);
//   try {
//     // Insert the new user into the database
//     const [newUser] = await db
//       .insertInto("User")
//       .values({
//         id: userId,
//         email: parsedInput.data.email,
//         password: hashedPassword,
//         firstName: parsedInput.data.firstName,
//         lastName: parsedInput.data.lastName,
//         role: parsedInput.data.role,
//         createdAt: new Date(),
//         updatedAt: new Date(),
//       })
//       .returning([
//         "id",
//         "email",
//         "password",
//         "firstName",
//         "lastName",
//         "role",
//         "createdAt",
//         "updatedAt",
//       ])
//       .execute();
//     // Type assertion for the returned user
//     const userWithPassword = newUser as unknown as DB["User"];
//     const { password, ...userWithoutPassword } = userWithPassword;
//     // Generate tokens
//     const accessToken = generateToken(
//       {
//         id: userId,
//         email: parsedInput.data.email,
//         role: parsedInput.data.role,
//       },
//       "access"
//     );
//     const refreshToken = generateToken(
//       {
//         id: userId,
//         email: parsedInput.data.email,
//         role: parsedInput.data.role,
//       },
//       "refresh"
//     );
//     return {
//       user: userWithoutPassword,
//       accessToken,
//       refreshToken,
//     };
//   } catch (error) {
//     console.error("Error creating user:", error);
//     throw new Error("Unable to create user. Please try again later.");
//   }
// };
const createUser = async (inputs) => {
    const parsedInputs = inputs.map((input) => schema_1.createUserSchema.safeParse(input));
    const invalidInputs = parsedInputs.filter((result) => !result.success);
    if (invalidInputs.length > 0) {
        throw new Error("Invalid input(s): " +
            invalidInputs
                .map((result, index) => result.success
                ? ""
                : `User ${index + 1}: ` +
                    result.error.errors.map((err) => err.message).join(", "))
                .filter(Boolean)
                .join("; "));
    }
    const validatedInputs = parsedInputs.map((result) => result.data);
    const usersToInsert = await Promise.all(validatedInputs.map(async (input) => ({
        id: (0, uuid_1.v4)(),
        email: input.email,
        password: await bcrypt_1.default.hash(input.password, 10),
        firstName: input.firstName,
        lastName: input.lastName,
        role: input.role,
        createdAt: new Date(),
        updatedAt: new Date(),
    })));
    try {
        // Bulk insert users into the database
        const insertedUsers = await db_1.db
            .insertInto("User")
            .values(usersToInsert)
            .returning([
            "id",
            "email",
            "firstName",
            "lastName",
            "role",
            "createdAt",
            "updatedAt",
        ])
            .execute();
        // Return user data excluding the password
        return insertedUsers.map((user) => user);
    }
    catch (error) {
        console.error("Error creating users:", error);
        throw new Error("Unable to create users. Please try again later.");
    }
};
exports.createUser = createUser;
const loginUser = async (input, res) => {
    const parsedInput = schema_1.loginUserSchema.safeParse(input);
    if (!parsedInput.success) {
        const errors = parsedInput.error.errors
            .map((err) => err.message)
            .join(", ");
        throw new Error(`Invalid input: ${errors}`);
    }
    const { email, password } = parsedInput.data;
    try {
        // Check if the user exists
        const [user] = await db_1.db
            .selectFrom("User")
            .select(["id", "email", "password", "firstName", "lastName", "role"])
            .where("email", "=", email)
            .execute();
        if (!user) {
            throw new Error("Email not found. Please check your credentials.");
        }
        // Validate password
        const isPasswordValid = await bcrypt_1.default.compare(password, user.password);
        if (!isPasswordValid) {
            throw new Error("Invalid password");
        }
        // Remove the password field and create the user without the password
        const { password: _, ...userWithoutPassword } = user;
        // Generate access and refresh tokens
        const accessToken = (0, jwtService_1.generateToken)({
            id: user.id,
            email: user.email,
            role: user.role,
        }, "access");
        const refreshToken = (0, jwtService_1.generateToken)({
            id: user.id,
            email: user.email,
            role: user.role,
        }, "refresh");
        return {
            user: userWithoutPassword,
            accessToken,
            refreshToken,
        };
    }
    catch (error) {
        console.error("Error logging in:", error.message);
        throw new Error(error.message || "Unable to log in. Please try again.");
    }
};
exports.loginUser = loginUser;
const refreshTokens = async (refreshToken, res) => {
    try {
        const newAccessToken = (0, jwtService_1.refreshAccessToken)(refreshToken);
        // Return the new access token
        return res.status(200).json({
            message: "Token refreshed successfully",
            accessToken: newAccessToken,
        });
    }
    catch (error) {
        console.error("Error refreshing token:", error);
        throw new Error("Unable to refresh token. Please try again later.");
    }
};
exports.refreshTokens = refreshTokens;
const getUsers = async (page, limit, accessToken // Add accessToken as part of the function signature
) => {
    try {
        if (page < 1 || limit < 1) {
            throw new Error("Page and limit must be greater than 0.");
        }
        const users = await db_1.db
            .selectFrom("User")
            .select(["id", "email", "firstName", "lastName", "role", "createdAt", "updatedAt"])
            .orderBy("createdAt", "desc")
            .offset((page - 1) * limit)
            .limit(limit)
            .execute();
        const totalResult = await db_1.db
            .selectFrom("User")
            .select([db_1.db.fn.count("id").as("total")])
            .execute();
        const total = Number(totalResult[0].total);
        return {
            users: users,
            total,
        };
    }
    catch (error) {
        console.error("Error fetching users:", error);
        throw new Error("Unable to fetch users. Please try again later.");
    }
};
exports.getUsers = getUsers;
const deleteUser = async (userId, accessToken) => {
    const user = await db_1.db
        .selectFrom("User")
        .select(["id"])
        .where("id", "=", userId)
        .execute();
    if (user.length === 0) {
        throw new Error("User not found.");
    }
    await db_1.db.deleteFrom("User").where("id", "=", userId).execute();
};
exports.deleteUser = deleteUser;
const updateUser = async (userId, input, accessToken, // Access token comes first
res // Response comes last
) => {
    const parsedInput = schema_1.updateUserSchema.safeParse(input);
    if (!parsedInput.success) {
        throw new Error("Invalid input: " +
            parsedInput.error.errors.map((err) => err.message).join(", "));
    }
    const updateData = parsedInput.data;
    const [user] = await db_1.db
        .selectFrom("User")
        .select(["id"])
        .where("id", "=", userId)
        .execute();
    if (!user) {
        throw new Error("User not found.");
    }
    if (updateData.password) {
        updateData.password = await bcrypt_1.default.hash(updateData.password, 10);
    }
    try {
        const [updatedUser] = await db_1.db
            .updateTable("User")
            .set({
            ...updateData,
            updatedAt: new Date(),
        })
            .where("id", "=", userId)
            .returning(["id", "email", "password", "firstName", "lastName", "role", "createdAt", "updatedAt"])
            .execute();
        const { password, ...userWithoutPassword } = updatedUser;
        return userWithoutPassword;
    }
    catch (error) {
        console.error("Error updating user:", error);
        throw new Error("Unable to update user. Please try again later.");
    }
};
exports.updateUser = updateUser;
const hashPassword = async (password) => {
    const salt = await bcrypt_1.default.genSalt(10);
    return bcrypt_1.default.hash(password, salt);
};
// Service to handle reset password functionality
const getUserDetailsFromToken = async (userId, accessToken) => {
    try {
        // Verify the access token
        const decoded = (0, jwtService_1.verifyToken)(accessToken, "access");
        // Check if the user ID from token matches the provided ID
        if (decoded.id !== userId) {
            throw new Error("User ID does not match the token.");
        }
        // Fetch user details
        const user = await db_1.db
            .selectFrom("User")
            .select(["id", "email", "firstName", "lastName", "role"])
            .where("id", "=", userId)
            .executeTakeFirst();
        if (!user) {
            throw new Error("User not found.");
        }
        return user;
    }
    catch (error) {
        console.error("Error retrieving user details:", error.message);
        return null;
    }
};
exports.getUserDetailsFromToken = getUserDetailsFromToken;
const sendDetails = async (userId, // Accepts null for bulk sending
accessToken) => {
    let users;
    if (userId) {
        // Fetch the single user details by ID
        users = await db_1.db
            .selectFrom("User")
            .select(["id", "email", "firstName", "lastName", "role"])
            .where("id", "=", userId)
            .execute();
    }
    else {
        // Fetch all users for bulk sending
        users = await db_1.db
            .selectFrom("User")
            .select(["id", "email", "firstName", "lastName", "role"])
            .execute();
    }
    if (!users || users.length === 0) {
        throw new Error(userId ? "User not found." : "No users found.");
    }
    // Configure Nodemailer
    const transporter = nodemailer_1.default.createTransport({
        host: 'mail.nyansa-po.com',
        port: 587,
        secure: false, // Use STARTTLS instead of SSL
        auth: {
            user: process.env.EMAIL_USERNAME,
            pass: process.env.EMAIL_PASSWORD,
        },
        tls: {
            rejectUnauthorized: false // Accept self-signed certificates
        }
    });
    for (const user of users) {
        const email = user.email;
        const username = `${user.firstName} ${user.lastName}`;
        // Generate a reset password token
        const resetPasswordToken = (0, jwtService_1.generateToken)({
            id: user.id,
            email: user.email,
            role: user.role,
        }, "access");
        const resetPasswordUrl = `${process.env.FRONTEND_URL}/reset-password?userId=${user.id}&token=${resetPasswordToken}`;
        // Email content
        const mailOptions = {
            from: process.env.EMAIL_USERNAME,
            to: email,
            subject: "Your Account Details",
            html: `
        <p>Hello ${username},</p>
        <p>Your organization has assigned this course to you. Kindly click the button below to get started:</p>
    
        <p>
          <a href="${resetPasswordUrl}" target="_blank" style="
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: #ffffff;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
          ">
            Get Started
          </a>
        </p>
    
        <p>If you have any questions, feel free to contact our support team: ${process.env.EMAIL_SUPPORT}</p>
      `,
        };
        try {
            await transporter.sendMail(mailOptions);
            console.log(`Account details sent to ${email}`);
        }
        catch (error) {
            console.error("Error sending email:", error);
        }
    }
};
exports.sendDetails = sendDetails;

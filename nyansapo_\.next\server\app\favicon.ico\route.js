"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/favicon.ico/route";
exports.ids = ["app/favicon.ico/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_page_2Ffavicon_ico_2Froute_filePath_2Fmnt_2Fe_2FPROJECTS_2FFRANCIS_20ASANTE_2Fnyansapo_2Fsrc_2Fapp_2Ffavicon_ico_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&filePath=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__ */ \"(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"export\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/favicon.ico/route\",\n        pathname: \"/favicon.ico\",\n        filename: \"favicon\",\n        bundlePath: \"app/favicon.ico/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&filePath=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_page_2Ffavicon_ico_2Froute_filePath_2Fmnt_2Fe_2FPROJECTS_2FFRANCIS_20ASANTE_2Fnyansapo_2Fsrc_2Fapp_2Ffavicon_ico_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/favicon.ico/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__ ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(app-metadata-route)/./node_modules/next/dist/api/server.js\");\n/* static asset route */\n\n\nconst contentType = \"image/x-icon\"\nconst buffer = Buffer.from(\"AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAABMLAAATCwAAAAAAAAAAAADSdhlO0nYZ1tJ2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn70nYZsNJ2GRvSdhkA85YhAPOWIRvzliGw85Yh+/OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliHW85YhTtJ2GdbSdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZeNJ2GQDzliEA85YhePOWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliHW0nYZ/tJ2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/SdhmjzXMaAfiaIgHzliGj85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf7Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2GaXOdBoB95kiAfOWIaXzliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZpc50GgH3mSIB85YhpfOWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/SdhmlznQaAfeZIgHzliGl85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2GaXOdBoB95kiAfOWIaXzliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZpc50GgH3mSIB85YhpfOWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhml0HUaAb1jFQDzliGd85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIfnSdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2GaXSdxoB8pUhAPOWIVnzliH085Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH+85YhsNJ2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZpdJ3GgHskCAA85YhBvOWIVnzliGf85YhpfOWIaXzliGl85YhpfOWIaXzliGl85YhpfOWIaXzliGl85YhpPOWIXnzliEb0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhml0ncaAdJ3GgD4nSIA95YhAPeaIgH3mSIB95kiAfeZIgH3mSIB95kiAfeZIgH3mSIB95kiAfeZIgH3mSIB+JsjAPibIwDSdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2GaXSdxoB0ncaAM1zGgDPdRsAznQaAc50GgHOdBoBznQaAc50GgHOdBoBznQaAc50GgHOdBoBznQaAc50GgHNchgAzXIYANJ2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZpdJ3GgHSdhkA0nYZBtJ2GVjSdhme0nYZpdJ2GaXSdhml0nYZpdJ2GaXSdhml0nYZpdJ2GaXSdhml0nYZpNJ2GXnSdhkb0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhml0ncaAdJ2GQDSdhlZ0nYZ9NJ2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/tJ2GbDSdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2GaXSeBsB0mkMANJ2GZ3Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ+dJ2GfnSdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZndJrDgDSeBsB0nYZpdJ2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZsNJ2Gf7Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2GfTSdhlZ0nYZANJ3GgHSdhml0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhkb0nYZedJ2GaTSdhml0nYZpdJ2GaXSdhml0nYZpdJ2GaXSdhml0nYZpdJ2GaXSdhmf0nYZWdJ2GQbSdhkA0ncaAdJ2GaXSdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/8xxFwDNcRgAznMaAc50GgHOdBoBznQaAc50GgHOdBoBznQaAc50GgHOdBoBznQaAc5zGQHOdhwAz3EYANJ3GgDSdxoB0nYZpdJ2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/+ZsiAPmbIgD3mSIB95kiAfeZIgH3mSIB95kiAfeZIgH3mSIB95kiAfeZIgH3mSIB95kiAfeYIQD4myQA0ncaANJ3GgHSdhml0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/zliEb85YhePOWIaTzliGl85YhpfOWIaXzliGl85YhpfOWIaXzliGl85YhpfOWIaXzliGe85YhWfOWIQbskCAA0ncaAdJ2GaXSdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ//OWIbDzliH+85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH085YhWPKVIQDSdxoB0nYZpdJ2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/85Yh+fOWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliGdyG0XANB1GgHSdhml0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIaX3mSIBznQaAdJ2GaXSdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85YhpfeZIgHOdBoB0nYZpdJ2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliGl95kiAc50GgHSdhml0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIaX3mSIBznQaAdJ2GaXSdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85YhpfeZIgHOdBoB0nYZpdJ2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/85Yh/vOWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliGj+JoiAc10GgHSdhmj0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf7zliHW85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIXjzliEA0nYZANJ2GXjSdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ1vOWIU7zliHW85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIf/zliH/85Yh//OWIfvzliGw85YhG/OWIQDSdhkA0nYZG9J2GbDSdhn70nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2Gf/Sdhn/0nYZ/9J2GdfSdhlOAAGAAAABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAACAAAAAgAAAAOADAADgAwAAgAAAAIAAAACAAAABAAAAAQAAAAEAAMAHAADABwAAAAEAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAABgAA=\", 'base64'\n  )\n\nfunction GET() {\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n\nconst dynamic = 'force-static'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2FPROJECTS%2FFRANCIS%20ASANTE%2Fnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
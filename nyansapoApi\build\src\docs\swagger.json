{"openapi": "3.0.0", "info": {"title": "Learning App API", "description": "API for managing users, courses, quizzes, and reports in the Learning App.", "version": "1.0.2"}, "servers": [{"url": "https://nyansapo-api.vercel.app/api", "description": "Local server"}], "paths": {"/main-course-management": {"post": {"summary": "Manage Courses", "tags": ["Course"], "parameters": [{"$ref": "#/components/parameters/SecretComsKey"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"mode": {"type": "string", "enum": ["retrieve", "createnew", "update", "delete", "<PERSON><PERSON><PERSON>"], "example": "createnew"}, "id": {"type": "string", "example": "course-uuid", "description": "Course ID, required for update, delete, or getsingle."}, "title": {"type": "string", "example": "Introduction to Programming", "description": "Course title, required for createnew or update."}, "description": {"type": "string", "example": "A beginner-friendly course on programming.", "description": "Course description, required for createnew or update."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequest"}}}}, "/main-quiz-management": {"post": {"summary": "Manage Quizzes", "tags": ["Quiz"], "parameters": [{"$ref": "#/components/parameters/SecretComsKey"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"mode": {"type": "string", "enum": ["retrieve", "createnew", "update", "delete", "<PERSON><PERSON><PERSON>"], "example": "retrieve"}, "id": {"type": "string", "example": "quiz-uuid", "description": "Quiz ID, required for update, delete, or getsingle."}, "title": {"type": "string", "example": "Programming Basics Quiz", "description": "Quiz title, required for createnew or update."}, "questions": {"type": "array", "items": {"type": "object", "properties": {"question": {"type": "string", "example": "What is a variable?"}, "options": {"type": "array", "items": {"type": "string", "example": "A placeholder for data."}}, "correctAnswer": {"type": "string", "example": "A placeholder for data."}}}, "description": "List of questions for the quiz, required for createnew or update."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/SuccessResponse"}, "400": {"$ref": "#/components/responses/BadRequest"}}}}}, "components": {"parameters": {"SecretComsKey": {"in": "header", "name": "SecretComsKey", "schema": {"type": "string"}, "required": true}}, "responses": {"SuccessResponse": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Operation successful"}}}}}}, "BadRequest": {"description": "Bad request", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Invalid input"}}}}}}}}}
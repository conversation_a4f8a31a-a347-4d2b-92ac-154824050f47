"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mainCourseManagementController = void 0;
const services_1 = require("../services");
// Function to handle main course management actions
const mainCourseManagementController = async (req, res) => {
    const { mode, title, description, url, id, userId, userIds, accessToken, } = req.body; // Get data from request body
    const { page = 1, limit = 10 } = req.query; // Get page and limit from query params
    try {
        if (mode === "createnew") {
            // Validate and add a new course with userIds if available
            const input = { title, description, url, userIds }; // Pass userIds here
            const newCourse = await (0, services_1.addCourse)(input, accessToken); // Pass accessToken
            return res.status(201).json({
                success: true,
                message: "Course created successfully",
                data: newCourse,
            });
        }
        else if (mode === "retrieve") {
            const pageNumber = parseInt(page, 10);
            const limitNumber = parseInt(limit, 10);
            if (isNaN(pageNumber) || isNaN(limitNumber) || pageNumber < 1 || limitNumber < 1) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid page or limit. Page and limit must be positive numbers.",
                });
            }
            const { courses, total } = await (0, services_1.getAllCourses)(pageNumber, limitNumber, accessToken); // Pass accessToken
            return res.status(200).json({
                success: true,
                message: "Courses retrieved successfully.",
                data: {
                    courses,
                    total,
                    page: pageNumber,
                    limit: limitNumber,
                },
            });
        }
        else if (mode === "getsingle") {
            if (!id) {
                return res.status(400).json({
                    success: false,
                    message: "Course ID is required to fetch a single course.",
                });
            }
            const courseWithQuizzes = await (0, services_1.getSingleCourseWithQuiz)(id, accessToken); // Pass accessToken
            if (!courseWithQuizzes) {
                return res.status(404).json({
                    success: false,
                    message: "Course not found.",
                });
            }
            // Slice the quizzes array to return only 5 random questions
            const quizzesToReturn = courseWithQuizzes.quizzes.slice(0, 5); // Ensure this gives only 5 questions after shuffling
            return res.status(200).json({
                success: true,
                message: "Course and quizzes retrieved successfully.",
                data: {
                    ...courseWithQuizzes, // Include other course data
                    quizzes: quizzesToReturn, // Return only the first 5 random quizzes
                },
            });
        }
        else if (mode === "update") {
            if (!id) {
                return res.status(400).json({
                    success: false,
                    message: "Course ID is required for update.",
                });
            }
            // Include userIds for update
            const updateInput = { id, title, description, url, userIds }; // Add userIds here
            const updatedCourse = await (0, services_1.updateCourse)(updateInput, accessToken); // Pass accessToken
            return res.status(200).json({
                success: true,
                message: "Course updated successfully.",
                data: updatedCourse,
            });
        }
        else if (mode === "delete") {
            if (!id) {
                return res.status(400).json({
                    success: false,
                    message: "Course ID is required for deletion.",
                });
            }
            await (0, services_1.deleteCourse)(id, accessToken); // Pass accessToken
            return res.status(200).json({
                success: true,
                message: "Course deleted successfully.",
            });
        }
        else if (mode === "assign") {
            // Assign course to users
            if (!id || !userIds || userIds.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: "Course ID and user IDs are required for assignment.",
                });
            }
            const result = await (0, services_1.assignCourseToUsers)(id, userIds, accessToken);
            if (!result.success) {
                return res.status(400).json(result);
            }
            return res.status(200).json(result);
        }
        else if (mode === "unassign") {
            // Unassign course from users
            if (!id || !userIds || userIds.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: "Course ID and user IDs are required for unassignment.",
                });
            }
            const result = await (0, services_1.unassignCourseFromUsers)(id, userIds, accessToken);
            if (!result.success) {
                return res.status(400).json(result);
            }
            return res.status(200).json(result);
        }
        else if (mode === "assignments") {
            // Get course assignments
            if (!id) {
                return res.status(400).json({
                    success: false,
                    message: "Course ID is required to fetch assignments.",
                });
            }
            const assignments = await (0, services_1.getCourseAssignments)(id, accessToken);
            return res.status(200).json({
                success: true,
                message: "Course assignments retrieved successfully.",
                data: assignments,
            });
        }
        else if (mode === "user-assignments") {
            // Get user assignments
            if (!userId) {
                return res.status(400).json({
                    success: false,
                    message: "User ID is required to fetch user assignments.",
                });
            }
            const assignments = await (0, services_1.getUserAssignments)(userId, accessToken);
            return res.status(200).json({
                success: true,
                message: "User assignments retrieved successfully.",
                data: assignments,
            });
        }
        else {
            return res.status(400).json({
                success: false,
                message: "Invalid mode specified.",
            });
        }
    }
    catch (error) {
        console.error("Error in mainCourseManagementController:", error);
        return res.status(500).json({
            success: false,
            message: "Failed to process request. Please try again later.",
        });
    }
};
exports.mainCourseManagementController = mainCourseManagementController;

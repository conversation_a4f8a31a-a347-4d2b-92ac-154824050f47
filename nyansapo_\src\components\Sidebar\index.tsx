"use client";

import React, { useEffect, useState, useCallback, useRef } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from 'next/image'
import {
  DashboardOutlined,
  UserOutlined,
  BookOutlined,
  FileTextOutlined,
  UserAddOutlined,
} from "@ant-design/icons";
import SidebarItem from "@/components/Sidebar/SidebarItem";

interface SidebarProps {
  sidebarOpen: boolean;
  setSidebarOpen: (arg: boolean) => void;
}

const Sidebar = ({ sidebarOpen, setSidebarOpen }: SidebarProps) => {
  const [mounted, setMounted] = useState(false);
  const [userRole, setUserRole] = useState<string>("");
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Get user role after mounting
  useEffect(() => {
    if (mounted) {
      try {
        const role = localStorage.getItem("userRole");
        setUserRole(role || "");
      } catch (error) {
        console.error("Error accessing localStorage:", error);
        setUserRole("");
      }
    }
  }, [mounted]);

  // Sidebar ref for click outside detection
  const sidebarRef = useRef<HTMLElement>(null);

  // Handle click outside using ref instead of getElementById
  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (!mounted || !sidebarOpen) return;

    const target = event.target as Element;

    // Check if target is valid and component is still mounted
    if (!target || !target.nodeType) return;

    try {
      // Use ref instead of getElementById to avoid DOM manipulation issues
      if (sidebarRef.current && !sidebarRef.current.contains(target)) {
        setSidebarOpen(false);
      }
    } catch (error) {
      // Silently handle any DOM-related errors
      console.warn('Sidebar: Click outside detection failed', error);
    }
  }, [mounted, sidebarOpen, setSidebarOpen]);

  // Add click outside listener with improved cleanup
  useEffect(() => {
    if (!mounted || !sidebarOpen) return;

    // Use a small delay to ensure DOM is ready
    const timeoutId = setTimeout(() => {
      if (mounted && sidebarOpen) {
        document.addEventListener('mousedown', handleClickOutside, { passive: true });
      }
    }, 0);

    return () => {
      clearTimeout(timeoutId);
      // Safe cleanup
      if (typeof document !== 'undefined' && document.removeEventListener) {
        document.removeEventListener('mousedown', handleClickOutside);
      }
    };
  }, [mounted, sidebarOpen, handleClickOutside]);

  // Toggle expanded items
  const toggleExpanded = useCallback((itemLabel: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemLabel)) {
        newSet.delete(itemLabel);
      } else {
        newSet.add(itemLabel);
      }
      return newSet;
    });
  }, []);

  // Don't render until mounted and user role is determined
  if (!mounted) {
    return null;
  }

  const adminMenuItems = [
    {
      icon: <DashboardOutlined />,
      label: "Dashboard",
      route: "/dashboard",
    },
    {
      icon: <UserOutlined />,
      label: "User Management",
      route: "/dashboard/user/management",
    },
    {
      icon: <UserOutlined />,
      label: "User Report",
      route: "/dashboard/user/report",
    },
    {
      icon: <BookOutlined />,
      label: "Lesson Management",
      route: "/dashboard/lesson",
    },
    {
      icon: <FileTextOutlined />,
      label: "Quiz Management",
      route: "/dashboard/quiz",
    },
    {
      icon: <UserAddOutlined />,
      label: "Course Assignments",
      route: "/dashboard/assignments",
    },
  ];

  const userMenuItems = [
    {
      icon: <DashboardOutlined />,
      label: "Home",
      route: "/user/dashboard",
    },
    {
      icon: <FileTextOutlined />,
      label: "Statistic",
      route: "/user/dashboard/statistic",
    },
    {
      icon: <BookOutlined />,
      label: "My Lessons",
      route: "/user/dashboard/lessons",
    },
  ];

  const menuItems = userRole === "admin" ? adminMenuItems : userMenuItems;

  return (
    <aside
      ref={sidebarRef}
      className={`fixed left-0 top-0 z-50 flex h-screen w-72.5 flex-col overflow-y-hidden bg-gradient-to-br from-blue-950 via-blue-900 to-blue-950 transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        sidebarOpen ? "translate-x-0" : "-translate-x-full"
      }`}
    >
      {/* SIDEBAR HEADER */}
      <div className="flex items-center justify-between gap-2 px-6 py-6 border-b border-blue-800/30">
        <Link href="/dashboard" className="flex items-center space-x-3 group">
          <div className="relative">
            <Image
              src="/images/clogo.png"
              alt="Nyansapo Logo"
              width={40}
              height={40}
              className="h-10 w-10 transition-transform duration-300 group-hover:scale-110 rounded-lg shadow-lg"
            />
            <div className="absolute inset-0 bg-blue-400/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
          <span className="text-2xl font-bold text-white tracking-wide">
            NyansaPo
          </span>
        </Link>

        <button
          onClick={() => setSidebarOpen(false)}
          aria-label="Close sidebar"
          className="block lg:hidden p-2 rounded-lg hover:bg-blue-800/50 transition-colors duration-200"
        >
          <svg
            className="fill-current text-blue-200 hover:text-white transition-colors duration-200"
            width="20"
            height="18"
            viewBox="0 0 20 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z"
              fill=""
            />
          </svg>
        </button>
      </div>

      {/* SIDEBAR MENU */}
      <div className="flex flex-col overflow-y-auto">
        <nav className="mt-6 px-4 py-4 lg:px-6">
          <div className="mb-8">
            <h3 className="mb-4 ml-4 text-xs font-bold text-blue-300 uppercase tracking-wider">
              {userRole === "admin" ? "Admin Menu" : "User Menu"}
            </h3>

            <ul className="flex flex-col gap-2">
              {menuItems.map((item, index) => (
                <SidebarItem
                  key={`${item.label}-${index}`}
                  item={item}
                  pathname={pathname}
                  expandedItems={expandedItems}
                  toggleExpanded={toggleExpanded}
                />
              ))}
            </ul>
          </div>
        </nav>
      </div>
    </aside>
  );
};

export default Sidebar;

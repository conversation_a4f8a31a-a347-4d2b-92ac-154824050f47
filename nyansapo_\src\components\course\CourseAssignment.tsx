"use client";

import React, { useState, useEffect } from "react";
import {
  Select,
  Button,
  Form,
  message,
  Spin,
  Alert,
  Table,
  Space,
  Typography,
  Card,
  Divider,
} from "antd";
import {
  UserAddOutlined,
  UserDeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import {
  useAssignCourseMutation,
  useUnassignCourseMutation,
  useGetCourseAssignmentsQuery,
  useRetrieveCourseQuery,
} from "../../reduxRTK/services/courseApi";
import { useGetUsersQuery } from "../../reduxRTK/services/authApi";

const { Option } = Select;
const { Title, Text } = Typography;

interface CourseAssignmentProps {
  onSuccess?: () => void;
}

const CourseAssignment: React.FC<CourseAssignmentProps> = ({ onSuccess }) => {
  const [form] = Form.useForm();
  const [selectedCourse, setSelectedCourse] = useState<string>("");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showAssignments, setShowAssignments] = useState(false);

  const accessToken = localStorage.getItem("token");

  // API Hooks
  const [assignCourse, { isLoading: isAssigning }] = useAssignCourseMutation();
  const [unassignCourse, { isLoading: isUnassigning }] = useUnassignCourseMutation();

  // Queries
  const { data: coursesData, isLoading: isLoadingCourses } = useRetrieveCourseQuery({
    page: 1,
    limit: 100,
    accessToken: accessToken || "",
  });

  const { data: usersData, isLoading: isLoadingUsers } = useGetUsersQuery({
    page: 1,
    limit: 100,
    accessToken: accessToken || "",
  });

  const {
    data: assignmentsData,
    isLoading: isLoadingAssignments,
    error: assignmentsError,
    refetch: refetchAssignments,
  } = useGetCourseAssignmentsQuery(
    { courseId: selectedCourse, accessToken: accessToken || "" },
    {
      skip: !selectedCourse,
      refetchOnMountOrArgChange: true,
      refetchOnFocus: true
    }
  );

  // Handle course assignment
  const handleAssign = async () => {
    if (!selectedCourse || selectedUsers.length === 0) {
      message.error("Please select a course and at least one user");
      return;
    }

    try {
      const result = await assignCourse({
        courseId: selectedCourse,
        userIds: selectedUsers,
        accessToken: accessToken || "",
      }).unwrap();

      if (result.success) {
        message.success(result.message);
        setSelectedUsers([]);
        form.resetFields(["userIds"]);
        refetchAssignments();
        onSuccess?.();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      message.error(error.message || "Failed to assign course");
    }
  };

  // Handle course unassignment
  const handleUnassign = async (userIds: string[]) => {
    if (!selectedCourse || userIds.length === 0) {
      message.error("Please select users to unassign");
      return;
    }

    try {
      const result = await unassignCourse({
        courseId: selectedCourse,
        userIds,
        accessToken: accessToken || "",
      }).unwrap();

      if (result.success) {
        message.success(result.message);
        refetchAssignments();
        onSuccess?.();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      message.error(error.message || "Failed to unassign course");
    }
  };

  // Table columns for assignments
  const columns = [
    {
      title: "Name",
      dataIndex: "firstName",
      key: "name",
      render: (firstName: string, record: any) => `${firstName} ${record.lastName}`,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Assigned Date",
      dataIndex: "assignedAt",
      key: "assignedAt",
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: "Actions",
      key: "actions",
      render: (record: any) => (
        <Button
          type="link"
          danger
          icon={<UserDeleteOutlined />}
          onClick={() => handleUnassign([record.id])}
          loading={isUnassigning}
        >
          Unassign
        </Button>
      ),
    },
  ];

  // Get available users (exclude already assigned ones)
  const getAvailableUsers = () => {
    if (!usersData?.data?.users || !assignmentsData?.data) {
      return usersData?.data?.users || [];
    }

    // The assignment data contains user information, so we use the user id from assignments
    const assignedUserIds = assignmentsData.data.map((assignment) => assignment.id);
    return usersData.data.users.filter((user: any) => !assignedUserIds.includes(user.id));
  };

  const availableUsers = getAvailableUsers();

  return (
    <div className="p-4">
      <Card>
        <Title level={3}>Course Assignment Management</Title>
        <Text type="secondary">
          Assign courses to users and manage existing assignments
        </Text>

        <Divider />

        <Form form={form} layout="vertical" className="mb-6">
          <Form.Item
            label="Select Course"
            name="courseId"
            rules={[{ required: true, message: "Please select a course" }]}
          >
            <Select
              placeholder="Choose a course to manage assignments"
              loading={isLoadingCourses}
              onChange={(value) => {
                setSelectedCourse(value);
                setShowAssignments(true);
                setSelectedUsers([]);
                form.resetFields(["userIds"]);
              }}
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)
                  ?.toLowerCase()
                  ?.includes(input.toLowerCase())
              }
            >
              {coursesData?.data?.courses?.map((course: any) => (
                <Option key={course.id} value={course.id}>
                  {course.title}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {selectedCourse && (
            <>
              <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                <Text strong>Course: </Text>
                <Text>{coursesData?.data?.courses?.find(c => c.id === selectedCourse)?.title}</Text>
                <br />
                <Text strong>Currently Assigned: </Text>
                <Text>{assignmentsData?.data?.length || 0} users</Text>
                <br />
                <Text strong>Available for Assignment: </Text>
                <Text>{availableUsers.length} users</Text>
                <br />
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetchAssignments()}
                  size="small"
                  type="link"
                  loading={isLoadingAssignments}
                >
                  Refresh Assignments
                </Button>
                {assignmentsError && (
                  <>
                    <br />
                    <Text type="danger">Error loading assignments: {(assignmentsError as any)?.message || 'Unknown error'}</Text>
                  </>
                )}
              </div>

              <Form.Item
                label="Select Users to Assign"
                name="userIds"
                help={availableUsers.length === 0 ?
                  "All users are already assigned to this course" :
                  `${availableUsers.length} users available for assignment`
                }
              >
                <Select
                  mode="multiple"
                  placeholder={availableUsers.length === 0 ?
                    "No users available for assignment" :
                    "Choose users to assign to this course"
                  }
                  loading={isLoadingUsers}
                  value={selectedUsers}
                  onChange={setSelectedUsers}
                  disabled={availableUsers.length === 0}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)
                      ?.toLowerCase()
                      ?.includes(input.toLowerCase())
                  }
                >
                  {availableUsers.map((user: any) => (
                    <Option key={user.id} value={user.id}>
                      {user.firstName} {user.lastName} ({user.email})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </>
          )}

          {selectedCourse && (
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  icon={<UserAddOutlined />}
                  onClick={handleAssign}
                  loading={isAssigning}
                  disabled={selectedUsers.length === 0}
                >
                  Assign Course ({selectedUsers.length} users)
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetchAssignments()}
                  loading={isLoadingAssignments}
                >
                  Refresh
                </Button>
              </Space>
            </Form.Item>
          )}
        </Form>

        {showAssignments && selectedCourse && (
          <>
            <Divider />
            <div className="mb-4">
              <Title level={4}>
                <EyeOutlined /> Current Assignments
              </Title>
              <Text type="secondary">
                Users currently assigned to this course
              </Text>
            </div>

            {isLoadingAssignments ? (
              <div className="flex justify-center items-center py-8">
                <Spin size="large" tip="Loading assignments..." />
              </div>
            ) : assignmentsData?.data?.length === 0 ? (
              <Alert
                message="No assignments found"
                description="This course has not been assigned to any users yet."
                type="info"
                showIcon
              />
            ) : (
              <Table
                columns={columns}
                dataSource={assignmentsData?.data || []}
                rowKey="id"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showTotal: (total) => `Total ${total} assignments`,
                }}
              />
            )}
          </>
        )}
      </Card>
    </div>
  );
};

export default CourseAssignment;

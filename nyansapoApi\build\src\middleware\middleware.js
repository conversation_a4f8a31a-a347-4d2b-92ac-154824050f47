"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticate = void 0;
const jwtService_1 = require("../services/jwtService");
const authenticate = async (req, res, next) => {
    const { accessToken: token } = req.body; // Retrieve access token from the request body
    const refreshToken = req.body.refreshToken; // Retrieve the refresh token from the request body
    if (!token) {
        // If access token is missing, send an error and stop execution
        res.status(401).json({ message: 'Access token is missing' });
        return; // Stop further execution after sending the response
    }
    try {
        // Try to verify the access token
        const decoded = (0, jwtService_1.verifyToken)(token, 'access');
        req.user = decoded; // Attach decoded user to the request object
        next(); // Proceed to the next middleware/route handler
    }
    catch (error) {
        // If access token is expired or invalid, check for refresh token
        if (error.name === 'TokenExpiredError' && refreshToken) {
            try {
                // Verify the refresh token
                const decodedRefresh = (0, jwtService_1.verifyToken)(refreshToken, 'refresh'); // Verify the refresh token
                // If refresh token is valid, allow access without generating a new access token
                req.user = decodedRefresh; // Attach the decoded refresh token user to the request
                next(); // Proceed to the next middleware/route handler
            }
            catch (refreshError) {
                // If refresh token is invalid, send an error and stop execution
                res.status(401).json({ message: 'Invalid or expired refresh token' });
                return; // Stop further execution after sending the response
            }
        }
        // If no valid token or refresh token, send an unauthorized error
        res.status(401).json({ message: 'Invalid or expired access token and no valid refresh token' });
    }
};
exports.authenticate = authenticate;

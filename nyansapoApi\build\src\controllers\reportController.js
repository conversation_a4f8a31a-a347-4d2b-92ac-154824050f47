"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mainReportManagementController = void 0;
const services_1 = require("../services"); // Import report services
const mainReportManagementController = async (req, res) => {
    const { mode, id, userId, courseId, completed, score, accessToken, } = req.body; // Extract mode and report details from the request body
    try {
        // Ensure accessToken is present and a string
        if (!accessToken || typeof accessToken !== "string") {
            return res.status(401).json({
                success: false,
                message: "Access token is required in the request body.",
            });
        }
        if (mode === "createnew") {
            // Create a new report
            const input = { userId, courseId, completed, score }; // Prepare input for report creation
            const newReport = await (0, services_1.addReport)(input, accessToken); // Pass accessToken to service
            return res.status(201).json({
                success: true,
                message: "Report created successfully.",
                data: newReport,
            });
        }
        else if (mode === "upsert") {
            // Create or update a report (upsert functionality)
            const input = { userId, courseId, completed, score }; // Prepare input for report upsert
            const report = await (0, services_1.upsertReport)(input, accessToken); // Pass accessToken to service
            return res.status(200).json({
                success: true,
                message: "Report created or updated successfully.",
                data: report,
            });
        }
        else if (mode === "retrieve") {
            // Retrieve reports with optional courseId and userId filters
            const { page = 1, limit = 10 } = req.query;
            const pageNumber = parseInt(page, 10);
            const limitNumber = parseInt(limit, 10);
            // Validate pagination input
            if (isNaN(pageNumber) || isNaN(limitNumber) || pageNumber < 1 || limitNumber < 1) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid page or limit. Both must be positive numbers.",
                });
            }
            // Call service to retrieve reports
            const { reports, total } = await (0, services_1.getReports)(accessToken, courseId, userId, pageNumber, limitNumber);
            return res.status(200).json({
                success: true,
                message: "Reports retrieved successfully.",
                data: {
                    reports,
                    total,
                    page: pageNumber,
                    limit: limitNumber,
                },
            });
        }
        else if (mode === "update") {
            // Update a report
            if (!id) {
                return res.status(400).json({
                    success: false,
                    message: "Report ID is required for update.",
                });
            }
            // Prepare input for report update
            const input = { id, userId, courseId, completed, score };
            const updatedReport = await (0, services_1.updateReport)(input, accessToken); // Pass accessToken to service
            return res.status(200).json({
                success: true,
                message: "Report updated successfully.",
                data: updatedReport,
            });
        }
        else if (mode === "delete") {
            // Delete a report
            if (!id) {
                return res.status(400).json({
                    success: false,
                    message: "Report ID is required for deletion.",
                });
            }
            // Call service to delete report
            const isDeleted = await (0, services_1.deleteReport)(id, accessToken); // Pass accessToken to service
            return res.status(200).json({
                success: isDeleted,
                message: isDeleted
                    ? "Report deleted successfully."
                    : "Report deletion failed.",
            });
        }
        else if (mode === "reset") {
            // Reset a course to "NEW" state by deleting its report
            if (!userId || !courseId) {
                return res.status(400).json({
                    success: false,
                    message: "User ID and Course ID are required for reset.",
                });
            }
            const isDeleted = await (0, services_1.deleteReportByUserAndCourse)(userId, courseId, accessToken);
            return res.status(200).json({
                success: isDeleted,
                message: isDeleted
                    ? "Course reset to NEW state successfully."
                    : "Course reset failed.",
            });
        }
        else {
            // Handle invalid mode
            return res.status(400).json({
                success: false,
                message: "Invalid mode specified. Supported modes: createnew, retrieve, update, delete.",
            });
        }
    }
    catch (error) {
        console.error("Error in mainReportManagementController:", error);
        return res.status(500).json({
            success: false,
            message: "Failed to process request. Please try again later.",
        });
    }
};
exports.mainReportManagementController = mainReportManagementController;

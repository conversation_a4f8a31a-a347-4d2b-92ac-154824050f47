{"name": "n<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "watch": "nodemon", "start": "node build/index.js", "dev": "ts-node-dev --respawn --transpile-only index.ts", "build": "tsc", "migrate": "node-pg-migrate create", "migrate:up": "node-pg-migrate up", "migrate:down": "node-pg-migrate down", "generate:types": "kysely-codegen --out-file ./src/db/types.ts", "vercel-build": "echo hello"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/bcrypt": "^5", "@types/cookie-parser": "^1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.12.12", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.15", "@types/pg": "^8.11.6", "@types/swagger-ui-express": "^4.1.7", "@types/uuid": "^9.0.8", "cross-env": "^7.0.3", "kysely-codegen": "^0.18.5", "node-pg-migrate": "^8.0.3", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "dependencies": {"bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "envalid": "^8.0.0", "express": "^4.19.2", "express-rate-limit": "^7.2.0", "helmet": "^7.1.0", "isomorphic-dompurify": "^2.25.0", "jsonwebtoken": "^9.0.2", "kysely": "^0.27.3", "mysql2": "^3.12.0", "node-cron": "^3.0.3", "node-windows": "^1.0.0-beta.8", "nodemailer": "^6.9.16", "nodemon": "^3.1.1", "pg": "^8.11.5", "prisma": "^5.21.1", "prisma-kysely": "^1.8.0", "swagger-ui-express": "^5.0.1", "xss-clean": "^0.1.4", "zod": "^3.23.8"}}
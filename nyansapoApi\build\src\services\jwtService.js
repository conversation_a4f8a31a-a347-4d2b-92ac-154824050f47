"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshAccessToken = exports.verifyToken = exports.generateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const ACCESS_TOKEN_EXPIRATION = '7d';
const REFRESH_TOKEN_EXPIRATION = '7d';
const generateToken = (user, type) => {
    const secretKey = type === 'access' ? process.env.JWT_SECRET : process.env.REFRESH_SECRET;
    if (!secretKey) {
        throw new Error(`${type} secret key is not defined in environment variables.`);
    }
    const expiresIn = type === 'access' ? ACCESS_TOKEN_EXPIRATION : REFRESH_TOKEN_EXPIRATION;
    return jsonwebtoken_1.default.sign({ id: user.id, email: user.email, role: user.role }, secretKey, { expiresIn });
};
exports.generateToken = generateToken;
const verifyToken = (token, type) => {
    const secretKey = type === 'access' ? process.env.JWT_SECRET : process.env.REFRESH_SECRET;
    if (!secretKey) {
        throw new Error(`${type} secret key is not defined in environment variables.`);
    }
    try {
        // Verify the token and decode the payload into the DecodedToken interface
        const decoded = jsonwebtoken_1.default.verify(token, secretKey);
        return decoded;
    }
    catch (error) {
        throw new Error('Invalid or expired token');
    }
};
exports.verifyToken = verifyToken;
const refreshAccessToken = (refreshToken) => {
    const decoded = (0, exports.verifyToken)(refreshToken, 'refresh');
    // Generate a new access token using the data from the refresh token
    const newAccessToken = (0, exports.generateToken)({
        id: decoded.id,
        email: decoded.email,
        role: decoded.role,
    }, 'access');
    return newAccessToken;
};
exports.refreshAccessToken = refreshAccessToken;

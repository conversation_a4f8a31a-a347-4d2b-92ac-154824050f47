"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const helmet_1 = __importDefault(require("helmet"));
const cookie_parser_1 = __importDefault(require("cookie-parser")); // Import cookie-parser
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const routes_1 = __importDefault(require("./src/routes/routes"));
const swagger_json_1 = __importDefault(require("./src/docs/swagger.json"));
dotenv_1.default.config();
const app = (0, express_1.default)();
const port = process.env.PORT || 5051;
// Enable trust proxy for secure cookies
app.set('trust proxy', 1);
// Rate limiter 
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per window
});
// CORS configuration
const corsOptions = {
    origin: [
        'http://localhost:3000',
        'https://nyansapo.vercel.app',
        'http://nyansa-po.com',
        'https://nyansa-po.com'
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE'], // Allow specific methods
    allowedHeaders: [
        'Authorization', // Allow Authorization header
        'X-User-Role', // Allow X-User-Role header
        'Content-Type', // Allow Content-Type header
    ],
    credentials: true, // Allow credentials (cookies, authorization headers, etc.)
};
app.use((0, cors_1.default)(corsOptions));
app.options('*', (0, cors_1.default)(corsOptions));
app.use((0, cookie_parser_1.default)()); // Add cookie-parser middleware
app.use(express_1.default.json());
app.use((0, helmet_1.default)());
app.use(limiter);
// Swagger setup
app.use('/api-docs', swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swagger_json_1.default));
// Routes
app.use('/api', routes_1.default);
// Root route
app.get('/', (req, res) => {
    res.send({ message: 'Welcome to the API' });
});
// Start the server
app.listen(port, () => {
    console.log(`Server is running at http://localhost:${port}`);
    console.log(`Swagger documentation available at http://localhost:${port}/api-docs`);
});
exports.default = app;

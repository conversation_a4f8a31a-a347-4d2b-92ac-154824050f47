"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getReportsByUserId = exports.upsertReport = exports.deleteReportByUserAndCourse = exports.deleteReport = exports.updateReport = exports.getReports = exports.addReport = void 0;
const db_1 = require("../db/db");
const uuid_1 = require("uuid");
const schema_1 = require("../schema");
/**
 * Service to add a new report
 */
const addReport = async (input, accessToken) => {
    const parsedInput = schema_1.addReportSchema.safeParse(input);
    if (!parsedInput.success) {
        throw new Error("Invalid input: " +
            parsedInput.error.errors.map((err) => err.message).join(", "));
    }
    const reportId = (0, uuid_1.v4)();
    try {
        const [newReport] = await db_1.db
            .insertInto("Report")
            .values({
            id: reportId,
            userId: parsedInput.data.userId,
            courseId: parsedInput.data.courseId,
            completed: parsedInput.data.completed ?? false,
            score: parsedInput.data.score ?? null,
            createdAt: new Date(),
            updatedAt: new Date(),
        })
            .returning([
            "id",
            "userId",
            "courseId",
            "completed",
            "score",
            "createdAt",
            "updatedAt",
        ])
            .execute();
        console.log("Request Body:", {
            report: {
                id: reportId,
                userId: parsedInput.data.userId,
                courseId: parsedInput.data.courseId,
                completed: parsedInput.data.completed,
                score: parsedInput.data.score,
            },
        });
        return newReport;
    }
    catch (error) {
        console.error("Error adding report:", error);
        throw new Error("Unable to add report. Please try again later.");
    }
};
exports.addReport = addReport;
/**
 * Service to fetch reports
 */
// export const getReports = async (
//   accessToken: string,
//   courseId?: string,
//   userId?: string,
//   page: number = 1,
//   limit: number = 10
// ): Promise<{ reports: DB["Report"][]; total: number }> => {
//   try {
//     if (page < 1 || limit < 1) {
//       throw new Error("Page and limit must be greater than 0.");
//     }
//     let query = db
//       .selectFrom("Report")
//       .select([
//         "id",
//         "userId",
//         "courseId",
//         "completed",
//         "score",
//         "createdAt",
//         "updatedAt",
//       ])
//       .orderBy("createdAt", "desc")
//       .offset((page - 1) * limit)
//       .limit(limit);
//     if (courseId) query = query.where("courseId", "=", courseId);
//     if (userId) query = query.where("userId", "=", userId);
//     const reports = await query.execute();
//     let totalQuery = db.selectFrom("Report").select([db.fn.count("id").as("total")]);
//     if (courseId) totalQuery = totalQuery.where("courseId", "=", courseId);
//     if (userId) totalQuery = totalQuery.where("userId", "=", userId);
//     const totalResult = await totalQuery.execute();
//     const total = Number(totalResult[0].total);
//     console.log("Request Body:", {
//       accessToken,
//       courseId,
//       userId,
//       page,
//       limit,
//     });
//     return {
//       reports: reports as unknown as DB["Report"][],
//       total,
//     };
//   } catch (error) {
//     console.error("Error fetching reports:", error);
//     throw new Error("Unable to fetch reports. Please try again later.");
//   }
// };
const getReports = async (accessToken, courseId, userId, page = 1, limit = 10) => {
    try {
        if (page < 1 || limit < 1) {
            throw new Error("Page and limit must be greater than 0.");
        }
        let query = db_1.db
            .selectFrom("Report")
            .innerJoin("User", "Report.userId", "User.id") // Join with User table
            .innerJoin("Course", "Report.courseId", "Course.id") // Join with Course table
            .select([
            "Report.id",
            "Report.userId",
            "Report.courseId",
            "Report.completed",
            "Report.score",
            "Report.createdAt",
            "Report.updatedAt",
            "User.id as userId",
            "User.email as userEmail",
            "User.firstName as userFirstName",
            "User.lastName as userLastName",
            "User.role as userRole",
            "Course.id as courseId",
            "Course.title as courseTitle",
            "Course.description as courseDescription",
            "Course.url as courseUrl",
            "Course.createdAt as courseCreatedAt",
            "Course.updatedAt as courseUpdatedAt",
        ])
            .orderBy("Report.createdAt", "desc")
            .offset((page - 1) * limit)
            .limit(limit);
        if (courseId)
            query = query.where("Report.courseId", "=", courseId);
        if (userId)
            query = query.where("Report.userId", "=", userId);
        const reports = await query.execute();
        let totalQuery = db_1.db.selectFrom("Report").select([db_1.db.fn.count("id").as("total")]);
        if (courseId)
            totalQuery = totalQuery.where("courseId", "=", courseId);
        if (userId)
            totalQuery = totalQuery.where("userId", "=", userId);
        const totalResult = await totalQuery.execute();
        const total = Number(totalResult[0].total);
        console.log("Request Body:", {
            accessToken,
            courseId,
            userId,
            page,
            limit,
        });
        const detailedReports = reports.map((report) => ({
            id: report.id,
            userId: report.userId,
            courseId: report.courseId,
            completed: report.completed,
            score: report.score,
            createdAt: report.createdAt,
            updatedAt: report.updatedAt,
            user: {
                id: report.userId,
                email: report.userEmail,
                firstName: report.userFirstName,
                lastName: report.userLastName,
                role: report.userRole,
            },
            course: {
                id: report.courseId,
                title: report.courseTitle,
                description: report.courseDescription,
                url: report.courseUrl,
                createdAt: report.courseCreatedAt,
                updatedAt: report.courseUpdatedAt,
            },
        }));
        return {
            reports: detailedReports,
            total,
        };
    }
    catch (error) {
        console.error("Error fetching reports:", error);
        throw new Error("Unable to fetch reports. Please try again later.");
    }
};
exports.getReports = getReports;
/**
 * Service to update a report
 */
const updateReport = async (input, accessToken) => {
    const parsedInput = schema_1.updateReportSchema.safeParse(input);
    if (!parsedInput.success) {
        throw new Error("Invalid input: " +
            parsedInput.error.errors.map((err) => err.message).join(", "));
    }
    const { id, userId, courseId, completed, score } = parsedInput.data;
    try {
        const existingReport = await db_1.db
            .selectFrom("Report")
            .selectAll()
            .where("id", "=", id)
            .executeTakeFirst();
        if (!existingReport) {
            throw new Error(`Report with ID ${id} does not exist.`);
        }
        const [updatedReport] = await db_1.db
            .updateTable("Report")
            .set({
            userId: userId || existingReport.userId,
            courseId: courseId || existingReport.courseId,
            completed: completed !== undefined ? completed : existingReport.completed,
            score: score !== undefined ? score : existingReport.score,
            updatedAt: new Date(),
        })
            .where("id", "=", id)
            .returning([
            "id",
            "userId",
            "courseId",
            "completed",
            "score",
            "createdAt",
            "updatedAt",
        ])
            .execute();
        console.log("Request Body:", {
            accessToken,
            id,
            userId,
            courseId,
            completed,
            score,
        });
        return updatedReport;
    }
    catch (error) {
        console.error("Error updating report:", error);
        throw new Error("Unable to update report. Please try again later.");
    }
};
exports.updateReport = updateReport;
/**
 * Service to delete a report
 */
const deleteReport = async (reportId, accessToken) => {
    try {
        const result = await db_1.db.deleteFrom("Report").where("id", "=", reportId).execute();
        if (result.length === 0) {
            throw new Error("Report not found.");
        }
        console.log("Request Body:", {
            accessToken,
            reportId,
        });
        return true;
    }
    catch (error) {
        console.error("Error deleting report:", error);
        throw new Error("Unable to delete report. Please try again later.");
    }
};
exports.deleteReport = deleteReport;
// New function to delete report by userId and courseId (for testing/reset purposes)
const deleteReportByUserAndCourse = async (userId, courseId, accessToken) => {
    try {
        if (!userId || !courseId) {
            throw new Error("User ID and Course ID are required to delete the report.");
        }
        const result = await db_1.db
            .deleteFrom("Report")
            .where("userId", "=", userId)
            .where("courseId", "=", courseId)
            .execute();
        console.log(`Deleted ${result.length} report(s) for user ${userId} and course ${courseId}`);
        console.log("Request Body:", { accessToken, userId, courseId });
        return true;
    }
    catch (error) {
        console.error("Error deleting report by user and course:", error);
        throw new Error("Unable to delete report. Please try again later.");
    }
};
exports.deleteReportByUserAndCourse = deleteReportByUserAndCourse;
/**
 * Service to create or update a report (upsert functionality)
 * If a report exists for the user-course combination, update it
 * Otherwise, create a new report
 */
const upsertReport = async (input, accessToken) => {
    const parsedInput = schema_1.addReportSchema.safeParse(input);
    if (!parsedInput.success) {
        throw new Error("Invalid input: " +
            parsedInput.error.errors.map((err) => err.message).join(", "));
    }
    try {
        // Check if a report already exists for this user-course combination
        const existingReport = await db_1.db
            .selectFrom("Report")
            .selectAll()
            .where("userId", "=", parsedInput.data.userId)
            .where("courseId", "=", parsedInput.data.courseId)
            .executeTakeFirst();
        if (existingReport) {
            // Update existing report only if it's not completed and we're not trying to mark it as completed
            // This prevents overwriting quiz results with "started" status
            if (!existingReport.completed && !parsedInput.data.completed) {
                const [updatedReport] = await db_1.db
                    .updateTable("Report")
                    .set({
                    completed: parsedInput.data.completed ?? existingReport.completed,
                    score: parsedInput.data.score ?? existingReport.score,
                    updatedAt: new Date(),
                })
                    .where("id", "=", existingReport.id)
                    .returning([
                    "id",
                    "userId",
                    "courseId",
                    "completed",
                    "score",
                    "createdAt",
                    "updatedAt",
                ])
                    .execute();
                console.log("Updated existing report:", updatedReport);
                return updatedReport;
            }
            else {
                // Return existing report without changes
                console.log("Report already exists and is completed or being completed:", existingReport);
                return existingReport;
            }
        }
        else {
            // Create new report
            const reportId = (0, uuid_1.v4)();
            const [newReport] = await db_1.db
                .insertInto("Report")
                .values({
                id: reportId,
                userId: parsedInput.data.userId,
                courseId: parsedInput.data.courseId,
                completed: parsedInput.data.completed ?? false,
                score: parsedInput.data.score ?? null,
                createdAt: new Date(),
                updatedAt: new Date(),
            })
                .returning([
                "id",
                "userId",
                "courseId",
                "completed",
                "score",
                "createdAt",
                "updatedAt",
            ])
                .execute();
            console.log("Created new report:", newReport);
            return newReport;
        }
    }
    catch (error) {
        console.error("Error upserting report:", error);
        throw new Error("Unable to create or update report. Please try again later.");
    }
};
exports.upsertReport = upsertReport;
/**
 * Service to fetch reports by user ID
 */
const getReportsByUserId = async (userId, accessToken) => {
    try {
        if (!userId) {
            throw new Error("User ID is required.");
        }
        const reports = await db_1.db
            .selectFrom("Report")
            .select([
            "id",
            "userId",
            "courseId",
            "completed",
            "score",
            "createdAt",
            "updatedAt",
        ])
            .where("userId", "=", userId)
            .orderBy("createdAt", "desc")
            .execute();
        console.log("Request Body:", {
            accessToken,
            userId,
        });
        return reports;
    }
    catch (error) {
        console.error("Error fetching reports by user ID:", error);
        throw new Error("Unable to fetch reports. Please try again later.");
    }
};
exports.getReportsByUserId = getReportsByUserId;

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateReportSchema = exports.addReportSchema = void 0;
const zod_1 = require("zod");
// Schema for adding a new Report
exports.addReportSchema = zod_1.z.object({
    userId: zod_1.z.string().uuid({ message: "User ID must be a valid UUID" }),
    courseId: zod_1.z.string().uuid({ message: "Course ID must be a valid UUID" }),
    completed: zod_1.z.boolean().optional().default(false), // Optional, defaults to false
    score: zod_1.z.number().optional().nullable(), // Optional and nullable
});
// Schema for updating an existing Report
exports.updateReportSchema = zod_1.z.object({
    id: zod_1.z.string().uuid({ message: "Report ID must be a valid UUID" }),
    userId: zod_1.z.string().uuid({ message: "User ID must be a valid UUID" }).optional(),
    courseId: zod_1.z.string().uuid({ message: "Course ID must be a valid UUID" }).optional(),
    completed: zod_1.z.boolean().optional(), // Optional boolean
    score: zod_1.z.number().optional().nullable(), // Optional and nullable number
});

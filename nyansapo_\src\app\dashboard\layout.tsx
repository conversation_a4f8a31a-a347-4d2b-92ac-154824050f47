"use client";
import "jsvectormap/dist/jsvectormap.css";
import "flatpickr/dist/flatpickr.min.css";
import "@/css/satoshi.css";
import "@/css/style.css";

import React, { useEffect, useState } from "react";
import { Spin, Button, Alert, Badge } from 'antd'; // Import Badge and Alert from Ant Design
import { AntdRegistry } from '@ant-design/nextjs-registry';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [hydrated, setHydrated] = React.useState(false);
  React.useEffect(() => {
    setHydrated(true);
  }, []);
  if (!hydrated) return null;

  // All other hooks and logic go here, after hydration is confirmed
  const [loading, setLoading] = React.useState<boolean>(true);
  const [loadingAuth, setLoadingAuth] = React.useState<boolean>(true);
  const [isAuthorized, setIsAuthorized] = React.useState<boolean>(true); // Assuming authorized by default

  React.useEffect(() => {
    const token = localStorage.getItem("token");
    const userRole = localStorage.getItem("userRole");
    if (!token || userRole !== "admin") {
      setIsAuthorized(false);
    }
    setTimeout(() => setLoading(false), 1000);
  }, []);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setLoadingAuth(false);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  if (loadingAuth || loading) {
    return (
      <div className="flex justify-center items-center h-screen w-full">
        <Spin size="large" />
      </div>
    );
  }

  const handleRedirect = () => {
    window.location.href = "/";
  };

  if (!isAuthorized) {
    return (
      <div className="flex justify-center items-center h-screen w-full flex-col">
        <Badge.Ribbon text="Unauthorized" color="red">
          <Alert message="You are not authorized. Please log in." type="error" showIcon />
        </Badge.Ribbon>
        <Button type="primary" onClick={handleRedirect} className="mt-4">
          Go to Login
        </Button>
      </div>
    );
  }

  return (
    <html lang="en">
      <body>
        <div className="dark:bg-boxdark-2 dark:text-bodydark">
          <AntdRegistry>{children}</AntdRegistry>
        </div>
      </body>
    </html>
  );
}

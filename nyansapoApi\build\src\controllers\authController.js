"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetPasswordController = exports.sendDetailsController = exports.deleteUserController = exports.checkAuthController = exports.LoginController = exports.CreateUserController = void 0;
const services_1 = require("../services");
// User creation controller
const CreateUserController = async (req, res) => {
    try {
        const { mode, users } = req.body;
        // Validate the mode
        if (mode !== "createnew") {
            return res.status(400).json({
                success: false,
                message: "Invalid mode for this operation.",
            });
        }
        // Validate the users input
        if (!Array.isArray(users) || users.length === 0) {
            return res.status(400).json({
                success: false,
                message: "Users data must be a non-empty array.",
            });
        }
        // Call the service to create users
        const createdUsers = await (0, services_1.createUser)(users);
        return res.status(201).json({
            success: true,
            message: "Users created successfully.",
            data: createdUsers,
        });
    }
    catch (error) {
        console.error("Error during user creation:", error.message);
        return res.status(500).json({
            success: false,
            message: error.message || "Internal server error.",
        });
    }
};
exports.CreateUserController = CreateUserController;
const LoginController = async (req, res) => {
    try {
        const { email, password } = req.body;
        if (!email || !password) {
            return res.status(400).json({
                success: false,
                message: "Email and password are required.",
            });
        }
        // Call the updated loginUser function
        const { user, accessToken, refreshToken } = await (0, services_1.loginUser)({ email, password }, res);
        return res.status(200).json({
            success: true,
            message: "Login successful.",
            data: { user, accessToken, refreshToken },
        });
    }
    catch (error) {
        console.error("Error during login:", error.message);
        // Handle known errors
        if (error.message.includes("Email not found") ||
            error.message.includes("Invalid password")) {
            return res.status(401).json({
                success: false,
                message: error.message,
            });
        }
        // Handle other errors as general server errors
        return res.status(500).json({
            success: false,
            message: "Internal server error. Please try again later.",
        });
    }
};
exports.LoginController = LoginController;
// Controller to check if the user is logged in
const checkAuthController = async (req, res) => {
    const { mode } = req.body;
    if (mode !== "checkauth") {
        return res.status(400).json({
            success: false,
            message: "Invalid mode for this operation. Mode must be 'checkauth'.",
        });
    }
    try {
        const sessionId = req.signedCookies.sessionId;
        if (!sessionId) {
            return res.status(401).json({
                success: false,
                message: "Session expired or not authenticated.",
            });
        }
        return res.status(200).json({
            success: true,
            message: "User is authenticated",
            sessionId,
        });
    }
    catch (error) {
        console.error("Error in checking authentication:", error);
        return res.status(500).json({
            success: false,
            message: error.message || "Internal Server Error",
        });
    }
};
exports.checkAuthController = checkAuthController;
// Delete user controller
const deleteUserController = async (req, res) => {
    const { mode, userId, accessToken } = req.body; // Extract accessToken from request body
    if (mode !== "delete") {
        return res.status(400).json({
            success: false,
            message: "Invalid mode for this operation. Mode must be 'delete'.",
        });
    }
    if (!userId) {
        return res.status(400).json({
            success: false,
            message: "User ID is required for deletion.",
        });
    }
    try {
        // Pass accessToken to deleteUser service
        await (0, services_1.deleteUser)(userId, accessToken);
        return res.status(200).json({
            success: true,
            message: "User deleted successfully.",
        });
    }
    catch (error) {
        console.error("Error during user deletion:", error);
        return res.status(500).json({
            success: false,
            message: error.message || "Internal server error.",
        });
    }
};
exports.deleteUserController = deleteUserController;
//Controller to send user details and reset password link
const sendDetailsController = async (req, res) => {
    const { mode, userId, accessToken } = req.body;
    // Validate the mode
    if (mode !== "senddetails") {
        return res.status(400).json({
            success: false,
            message: "Invalid mode for this operation. Mode must be 'senddetails'.",
        });
    }
    // Validate the userId
    if (!userId) {
        return res.status(400).json({
            success: false,
            message: "User ID is required.",
        });
    }
    try {
        // Call the service to send the details
        await (0, services_1.sendDetails)(userId, accessToken);
        return res.status(200).json({
            success: true,
            message: "Account details and password reset link sent successfully.",
        });
    }
    catch (error) {
        console.error("Error during sending account details:", error.message);
        return res.status(500).json({
            success: false,
            message: error.message || "Unable to send account details. Please try again later.",
        });
    }
};
exports.sendDetailsController = sendDetailsController;
// export const sendDetailsController = async (
//   req: Request,
//   res: Response
// ): Promise<Response> => {
//   const { mode, userIds, accessToken } = req.body;
//   // Validate the mode
//   if (mode !== "senddetails" && mode !== "sendbulkdetails") {
//     return res.status(400).json({
//       success: false,
//       message: "Invalid mode for this operation. Mode must be 'senddetails' or 'sendbulkdetails'.",
//     });
//   }
//   // Validate userIds if mode is 'senddetails' (Single or Multiple Users)
//   if (mode === "senddetails") {
//     if (!Array.isArray(userIds) || userIds.length === 0) {
//       return res.status(400).json({
//         success: false,
//         message: "An array of user IDs is required for sending details to specific users.",
//       });
//     }
//   }
//   try {
//     // Call the service function with userIds (null for bulk sending)
//     await sendDetails(mode === "senddetails" ? userIds : null, accessToken);
//     return res.status(200).json({
//       success: true,
//       message:
//         mode === "senddetails"
//           ? "Account details and password reset link sent successfully to specified users."
//           : "Account details sent to all users successfully.",
//     });
//   } catch (error: any) {
//     console.error("Error during sending account details:", error.message);
//     return res.status(500).json({
//       success: false,
//       message: error.message || "Unable to send account details. Please try again later.",
//     });
//   }
// };
const resetPasswordController = async (req, res) => {
    const { userId, accessToken } = req.body;
    // Validate the request data
    if (!userId || !accessToken) {
        return res.status(400).json({
            success: false,
            message: "User ID and access token are required.",
        });
    }
    try {
        // Get user details using the service
        const user = await (0, services_1.getUserDetailsFromToken)(userId, accessToken);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "User not found or token invalid.",
            });
        }
        // Return user details
        return res.status(200).json({
            success: true,
            message: "User verified successfully.",
            data: user,
        });
    }
    catch (error) {
        console.error("Error verifying user:", error.message);
        return res.status(400).json({
            success: false,
            message: error.message || "Failed to verify user.",
        });
    }
};
exports.resetPasswordController = resetPasswordController;

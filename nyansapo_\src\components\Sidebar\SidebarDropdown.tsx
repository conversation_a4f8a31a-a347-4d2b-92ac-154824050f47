import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

const SidebarDropdown = ({ item }: any) => {
  const pathname = usePathname();

  return (
    <>
      <ul className="mb-5.5 mt-2 flex flex-col gap-1 pl-6">
        {item.map((item: any, index: number) => (
          <li key={index}>
            <Link
              href={item.route}
              className={`group relative flex items-center gap-2.5 rounded-lg px-4 py-2 font-medium duration-300 ease-in-out transition-all ${
                pathname === item.route
                  ? "text-white bg-blue-500 shadow-md border-l-2 border-blue-300"
                  : "text-blue-200 hover:text-white hover:bg-blue-700"
              }`}
            >
              <span className="w-2 h-2 rounded-full bg-current opacity-60"></span>
              {item.label}
            </Link>
          </li>
        ))}
      </ul>
    </>
  );
};

export default SidebarDropdown;

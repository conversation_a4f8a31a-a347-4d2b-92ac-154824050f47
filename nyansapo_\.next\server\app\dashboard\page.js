/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=E%3A%5CPROJECTS%5CFRANCIS%20ASANTE%5Cnyansapo_%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CPROJECTS%5CFRANCIS%20ASANTE%5Cnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=E%3A%5CPROJECTS%5CFRANCIS%20ASANTE%5Cnyansapo_%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CPROJECTS%5CFRANCIS%20ASANTE%5Cnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=E%3A%5CPROJECTS%5CFRANCIS%20ASANTE%5Cnyansapo_%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CPROJECTS%5CFRANCIS%20ASANTE%5Cnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNQUk9KRUNUUyU1QyU1Q0ZSQU5DSVMlMjBBU0FOVEUlNUMlNUNueWFuc2Fwb18lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBNkciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLz84MTA1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcUFJPSkVDVFNcXFxcRlJBTkNJUyBBU0FOVEVcXFxcbnlhbnNhcG9fXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNQUk9KRUNUUyU1QyU1Q0ZSQU5DSVMlMjBBU0FOVEUlNUMlNUNueWFuc2Fwb18lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBa0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLz81YWRjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcUFJPSkVDVFNcXFxcRlJBTkNJUyBBU0FOVEVcXFxcbnlhbnNhcG9fXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Ccomponents%5C%5CDashboard%5C%5CE-commerce.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5CDefaultLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Ccomponents%5C%5CDashboard%5C%5CE-commerce.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5CDefaultLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Dashboard/E-commerce.tsx */ \"(ssr)/./src/components/Dashboard/E-commerce.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layouts/DefaultLayout.tsx */ \"(ssr)/./src/components/Layouts/DefaultLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNQUk9KRUNUUyU1QyU1Q0ZSQU5DSVMlMjBBU0FOVEUlNUMlNUNueWFuc2Fwb18lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDRGFzaGJvYXJkJTVDJTVDRS1jb21tZXJjZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNQUk9KRUNUUyU1QyU1Q0ZSQU5DSVMlMjBBU0FOVEUlNUMlNUNueWFuc2Fwb18lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDTGF5b3V0cyU1QyU1Q0RlZmF1bHRMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQXFKO0FBQ3JKO0FBQ0EsZ01BQXNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8/MGQ1YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJFOlxcXFxQUk9KRUNUU1xcXFxGUkFOQ0lTIEFTQU5URVxcXFxueWFuc2Fwb19cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcRGFzaGJvYXJkXFxcXEUtY29tbWVyY2UudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRTpcXFxcUFJPSkVDVFNcXFxcRlJBTkNJUyBBU0FOVEVcXFxcbnlhbnNhcG9fXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXExheW91dHNcXFxcRGVmYXVsdExheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Ccomponents%5C%5CDashboard%5C%5CE-commerce.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5CFRANCIS%20ASANTE%5C%5Cnyansapo_%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5CDefaultLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/api/apicaller.ts":
/*!******************************!*\
  !*** ./src/api/apicaller.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apicaller: () => (/* binding */ apicaller),\n/* harmony export */   loginapicaller: () => (/* binding */ loginapicaller)\n/* harmony export */ });\n// const BASEURL = 'http://localhost:5051/api/';\nconst BASEURL = \"https://nyansapo-api.vercel.app/api/\";\n//const BASEURL = 'https://nyansapoapi-production.up.railway.app/api/';\n// Function to check if the input is an object\nconst isitanobject = (text)=>{\n    try {\n        if (text == null || text == undefined || text == \"\") {\n            return false;\n        }\n        if (typeof text === \"object\" && !Array.isArray(text) && text !== null) {\n            return true;\n        } else {\n            return false;\n        }\n    } catch (error) {\n        return false;\n    }\n};\n// Client-side function to include role in headers\nconst apicaller = async (payloaddata, urlpath)=>{\n    try {\n        const headers = {\n            \"Accept\": \"application/json\",\n            \"Content-Type\": \"application/json\"\n        };\n        const options = {\n            method: \"POST\",\n            headers: headers,\n            body: JSON.stringify(payloaddata),\n            credentials: \"include\"\n        };\n        const fetchResult = await fetch(`${BASEURL}${urlpath}`, options);\n        const result = await fetchResult.json();\n        if (fetchResult.ok) {\n            return {\n                success: true,\n                message: result.message || \"Action completed successfully\",\n                data: result.data || null\n            };\n        } else {\n            return {\n                success: false,\n                message: result.message || \"Action failed\"\n            };\n        }\n    } catch (error) {\n        console.error(\"API call failed:\", error);\n        return {\n            success: false,\n            message: \"Unable to complete action. An error occurred!\"\n        };\n    }\n};\nconst loginapicaller = async (payloaddata, urlpath)=>{\n    try {\n        // Initialize the request headers (without Authorization and X-User-Role)\n        const requestheaders = {\n            Accept: \"application/json\",\n            \"Content-Type\": \"application/json\"\n        };\n        // Log the headers before sending (for debugging purposes)\n        console.log(\"Request Headers:\", requestheaders);\n        // Configure the request options, including method, headers, and body\n        const options = {\n            method: \"POST\",\n            headers: requestheaders,\n            body: JSON.stringify(payloaddata)\n        };\n        // Make the fetch request\n        const fetchResult = await fetch(`${BASEURL}${urlpath}`, options);\n        const result = await fetchResult.json();\n        // Log the response data\n        console.log(\"Response received:\", result);\n        // Check if the request was successful\n        if (fetchResult.ok) {\n            return {\n                success: true,\n                message: result.message || \"Action completed successfully\",\n                data: result.data || null\n            };\n        } else {\n            // Handle the error response\n            return {\n                success: false,\n                message: result.message || \"Action failed\"\n            };\n        }\n    } catch (error) {\n        // Handle any unexpected errors\n        console.error(\"API call failed:\", error);\n        return {\n            success: false,\n            message: \"Unable to complete action. An error occurred!\"\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/api/apicaller.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsvectormap_dist_jsvectormap_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsvectormap/dist/jsvectormap.css */ \"(ssr)/./node_modules/jsvectormap/dist/jsvectormap.css\");\n/* harmony import */ var flatpickr_dist_flatpickr_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! flatpickr/dist/flatpickr.min.css */ \"(ssr)/./node_modules/flatpickr/dist/flatpickr.min.css\");\n/* harmony import */ var _css_satoshi_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/css/satoshi.css */ \"(ssr)/./src/css/satoshi.css\");\n/* harmony import */ var _css_style_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/css/style.css */ \"(ssr)/./src/css/style.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Spin!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Spin!=!antd */ \"(ssr)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Spin!=!antd */ \"(ssr)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Spin!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(ssr)/./node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n // Import Badge and Alert from Ant Design\n\nfunction RootLayout({ children }) {\n    // State to manage loading\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    // State to manage loading of authentication check\n    const [loadingAuth, setLoadingAuth] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    // State to track authorization status\n    const [isAuthorized, setIsAuthorized] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true); // Assuming authorized by default\n    // Check if token and userRole exist in localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const token = localStorage.getItem(\"token\");\n        const userRole = localStorage.getItem(\"userRole\");\n        // If either token or userRole is missing, set isAuthorized to false\n        if (!token || userRole !== \"admin\") {\n            setIsAuthorized(false);\n        }\n        setTimeout(()=>setLoading(false), 1000); // Simulate loading for now\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            setLoadingAuth(false); // Set to false after the auth check completes\n        }, 500); // Adjust time as needed for auth check to be complete\n        return ()=>clearTimeout(timer);\n    }, []);\n    // Show Ant Design Spin until authentication check is done\n    if (loadingAuth || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login page when button is clicked\n    const handleRedirect = ()=>{\n        window.location.href = \"/\"; // This will redirect the user to the login page\n    };\n    // If not authorized, show the badge or alert with a button to redirect\n    if (!isAuthorized) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen w-full flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Ribbon, {\n                    text: \"Unauthorized\",\n                    color: \"red\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        message: \"You are not authorized. Please log in.\",\n                        type: \"error\",\n                        showIcon: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Spin_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    type: \"primary\",\n                    onClick: handleRedirect,\n                    className: \"mt-4\",\n                    children: \"Go to Login\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dark:bg-boxdark-2 dark:text-bodydark\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__.AntdRegistry, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsvectormap_dist_jsvectormap_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsvectormap/dist/jsvectormap.css */ \"(ssr)/./node_modules/jsvectormap/dist/jsvectormap.css\");\n/* harmony import */ var flatpickr_dist_flatpickr_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! flatpickr/dist/flatpickr.min.css */ \"(ssr)/./node_modules/flatpickr/dist/flatpickr.min.css\");\n/* harmony import */ var _css_satoshi_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/css/satoshi.css */ \"(ssr)/./src/css/satoshi.css\");\n/* harmony import */ var _css_style_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/css/style.css */ \"(ssr)/./src/css/style.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(ssr)/./node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* harmony import */ var _provider_Provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/provider/Provider */ \"(ssr)/./src/provider/Provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dark:bg-boxdark-2 dark:text-bodydark\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_provider_Provider__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_6__.AntdRegistry, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQzBDO0FBQ0E7QUFDZjtBQUNGO0FBQ0M7QUFDaUM7QUFDVjtBQUVsQyxTQUFTRyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsMEJBQTBCO3NCQUM5Qiw0RUFBQ0M7Z0JBQUlDLFdBQVU7MEJBRWIsNEVBQUNSLDBEQUFjQTs4QkFDYiw0RUFBQ0QscUVBQVlBO2tDQUFFRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCBcImpzdmVjdG9ybWFwL2Rpc3QvanN2ZWN0b3JtYXAuY3NzXCI7XHJcbmltcG9ydCBcImZsYXRwaWNrci9kaXN0L2ZsYXRwaWNrci5taW4uY3NzXCI7XHJcbmltcG9ydCBcIkAvY3NzL3NhdG9zaGkuY3NzXCI7XHJcbmltcG9ydCBcIkAvY3NzL3N0eWxlLmNzc1wiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IEFudGRSZWdpc3RyeSB9IGZyb20gXCJAYW50LWRlc2lnbi9uZXh0anMtcmVnaXN0cnlcIjtcclxuaW1wb3J0IENsaWVudFByb3ZpZGVyIGZyb20gXCJAL3Byb3ZpZGVyL1Byb3ZpZGVyXCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufTogUmVhZG9ubHk8e1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0+KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keSBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc9e3RydWV9PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGFyazpiZy1ib3hkYXJrLTIgZGFyazp0ZXh0LWJvZHlkYXJrXCI+XHJcbiAgICAgICAgICB7Lyoge2xvYWRpbmcgPyA8TG9hZGVyIC8+IDogY2hpbGRyZW59ICovfVxyXG4gICAgICAgICAgPENsaWVudFByb3ZpZGVyPlxyXG4gICAgICAgICAgICA8QW50ZFJlZ2lzdHJ5PntjaGlsZHJlbn08L0FudGRSZWdpc3RyeT5cclxuICAgICAgICAgIDwvQ2xpZW50UHJvdmlkZXI+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkFudGRSZWdpc3RyeSIsIkNsaWVudFByb3ZpZGVyIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Iiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CardDataStats.tsx":
/*!******************************************!*\
  !*** ./src/components/CardDataStats.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst CardDataStats = ({ title, total, rate, levelUp, levelDown, children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-7.5 py-6 shadow-default dark:border-strokedark dark:bg-boxdark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\CardDataStats.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex items-end justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-title-md font-bold text-black dark:text-white\",\n                                children: total\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\CardDataStats.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\CardDataStats.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\CardDataStats.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `flex items-center gap-1 text-sm font-medium ${levelUp && \"text-meta-3\"} ${levelDown && \"text-meta-5\"} `,\n                        children: [\n                            rate,\n                            levelUp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"fill-meta-3\",\n                                width: \"10\",\n                                height: \"11\",\n                                viewBox: \"0 0 10 11\",\n                                fill: \"none\",\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M4.35716 2.47737L0.908974 5.82987L5.0443e-07 4.94612L5 0.0848689L10 4.94612L9.09103 5.82987L5.64284 2.47737L5.64284 10.0849L4.35716 10.0849L4.35716 2.47737Z\",\n                                    fill: \"\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\CardDataStats.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\CardDataStats.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined),\n                            levelDown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"fill-meta-5\",\n                                width: \"10\",\n                                height: \"11\",\n                                viewBox: \"0 0 10 11\",\n                                fill: \"none\",\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M5.64284 7.69237L9.09102 4.33987L10 5.22362L5 10.0849L-8.98488e-07 5.22362L0.908973 4.33987L4.35716 7.69237L4.35716 0.0848701L5.64284 0.0848704L5.64284 7.69237Z\",\n                                    fill: \"\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\CardDataStats.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\CardDataStats.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\CardDataStats.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\CardDataStats.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\CardDataStats.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CardDataStats);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CardDataStats.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Charts/ChartOne.tsx":
/*!********************************************!*\
  !*** ./src/components/Charts/ChartOne.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Dynamically import ReactApexChart for client-side rendering\nconst ReactApexChart = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\Charts\\\\ChartOne.tsx -> \" + \"react-apexcharts\"\n        ]\n    },\n    ssr: false\n});\nconst ChartOne = ({ totalCoursesData, completedCoursesData, averageRatingData, months })=>{\n    const options = {\n        legend: {\n            show: false,\n            position: \"top\",\n            horizontalAlign: \"left\"\n        },\n        colors: [\n            \"#3C50E0\",\n            \"#80CAEE\",\n            \"#FF8A00\"\n        ],\n        chart: {\n            fontFamily: \"Satoshi, sans-serif\",\n            height: 335,\n            type: \"area\",\n            dropShadow: {\n                enabled: true,\n                color: \"#623CEA14\",\n                top: 10,\n                blur: 4,\n                left: 0,\n                opacity: 0.1\n            },\n            toolbar: {\n                show: false\n            }\n        },\n        responsive: [\n            {\n                breakpoint: 1024,\n                options: {\n                    chart: {\n                        height: 300\n                    }\n                }\n            },\n            {\n                breakpoint: 1366,\n                options: {\n                    chart: {\n                        height: 350\n                    }\n                }\n            }\n        ],\n        stroke: {\n            width: [\n                2,\n                2,\n                2\n            ],\n            curve: \"smooth\"\n        },\n        grid: {\n            xaxis: {\n                lines: {\n                    show: true\n                }\n            },\n            yaxis: {\n                lines: {\n                    show: true\n                }\n            }\n        },\n        dataLabels: {\n            enabled: false\n        },\n        markers: {\n            size: 4,\n            colors: \"#fff\",\n            strokeColors: [\n                \"#3056D3\",\n                \"#80CAEE\",\n                \"#FF8A00\"\n            ],\n            strokeWidth: 3,\n            strokeOpacity: 0.9,\n            fillOpacity: 1,\n            hover: {\n                size: undefined,\n                sizeOffset: 5\n            }\n        },\n        xaxis: {\n            type: \"category\",\n            categories: months,\n            axisBorder: {\n                show: false\n            },\n            axisTicks: {\n                show: false\n            }\n        },\n        yaxis: {\n            title: {\n                style: {\n                    fontSize: \"0px\"\n                }\n            },\n            min: 0,\n            max: 100\n        }\n    };\n    const series = [\n        {\n            name: \"Total Courses\",\n            data: totalCoursesData\n        },\n        {\n            name: \"Completed Courses\",\n            data: completedCoursesData\n        },\n        {\n            name: \"Average Rating\",\n            data: averageRatingData\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"col-span-12 rounded-sm border border-stroke bg-white px-5 pb-5 pt-7.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:col-span-8 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap items-start justify-between gap-3 sm:flex-nowrap\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex w-full flex-wrap gap-3 sm:gap-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex min-w-47.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2 mt-1 flex h-4 w-full max-w-4 items-center justify-center rounded-full border border-primary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block h-2.5 w-full max-w-2.5 rounded-full bg-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold text-primary\",\n                                                children: \"Total Courses\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium\",\n                                                children: [\n                                                    months[0],\n                                                    \" - \",\n                                                    months[months.length - 1]\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex min-w-47.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2 mt-1 flex h-4 w-full max-w-4 items-center justify-center rounded-full border border-secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block h-2.5 w-full max-w-2.5 rounded-full bg-secondary\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold text-secondary\",\n                                                children: \"Completed Courses\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium\",\n                                                children: [\n                                                    months[0],\n                                                    \" - \",\n                                                    months[months.length - 1]\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex min-w-47.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2 mt-1 flex h-4 w-full max-w-4 items-center justify-center rounded-full border border-tertiary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block h-2.5 w-full max-w-2.5 rounded-full bg-tertiary\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold text-tertiary\",\n                                                children: \"Average Rating\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium\",\n                                                children: [\n                                                    months[0],\n                                                    \" - \",\n                                                    months[months.length - 1]\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex w-full max-w-45 justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center rounded-md bg-whiter p-1.5 dark:bg-meta-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"rounded bg-white px-3 py-1 text-xs font-medium text-black shadow-card hover:bg-white hover:shadow-card dark:bg-boxdark dark:text-white dark:hover:bg-boxdark\",\n                                    children: \"Day\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"rounded px-3 py-1 text-xs font-medium text-black hover:bg-white hover:shadow-card dark:text-white dark:hover:bg-boxdark\",\n                                    children: \"Week\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"rounded px-3 py-1 text-xs font-medium text-black hover:bg-white hover:shadow-card dark:text-white dark:hover:bg-boxdark\",\n                                    children: \"Month\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chart-container overflow-hidden max-w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"chartOne\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactApexChart, {\n                        options: options,\n                        series: series,\n                        type: \"area\",\n                        height: 350,\n                        width: \"100%\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Charts\\\\ChartOne.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChartOne);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Charts/ChartOne.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClickOutside.tsx":
/*!*****************************************!*\
  !*** ./src/components/ClickOutside.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ClickOutside = ({ children, exceptionRef, onClick, className })=>{\n    const wrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickListener = (event)=>{\n            let clickedInside = false;\n            if (exceptionRef) {\n                clickedInside = wrapperRef.current && wrapperRef.current.contains(event.target) || exceptionRef.current && exceptionRef.current === event.target || exceptionRef.current && exceptionRef.current.contains(event.target);\n            } else {\n                clickedInside = wrapperRef.current && wrapperRef.current.contains(event.target);\n            }\n            if (!clickedInside) onClick();\n        };\n        // Add a small delay to ensure DOM is ready\n        const timeoutId = setTimeout(()=>{\n            document.addEventListener(\"mousedown\", handleClickListener);\n        }, 0);\n        return ()=>{\n            clearTimeout(timeoutId);\n            document.removeEventListener(\"mousedown\", handleClickListener);\n        };\n    }, [\n        exceptionRef,\n        onClick\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: wrapperRef,\n        className: `${className || \"\"}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\ClickOutside.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClickOutside);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClickOutside.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Dashboard/E-commerce.tsx":
/*!*************************************************!*\
  !*** ./src/components/Dashboard/E-commerce.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOutlined_CheckCircleOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOutlined,CheckCircleOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BookOutlined_CheckCircleOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOutlined,CheckCircleOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BookOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BookOutlined_CheckCircleOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOutlined,CheckCircleOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BookOutlined_CheckCircleOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOutlined,CheckCircleOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/TrophyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BookOutlined_CheckCircleOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOutlined,CheckCircleOutlined,TeamOutlined,TrophyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../reduxRTK/services/authApi */ \"(ssr)/./src/reduxRTK/services/authApi.ts\");\n/* harmony import */ var _reduxRTK_services_courseApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../reduxRTK/services/courseApi */ \"(ssr)/./src/reduxRTK/services/courseApi.ts\");\n/* harmony import */ var _reduxRTK_services_reportApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../reduxRTK/services/reportApi */ \"(ssr)/./src/reduxRTK/services/reportApi.ts\");\n/* harmony import */ var _CardDataStats__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../CardDataStats */ \"(ssr)/./src/components/CardDataStats.tsx\");\n/* harmony import */ var _Charts_ChartOne__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../Charts/ChartOne */ \"(ssr)/./src/components/Charts/ChartOne.tsx\");\n/* harmony import */ var _barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Card,Progress,Spin,Table,Tag,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Card,Progress,Spin,Table,Tag,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Card,Progress,Spin,Table,Tag,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Card,Progress,Spin,Table,Tag,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Card,Progress,Spin,Table,Tag,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Card,Progress,Spin,Table,Tag,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/table/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst { Title, Text } = _barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\nconst ECommerce = ()=>{\n    const [accessToken, setAccessToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prevTotalUsers, setPrevTotalUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [prevTotalCourses, setPrevTotalCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [prevCompletedCourses, setPrevCompletedCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [prevAverageCourseRating, setPrevAverageCourseRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Retrieve the access token on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const token = localStorage.getItem(\"token\");\n        setAccessToken(token);\n    }, []);\n    // Fetch users, courses, and reports\n    const { data: usersData, isLoading: usersLoading } = (0,_reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_2__.useGetUsersQuery)({\n        accessToken: accessToken || \"\"\n    }, {\n        skip: !accessToken\n    });\n    const { data: coursesData, isLoading: coursesLoading } = (0,_reduxRTK_services_courseApi__WEBPACK_IMPORTED_MODULE_3__.useRetrieveCourseQuery)({\n        accessToken: accessToken || \"\"\n    }, {\n        skip: !accessToken\n    });\n    const { data: reportsData, isLoading: reportsLoading } = (0,_reduxRTK_services_reportApi__WEBPACK_IMPORTED_MODULE_4__.useRetrieveReportQuery)({\n        accessToken: accessToken || \"\"\n    }, {\n        skip: !accessToken\n    });\n    // Calculate comprehensive analytics\n    const totalUsers = usersData?.data?.total || 0;\n    const totalCourses = coursesData?.data?.total || 0;\n    const reports = reportsData?.data?.reports || [];\n    const completedCourses = reports.filter((report)=>report.completed).length;\n    const totalReports = reports.length;\n    const averageCourseRating = totalReports > 0 ? (reports.reduce((sum, report)=>sum + (report.score || 0), 0) / totalReports).toFixed(1) : \"0.0\";\n    // Calculate completion rate\n    const completionRate = totalReports > 0 ? (completedCourses / totalReports * 100).toFixed(1) : \"0.0\";\n    // Calculate course performance data\n    const coursePerformance = coursesData?.data?.courses?.map((course)=>{\n        const courseReports = reports.filter((report)=>report.courseId === course.id);\n        const courseCompleted = courseReports.filter((report)=>report.completed).length;\n        const courseTotal = courseReports.length;\n        const courseCompletionRate = courseTotal > 0 ? (courseCompleted / courseTotal * 100).toFixed(1) : \"0.0\";\n        const courseAvgScore = courseTotal > 0 ? (courseReports.reduce((sum, report)=>sum + (report.score || 0), 0) / courseTotal).toFixed(1) : \"0.0\";\n        return {\n            key: course.id,\n            title: course.title,\n            totalAssigned: courseTotal,\n            completed: courseCompleted,\n            completionRate: parseFloat(courseCompletionRate),\n            averageScore: parseFloat(courseAvgScore)\n        };\n    }) || [];\n    // Recent activity data\n    const recentActivity = reports.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 10).map((report, index)=>({\n            key: index,\n            user: `${report.userFirstName} ${report.userLastName}`,\n            course: report.courseTitle,\n            status: report.completed ? \"Completed\" : \"In Progress\",\n            score: report.score || 0,\n            date: new Date(report.createdAt).toLocaleDateString()\n        }));\n    // Set previous values when the current values change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (usersData) setPrevTotalUsers(totalUsers);\n        if (coursesData) setPrevTotalCourses(totalCourses);\n        if (reportsData) setPrevCompletedCourses(completedCourses);\n        if (reportsData) setPrevAverageCourseRating(parseFloat(averageCourseRating));\n    }, [\n        usersData,\n        coursesData,\n        reportsData,\n        totalUsers,\n        totalCourses,\n        completedCourses,\n        averageCourseRating\n    ]);\n    // Loading state\n    if (usersLoading || coursesLoading || reportsLoading || !accessToken) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                lineNumber: 99,\n                columnNumber: 5\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n            lineNumber: 98,\n            columnNumber: 12\n        }, undefined);\n    }\n    // Data for ChartOne\n    const chartData = {\n        totalCoursesData: [\n            23,\n            11,\n            22,\n            27,\n            13,\n            22,\n            37,\n            21,\n            44,\n            22,\n            30,\n            45\n        ],\n        completedCoursesData: [\n            30,\n            25,\n            36,\n            30,\n            45,\n            35,\n            64,\n            52,\n            59,\n            36,\n            39,\n            51\n        ],\n        averageRatingData: [\n            4.2,\n            4.5,\n            4.1,\n            4.3,\n            4.4,\n            4.0,\n            4.6,\n            4.3,\n            4.2,\n            4.5,\n            4.6,\n            4.7\n        ],\n        months: [\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\",\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\"\n        ]\n    };\n    // Table columns for course performance\n    const courseColumns = [\n        {\n            title: \"Course Title\",\n            dataIndex: \"title\",\n            key: \"title\",\n            render: (text)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                    strong: true,\n                    children: text\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 33\n                }, undefined)\n        },\n        {\n            title: \"Total Assigned\",\n            dataIndex: \"totalAssigned\",\n            key: \"totalAssigned\",\n            align: \"center\"\n        },\n        {\n            title: \"Completed\",\n            dataIndex: \"completed\",\n            key: \"completed\",\n            align: \"center\"\n        },\n        {\n            title: \"Completion Rate\",\n            dataIndex: \"completionRate\",\n            key: \"completionRate\",\n            align: \"center\",\n            render: (rate)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    percent: rate,\n                    size: \"small\",\n                    status: rate >= 80 ? \"success\" : rate >= 50 ? \"active\" : \"exception\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"Avg Score\",\n            dataIndex: \"averageScore\",\n            key: \"averageScore\",\n            align: \"center\",\n            render: (score)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    color: score >= 80 ? \"green\" : score >= 60 ? \"orange\" : \"red\",\n                    children: [\n                        score.toFixed(1),\n                        \"%\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    // Table columns for recent activity\n    const activityColumns = [\n        {\n            title: \"User\",\n            dataIndex: \"user\",\n            key: \"user\"\n        },\n        {\n            title: \"Course\",\n            dataIndex: \"course\",\n            key: \"course\"\n        },\n        {\n            title: \"Status\",\n            dataIndex: \"status\",\n            key: \"status\",\n            render: (status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    color: status === \"Completed\" ? \"green\" : \"blue\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"Score\",\n            dataIndex: \"score\",\n            key: \"score\",\n            render: (score)=>`${score}%`\n        },\n        {\n            title: \"Date\",\n            dataIndex: \"date\",\n            key: \"date\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CardDataStats__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"Total Users\",\n                        total: totalUsers.toString(),\n                        rate: `${totalUsers > prevTotalUsers ? \"+\" : \"\"}${totalUsers - prevTotalUsers}`,\n                        levelUp: totalUsers > prevTotalUsers,\n                        levelDown: totalUsers < prevTotalUsers,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_CheckCircleOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            style: {\n                                fontSize: \"22px\",\n                                color: \"#1890ff\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CardDataStats__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"Total Courses\",\n                        total: totalCourses.toString(),\n                        rate: `${totalCourses > prevTotalCourses ? \"+\" : \"\"}${totalCourses - prevTotalCourses}`,\n                        levelUp: totalCourses > prevTotalCourses,\n                        levelDown: totalCourses < prevTotalCourses,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_CheckCircleOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            style: {\n                                fontSize: \"22px\",\n                                color: \"#52c41a\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CardDataStats__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"Completion Rate\",\n                        total: `${completionRate}%`,\n                        rate: `${completedCourses}/${totalReports} completed`,\n                        levelUp: parseFloat(completionRate) >= 70,\n                        levelDown: parseFloat(completionRate) < 50,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_CheckCircleOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            style: {\n                                fontSize: \"22px\",\n                                color: \"#faad14\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CardDataStats__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"Average Score\",\n                        total: `${averageCourseRating}%`,\n                        rate: `From ${totalReports} reports`,\n                        levelUp: parseFloat(averageCourseRating) >= 75,\n                        levelDown: parseFloat(averageCourseRating) < 60,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_CheckCircleOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            style: {\n                                fontSize: \"22px\",\n                                color: \"#f5222d\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"Performance Overview\",\n                className: \"shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Charts_ChartOne__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    totalCoursesData: chartData.totalCoursesData,\n                    completedCoursesData: chartData.completedCoursesData,\n                    averageRatingData: chartData.averageRatingData,\n                    months: chartData.months\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_CheckCircleOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Course Performance Analytics\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 11\n                }, void 0),\n                className: \"shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    columns: courseColumns,\n                    dataSource: coursePerformance,\n                    pagination: {\n                        pageSize: 10\n                    },\n                    scroll: {\n                        x: 800\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_CheckCircleOutlined_TeamOutlined_TrophyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Recent Learning Activity\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 11\n                }, void 0),\n                className: \"shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_Progress_Spin_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    columns: activityColumns,\n                    dataSource: recentActivity,\n                    pagination: {\n                        pageSize: 10\n                    },\n                    scroll: {\n                        x: 600\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Dashboard\\\\E-commerce.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ECommerce);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Dashboard/E-commerce.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header/DropdownUser.tsx":
/*!************************************************!*\
  !*** ./src/components/Header/DropdownUser.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,message!=!antd */ \"(ssr)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,message!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../reduxRTK/services/authApi */ \"(ssr)/./src/reduxRTK/services/authApi.ts\");\n/* harmony import */ var _barrel_optimize_names_PoweroffOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=PoweroffOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_PoweroffOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=PoweroffOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/PoweroffOutlined.js\");\n// import { useState, useEffect } from \"react\";\n// import Link from \"next/link\";\n// import { Avatar, Button, message } from \"antd\";\n// import { useRouter } from \"next/navigation\";\n// import { useLogoutMutation } from \"../../reduxRTK/services/authApi\";\n// import { PoweroffOutlined, UserOutlined } from \"@ant-design/icons\"; // Importing Ant Design icons\n// const DropdownUser = () => {\n//   const [dropdownOpen, setDropdownOpen] = useState(false);\n//   const [logout, { isLoading }] = useLogoutMutation();\n//   const router = useRouter();\n//   const [firstName, setFirstName] = useState<string | null>(null);\n//   // Fetch the first name from localStorage when the component mounts\n//   useEffect(() => {\n//     const storedFirstName = localStorage.getItem(\"firstName\");\n//     if (storedFirstName) {\n//       setFirstName(storedFirstName);\n//     }\n//   }, []);\n//   const handleLogout = async () => {\n//     try {\n//       const response = await logout();\n//       // Check if the response is successful and contains the message\n//       if (response.data && response.data.message === \"Logged out successfully\") {\n//         // Clear localStorage after successful logout\n//         localStorage.clear();\n//         message.success(\"Logout successful!\");\n//         router.replace(\"/\"); // Redirect to the login page\n//       } else {\n//         message.error(`Logout failed: ${response.data?.message || \"Unknown error\"}`);\n//       }\n//     } catch (error) {\n//       console.error(\"Logout error:\", error);\n//       message.error(\"An error occurred during logout.\");\n//     }\n//   };\n//   return (\n//     <div\n//       className=\"relative\"\n//       onClick={() => setDropdownOpen(!dropdownOpen)}\n//     >\n//       <Link\n//         className=\"flex items-center gap-4\"\n//         href=\"#\"\n//       >\n//         <span className=\"hidden text-right lg:block\">\n//           <span className=\"block text-sm font-medium text-black dark:text-white\">\n//             {firstName || \"User\"} {/* Display first name from localStorage or default to 'User' */}\n//           </span>\n//         </span>\n//         {/* Avatar Component */}\n//         <Avatar\n//           size={48}\n//           icon={<UserOutlined />}\n//           className=\"bg-blue-600 text-white\"\n//         />\n//         {/* Dropdown Arrow */}\n//         <svg\n//           className=\"hidden fill-current sm:block\"\n//           width=\"12\"\n//           height=\"8\"\n//           viewBox=\"0 0 12 8\"\n//           fill=\"none\"\n//           xmlns=\"http://www.w3.org/2000/svg\"\n//         >\n//           <path\n//             fillRule=\"evenodd\"\n//             clipRule=\"evenodd\"\n//             d=\"M0.410765 0.910734C0.736202 0.585297 1.26384 0.585297 1.58928 0.910734L6.00002 5.32148L10.4108 0.910734C10.7362 0.585297 11.2638 0.585297 11.5893 0.910734C11.9147 1.23617 11.9147 1.76381 11.5893 2.08924L6.58928 7.08924C6.26384 7.41468 5.7362 7.41468 5.41077 7.08924L0.410765 2.08924C0.0853277 1.76381 0.0853277 1.23617 0.410765 0.910734Z\"\n//             fill=\"\"\n//           />\n//         </svg>\n//       </Link>\n//       {/* Dropdown Menu */}\n//       {dropdownOpen && (\n//         <div\n//           className={`absolute right-0 mt-4 flex w-62.5 flex-col rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark`}\n//         >\n//           <ul className=\"flex flex-col gap-5 border-b border-stroke px-6 py-7.5 dark:border-strokedark\">\n//             <li>\n//               <Button\n//                 type=\"text\"\n//                 onClick={handleLogout}\n//                 className=\"text-sm font-medium text-red-600 dark:text-red-500\"\n//                 loading={isLoading}\n//               >\n//                 <PoweroffOutlined className=\"mr-2\" /> Logout {/* Added the logout icon here */}\n//               </Button>\n//             </li>\n//           </ul>\n//         </div>\n//       )}\n//     </div>\n//   );\n// };\n// export default DropdownUser;\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n // Importing Ant Design icons\nconst DropdownUser = ()=>{\n    const [logout, { isLoading }] = (0,_reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_3__.useLogoutMutation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [firstName, setFirstName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch the first name from localStorage when the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const storedFirstName = localStorage.getItem(\"firstName\");\n        if (storedFirstName) {\n            setFirstName(storedFirstName);\n        }\n    }, []);\n    const handleLogout = async ()=>{\n        try {\n            const response = await logout();\n            // Check if the response is successful and contains the message\n            if (response.data && response.data.message === \"Logged out successfully\") {\n                // Clear localStorage after successful logout\n                localStorage.clear();\n                _barrel_optimize_names_Avatar_Button_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Logout successful!\");\n                router.replace(\"/auth\"); // Redirect to the login page\n            } else {\n                _barrel_optimize_names_Avatar_Button_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(`Logout failed: ${response.data?.message || \"Unknown error\"}`);\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            _barrel_optimize_names_Avatar_Button_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"An error occurred during logout.\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-right\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"block text-sm font-medium text-black dark:text-white\",\n                    children: [\n                        firstName || \"User\",\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\DropdownUser.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\DropdownUser.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: 48,\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PoweroffOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\DropdownUser.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 15\n                }, void 0),\n                className: \"bg-blue-600 text-white\"\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\DropdownUser.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                type: \"text\",\n                onClick: handleLogout,\n                className: \"text-sm font-medium text-red-600 dark:text-red-500\",\n                loading: isLoading,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PoweroffOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"mr-2\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\DropdownUser.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    \" Logout \"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\DropdownUser.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\DropdownUser.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DropdownUser);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header/DropdownUser.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header/index.tsx":
/*!*****************************************!*\
  !*** ./src/components/Header/index.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _DropdownUser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DropdownUser */ \"(ssr)/./src/components/Header/DropdownUser.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n\n\n\n\nconst Header = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-999 flex w-full bg-white drop-shadow-1 dark:bg-boxdark dark:drop-shadow-none\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-grow items-center justify-between px-4 py-4 shadow-2 md:px-6 2xl:px-11\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 sm:gap-4 lg:hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            \"aria-controls\": \"sidebar\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                props.setSidebarOpen(!props.sidebarOpen);\n                            },\n                            className: \"z-99999 block rounded-sm border border-stroke bg-white p-1.5 shadow-sm dark:border-strokedark dark:bg-boxdark lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"relative block h-5.5 w-5.5 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"du-block absolute right-0 h-full w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `relative left-0 top-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-[0] duration-200 ease-in-out dark:bg-white ${!props.sidebarOpen && \"!w-full delay-300\"}`\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `relative left-0 top-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-150 duration-200 ease-in-out dark:bg-white ${!props.sidebarOpen && \"delay-400 !w-full\"}`\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `relative left-0 top-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-200 duration-200 ease-in-out dark:bg-white ${!props.sidebarOpen && \"!w-full delay-500\"}`\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute right-0 h-full w-full rotate-45\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `absolute left-2.5 top-0 block h-full w-0.5 rounded-sm bg-black delay-300 duration-200 ease-in-out dark:bg-white ${!props.sidebarOpen && \"!h-0 !delay-[0]\"}`\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `delay-400 absolute left-0 top-2.5 block h-0.5 w-full rounded-sm bg-black duration-200 ease-in-out dark:bg-white ${!props.sidebarOpen && \"!h-0 !delay-200\"}`\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"block flex-shrink-0 lg:hidden\",\n                            href: \"/\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                width: 28,\n                                height: 28,\n                                src: \"/images/logo/logo-icon.svg\",\n                                alt: \"Logo\",\n                                className: \"dark:filter dark:invert\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden sm:block\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3 2xsm:gap-7\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"flex items-center gap-2 2xsm:gap-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DropdownUser__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Header\\\\index.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9IZWFkZXIvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNkI7QUFDYTtBQUNYO0FBRS9CLE1BQU1HLFNBQVMsQ0FBQ0M7SUFJZCxxQkFDRSw4REFBQ0M7UUFBT0MsV0FBVTtrQkFDaEIsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUNiLDhEQUFDQztvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUNFOzRCQUNDQyxpQkFBYzs0QkFDZEMsU0FBUyxDQUFDQztnQ0FDUkEsRUFBRUMsZUFBZTtnQ0FDakJSLE1BQU1TLGNBQWMsQ0FBQyxDQUFDVCxNQUFNVSxXQUFXOzRCQUN6Qzs0QkFDQVIsV0FBVTtzQ0FFViw0RUFBQ1M7Z0NBQUtULFdBQVU7O2tEQUNkLDhEQUFDUzt3Q0FBS1QsV0FBVTs7MERBQ2QsOERBQUNTO2dEQUNDVCxXQUFXLENBQUMsZ0hBQWdILEVBQzFILENBQUNGLE1BQU1VLFdBQVcsSUFBSSxvQkFDdkIsQ0FBQzs7Ozs7OzBEQUVKLDhEQUFDQztnREFDQ1QsV0FBVyxDQUFDLGdIQUFnSCxFQUMxSCxDQUFDRixNQUFNVSxXQUFXLElBQUksb0JBQ3ZCLENBQUM7Ozs7OzswREFFSiw4REFBQ0M7Z0RBQ0NULFdBQVcsQ0FBQyxnSEFBZ0gsRUFDMUgsQ0FBQ0YsTUFBTVUsV0FBVyxJQUFJLG9CQUN2QixDQUFDOzs7Ozs7Ozs7Ozs7a0RBR04sOERBQUNDO3dDQUFLVCxXQUFVOzswREFDZCw4REFBQ1M7Z0RBQ0NULFdBQVcsQ0FBQyxnSEFBZ0gsRUFDMUgsQ0FBQ0YsTUFBTVUsV0FBVyxJQUFJLGtCQUN2QixDQUFDOzs7Ozs7MERBRUosOERBQUNDO2dEQUNDVCxXQUFXLENBQUMsZ0hBQWdILEVBQzFILENBQUNGLE1BQU1VLFdBQVcsSUFBSSxrQkFDdkIsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT1YsOERBQUNkLGlEQUFJQTs0QkFBQ00sV0FBVTs0QkFBZ0NVLE1BQUs7c0NBQ25ELDRFQUFDZCxrREFBS0E7Z0NBQ0plLE9BQU87Z0NBQ1BDLFFBQVE7Z0NBQ1JDLEtBQUs7Z0NBQ0xDLEtBQUk7Z0NBQ0pkLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUtoQiw4REFBQ0M7b0JBQUlELFdBQVU7Ozs7Ozs4QkFJZiw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDZTs0QkFBR2YsV0FBVTs7Ozs7O3NDQUVkLDhEQUFDTCxxREFBWUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLdkI7QUFFQSxpRUFBZUUsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9zcmMvY29tcG9uZW50cy9IZWFkZXIvaW5kZXgudHN4PzQwMDMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xyXG5pbXBvcnQgRHJvcGRvd25Vc2VyIGZyb20gXCIuL0Ryb3Bkb3duVXNlclwiO1xyXG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcclxuXHJcbmNvbnN0IEhlYWRlciA9IChwcm9wczoge1xyXG4gIHNpZGViYXJPcGVuOiBzdHJpbmcgfCBib29sZWFuIHwgdW5kZWZpbmVkO1xyXG4gIHNldFNpZGViYXJPcGVuOiAoYXJnMDogYm9vbGVhbikgPT4gdm9pZDtcclxufSkgPT4ge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aGVhZGVyIGNsYXNzTmFtZT1cInN0aWNreSB0b3AtMCB6LTk5OSBmbGV4IHctZnVsbCBiZy13aGl0ZSBkcm9wLXNoYWRvdy0xIGRhcms6YmctYm94ZGFyayBkYXJrOmRyb3Atc2hhZG93LW5vbmVcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtZ3JvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB4LTQgcHktNCBzaGFkb3ctMiBtZDpweC02IDJ4bDpweC0xMVwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgc206Z2FwLTQgbGc6aGlkZGVuXCI+XHJcbiAgICAgICAgICB7LyogPCEtLSBIYW1idXJnZXIgVG9nZ2xlIEJUTiAtLT4gKi99XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIGFyaWEtY29udHJvbHM9XCJzaWRlYmFyXCJcclxuICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcclxuICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xyXG4gICAgICAgICAgICAgIHByb3BzLnNldFNpZGViYXJPcGVuKCFwcm9wcy5zaWRlYmFyT3Blbik7XHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInotOTk5OTkgYmxvY2sgcm91bmRlZC1zbSBib3JkZXIgYm9yZGVyLXN0cm9rZSBiZy13aGl0ZSBwLTEuNSBzaGFkb3ctc20gZGFyazpib3JkZXItc3Ryb2tlZGFyayBkYXJrOmJnLWJveGRhcmsgbGc6aGlkZGVuXCJcclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicmVsYXRpdmUgYmxvY2sgaC01LjUgdy01LjUgY3Vyc29yLXBvaW50ZXJcIj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJkdS1ibG9jayBhYnNvbHV0ZSByaWdodC0wIGgtZnVsbCB3LWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHJlbGF0aXZlIGxlZnQtMCB0b3AtMCBteS0xIGJsb2NrIGgtMC41IHctMCByb3VuZGVkLXNtIGJnLWJsYWNrIGRlbGF5LVswXSBkdXJhdGlvbi0yMDAgZWFzZS1pbi1vdXQgZGFyazpiZy13aGl0ZSAke1xyXG4gICAgICAgICAgICAgICAgICAgICFwcm9wcy5zaWRlYmFyT3BlbiAmJiBcIiF3LWZ1bGwgZGVsYXktMzAwXCJcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICA+PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPHNwYW5cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgbGVmdC0wIHRvcC0wIG15LTEgYmxvY2sgaC0wLjUgdy0wIHJvdW5kZWQtc20gYmctYmxhY2sgZGVsYXktMTUwIGR1cmF0aW9uLTIwMCBlYXNlLWluLW91dCBkYXJrOmJnLXdoaXRlICR7XHJcbiAgICAgICAgICAgICAgICAgICAgIXByb3BzLnNpZGViYXJPcGVuICYmIFwiZGVsYXktNDAwICF3LWZ1bGxcIlxyXG4gICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgID48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8c3BhblxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSBsZWZ0LTAgdG9wLTAgbXktMSBibG9jayBoLTAuNSB3LTAgcm91bmRlZC1zbSBiZy1ibGFjayBkZWxheS0yMDAgZHVyYXRpb24tMjAwIGVhc2UtaW4tb3V0IGRhcms6Ymctd2hpdGUgJHtcclxuICAgICAgICAgICAgICAgICAgICAhcHJvcHMuc2lkZWJhck9wZW4gJiYgXCIhdy1mdWxsIGRlbGF5LTUwMFwiXHJcbiAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgPjwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCBoLWZ1bGwgdy1mdWxsIHJvdGF0ZS00NVwiPlxyXG4gICAgICAgICAgICAgICAgPHNwYW5cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgbGVmdC0yLjUgdG9wLTAgYmxvY2sgaC1mdWxsIHctMC41IHJvdW5kZWQtc20gYmctYmxhY2sgZGVsYXktMzAwIGR1cmF0aW9uLTIwMCBlYXNlLWluLW91dCBkYXJrOmJnLXdoaXRlICR7XHJcbiAgICAgICAgICAgICAgICAgICAgIXByb3BzLnNpZGViYXJPcGVuICYmIFwiIWgtMCAhZGVsYXktWzBdXCJcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICA+PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPHNwYW5cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZGVsYXktNDAwIGFic29sdXRlIGxlZnQtMCB0b3AtMi41IGJsb2NrIGgtMC41IHctZnVsbCByb3VuZGVkLXNtIGJnLWJsYWNrIGR1cmF0aW9uLTIwMCBlYXNlLWluLW91dCBkYXJrOmJnLXdoaXRlICR7XHJcbiAgICAgICAgICAgICAgICAgICAgIXByb3BzLnNpZGViYXJPcGVuICYmIFwiIWgtMCAhZGVsYXktMjAwXCJcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICA+PC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICB7LyogPCEtLSBIYW1idXJnZXIgVG9nZ2xlIEJUTiAtLT4gKi99XHJcblxyXG4gICAgICAgICAgPExpbmsgY2xhc3NOYW1lPVwiYmxvY2sgZmxleC1zaHJpbmstMCBsZzpoaWRkZW5cIiBocmVmPVwiL1wiPlxyXG4gICAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgICB3aWR0aD17Mjh9XHJcbiAgICAgICAgICAgICAgaGVpZ2h0PXsyOH1cclxuICAgICAgICAgICAgICBzcmM9e1wiL2ltYWdlcy9sb2dvL2xvZ28taWNvbi5zdmdcIn1cclxuICAgICAgICAgICAgICBhbHQ9XCJMb2dvXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJkYXJrOmZpbHRlciBkYXJrOmludmVydFwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIHNtOmJsb2NrXCI+XHJcbiAgICAgICAgIFxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIDJ4c206Z2FwLTdcIj5cclxuICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiAyeHNtOmdhcC00XCI+XHJcbiAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgPERyb3Bkb3duVXNlciAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvaGVhZGVyPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBIZWFkZXI7XHJcbiJdLCJuYW1lcyI6WyJMaW5rIiwiRHJvcGRvd25Vc2VyIiwiSW1hZ2UiLCJIZWFkZXIiLCJwcm9wcyIsImhlYWRlciIsImNsYXNzTmFtZSIsImRpdiIsImJ1dHRvbiIsImFyaWEtY29udHJvbHMiLCJvbkNsaWNrIiwiZSIsInN0b3BQcm9wYWdhdGlvbiIsInNldFNpZGViYXJPcGVuIiwic2lkZWJhck9wZW4iLCJzcGFuIiwiaHJlZiIsIndpZHRoIiwiaGVpZ2h0Iiwic3JjIiwiYWx0IiwidWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layouts/DefaultLayout.tsx":
/*!**************************************************!*\
  !*** ./src/components/Layouts/DefaultLayout.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DefaultLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Sidebar */ \"(ssr)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header/index.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DefaultLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    src: \"/images/background.jpg\",\n                    alt: \"Background\",\n                    fill: true,\n                    style: {\n                        objectFit: \"cover\"\n                    },\n                    className: \"opacity-20\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Layouts\\\\DefaultLayout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Layouts\\\\DefaultLayout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        sidebarOpen: sidebarOpen,\n                        setSidebarOpen: setSidebarOpen\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Layouts\\\\DefaultLayout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex flex-1 flex-col lg:ml-72.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                sidebarOpen: sidebarOpen,\n                                setSidebarOpen: setSidebarOpen\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Layouts\\\\DefaultLayout.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Layouts\\\\DefaultLayout.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Layouts\\\\DefaultLayout.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Layouts\\\\DefaultLayout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Layouts\\\\DefaultLayout.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layouts/DefaultLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar/SidebarDropdown.tsx":
/*!****************************************************!*\
  !*** ./src/components/Sidebar/SidebarDropdown.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n\n\n\n\nconst SidebarDropdown = ({ item })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            className: \"mb-5.5 mt-2 flex flex-col gap-1 pl-6\",\n            children: item.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: item.route,\n                        className: `group relative flex items-center gap-2.5 rounded-lg px-4 py-2 font-medium duration-300 ease-in-out transition-all ${pathname === item.route ? \"text-white bg-blue-500 shadow-md border-l-2 border-blue-300\" : \"text-blue-200 hover:text-white hover:bg-blue-700\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-2 h-2 rounded-full bg-current opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarDropdown.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 15\n                            }, undefined),\n                            item.label\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarDropdown.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 13\n                    }, undefined)\n                }, index, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarDropdown.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarDropdown.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarDropdown);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar/SidebarDropdown.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar/SidebarItem.tsx":
/*!************************************************!*\
  !*** ./src/components/Sidebar/SidebarItem.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_Sidebar_SidebarDropdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Sidebar/SidebarDropdown */ \"(ssr)/./src/components/Sidebar/SidebarDropdown.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n\n\n\n\n\nconst SidebarItem = ({ item, pageName, setPageName })=>{\n    const handleClick = ()=>{\n        const updatedPageName = pageName !== item.label.toLowerCase() ? item.label.toLowerCase() : \"\";\n        return setPageName(updatedPageName);\n    };\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const isActive = (item)=>{\n        if (item.route === pathname) return true;\n        if (item.children) {\n            return item.children.some((child)=>isActive(child));\n        }\n        return false;\n    };\n    const isItemActive = isActive(item);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: item.route,\n                    onClick: handleClick,\n                    className: `${isItemActive ? \"bg-blue-600 text-white shadow-lg border-l-4 border-blue-300\" : \"text-blue-100 hover:bg-blue-800 hover:text-white\"} group relative flex items-center gap-3 rounded-lg px-4 py-3 font-medium duration-300 ease-in-out transition-all`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: `${isItemActive ? \"text-white\" : \"text-blue-200\"} transition-colors duration-300`,\n                            children: item.icon\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarItem.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarItem.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: `absolute right-4 top-1/2 -translate-y-1/2 fill-current transition-transform duration-300 ${pageName === item.label.toLowerCase() && \"rotate-180\"} ${isItemActive ? \"text-white\" : \"text-blue-200\"}`,\n                            width: \"20\",\n                            height: \"20\",\n                            viewBox: \"0 0 20 20\",\n                            fill: \"none\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                clipRule: \"evenodd\",\n                                d: \"M4.41107 6.9107C4.73651 6.58527 5.26414 6.58527 5.58958 6.9107L10.0003 11.3214L14.4111 6.91071C14.7365 6.58527 15.2641 6.58527 15.5896 6.91071C15.915 7.23614 15.915 7.76378 15.5896 8.08922L10.5896 13.0892C10.2641 13.4147 9.73651 13.4147 9.41107 13.0892L4.41107 8.08922C4.08563 7.76378 4.08563 7.23614 4.41107 6.9107Z\",\n                                fill: \"\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarItem.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarItem.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarItem.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, undefined),\n                item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `translate transform overflow-hidden ${pageName !== item.label.toLowerCase() && \"hidden\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar_SidebarDropdown__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        item: item.children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarItem.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarItem.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\SidebarItem.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarItem);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar/SidebarItem.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar/index.tsx":
/*!******************************************!*\
  !*** ./src/components/Sidebar/index.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOutlined,DashboardOutlined,FileTextOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOutlined,DashboardOutlined,FileTextOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOutlined,DashboardOutlined,FileTextOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BookOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOutlined,DashboardOutlined,FileTextOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/FileTextOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOutlined,DashboardOutlined,FileTextOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserAddOutlined.js\");\n/* harmony import */ var _components_Sidebar_SidebarItem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar/SidebarItem */ \"(ssr)/./src/components/Sidebar/SidebarItem.tsx\");\n/* harmony import */ var _components_ClickOutside__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ClickOutside */ \"(ssr)/./src/components/ClickOutside.tsx\");\n/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useLocalStorage */ \"(ssr)/./src/hooks/useLocalStorage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst Sidebar = ({ sidebarOpen, setSidebarOpen })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [pageName, setPageName] = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(\"selectedMenu\", \"dashboard\");\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Fetch userRole from localStorage\n        const role = localStorage.getItem(\"userRole\");\n        setUserRole(role);\n    }, []);\n    const adminMenuGroups = [\n        {\n            name: \"Admin Menu\",\n            menuItems: [\n                {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 17\n                    }, undefined),\n                    label: \"Dashboard\",\n                    route: \"/dashboard\"\n                },\n                {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 17\n                    }, undefined),\n                    label: \"User\",\n                    route: \"#\",\n                    children: [\n                        {\n                            label: \"User Management\",\n                            route: \"/dashboard/user/management\"\n                        },\n                        {\n                            label: \"User Report\",\n                            route: \"/dashboard/user/report\"\n                        }\n                    ]\n                },\n                {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 17\n                    }, undefined),\n                    label: \"Lesson Management\",\n                    route: \"/dashboard/lesson\"\n                },\n                {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 17\n                    }, undefined),\n                    label: \"Quiz Management\",\n                    route: \"/dashboard/quiz\"\n                },\n                {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 17\n                    }, undefined),\n                    label: \"Course Assignments\",\n                    route: \"/dashboard/assignments\"\n                }\n            ]\n        }\n    ];\n    const userMenuGroups = [\n        {\n            name: \"User Menu\",\n            menuItems: [\n                {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 17\n                    }, undefined),\n                    label: \"Home\",\n                    route: \"/user/dashboard\"\n                },\n                {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 17\n                    }, undefined),\n                    label: \"Statistic\",\n                    route: \"/user/dashboard/statistic\"\n                },\n                {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOutlined_DashboardOutlined_FileTextOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 17\n                    }, undefined),\n                    label: \"My Lessons\",\n                    route: \"/user/dashboard/lessons\"\n                }\n            ]\n        }\n    ];\n    // Determine menu groups based on user role\n    let menuGroups = [];\n    if (userRole === \"admin\") {\n        menuGroups = adminMenuGroups;\n    } else if (userRole === \"user\") {\n        menuGroups = userMenuGroups;\n    } else {\n        // If role is not \"admin\" or \"user\", do not show anything\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClickOutside__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        onClick: ()=>setSidebarOpen(false),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: `fixed left-0 top-0 z-9999 flex h-screen w-72.5 flex-col overflow-y-hidden bg-gradient-to-br from-blue-950 via-blue-900 to-blue-950 duration-300 ease-linear dark:bg-boxdark lg:translate-x-0 ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between gap-2 px-6 py-6 border-b border-blue-800/30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/dashboard\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            src: \"/images/clogo.png\",\n                                            alt: \"Nyansapo Logo\",\n                                            width: 40,\n                                            height: 40,\n                                            className: \"h-10 w-10 transition-transform duration-300 group-hover:scale-110 rounded-lg shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-blue-400/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-white tracking-wide\",\n                                    children: \"NyansaPo\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            \"aria-controls\": \"sidebar\",\n                            className: \"block lg:hidden p-2 rounded-lg hover:bg-blue-800/50 transition-colors duration-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"fill-current text-blue-200 hover:text-white transition-colors duration-200\",\n                                width: \"20\",\n                                height: \"18\",\n                                viewBox: \"0 0 20 18\",\n                                fill: \"none\",\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z\",\n                                    fill: \"\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-6 px-4 py-4 lg:px-6\",\n                        children: menuGroups.map((group, groupIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"mb-4 ml-4 text-xs font-bold text-blue-300 uppercase tracking-wider\",\n                                        children: group.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"flex flex-col gap-2\",\n                                        children: group.menuItems.map((menuItem, menuIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar_SidebarItem__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                item: menuItem,\n                                                pageName: pageName,\n                                                setPageName: setPageName\n                                            }, menuIndex, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, groupIndex, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\Sidebar\\\\index.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useLocalStorage.tsx":
/*!***************************************!*\
  !*** ./src/hooks/useLocalStorage.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction useLocalStorage(key, initialValue) {\n    // State to store our value\n    // Pass  initial state function to useState so logic is only executed once\n    const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        try {\n            // Get from local storage by key\n            if (false) {}\n        } catch (error) {\n            // If error also return initialValue\n            console.log(error);\n            return initialValue;\n        }\n    });\n    // useEffect to update local storage when the state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        try {\n            // Allow value to be a function so we have same API as useState\n            const valueToStore = typeof storedValue === \"function\" ? storedValue(storedValue) : storedValue;\n            // Save state\n            if (false) {}\n        } catch (error) {\n            // A more advanced implementation would handle the error case\n            console.log(error);\n        }\n    }, [\n        key,\n        storedValue\n    ]);\n    return [\n        storedValue,\n        setStoredValue\n    ];\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useLocalStorage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useLocalStorage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/provider/Provider.tsx":
/*!***********************************!*\
  !*** ./src/provider/Provider.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ClientProvider = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_3__.Provider, {\n        store: _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_2__.store,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\provider\\\\Provider.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXIvUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ2E7QUFDUztBQU1oRCxNQUFNRyxpQkFBZ0QsQ0FBQyxFQUFFQyxRQUFRLEVBQUU7SUFDakUscUJBQU8sOERBQUNILGlEQUFRQTtRQUFDQyxPQUFPQSx3REFBS0E7a0JBQUdFOzs7Ozs7QUFDbEM7QUFFQSxpRUFBZUQsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9zcmMvcHJvdmlkZXIvUHJvdmlkZXIudHN4PzRiNDIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnOyBcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IFByb3ZpZGVyIH0gZnJvbSAncmVhY3QtcmVkdXgnO1xyXG5pbXBvcnQgeyBzdG9yZSB9IGZyb20gJy4uL3JlZHV4UlRLL3N0b3JlL3N0b3JlJzsgXHJcblxyXG5pbnRlcmZhY2UgQ2xpZW50UHJvdmlkZXJQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufVxyXG5cclxuY29uc3QgQ2xpZW50UHJvdmlkZXI6IFJlYWN0LkZDPENsaWVudFByb3ZpZGVyUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIHJldHVybiA8UHJvdmlkZXIgc3RvcmU9e3N0b3JlfT57Y2hpbGRyZW59PC9Qcm92aWRlcj47XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBDbGllbnRQcm92aWRlcjtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiUHJvdmlkZXIiLCJzdG9yZSIsIkNsaWVudFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/provider/Provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/authApi.ts":
/*!******************************************!*\
  !*** ./src/reduxRTK/services/authApi.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   useCheckAuthQuery: () => (/* binding */ useCheckAuthQuery),\n/* harmony export */   useCreateUserMutation: () => (/* binding */ useCreateUserMutation),\n/* harmony export */   useDeleteUserMutation: () => (/* binding */ useDeleteUserMutation),\n/* harmony export */   useGetUsersQuery: () => (/* binding */ useGetUsersQuery),\n/* harmony export */   useLoginMutation: () => (/* binding */ useLoginMutation),\n/* harmony export */   useLogoutMutation: () => (/* binding */ useLogoutMutation),\n/* harmony export */   useResetPasswordMutation: () => (/* binding */ useResetPasswordMutation),\n/* harmony export */   useSendDetailsMutation: () => (/* binding */ useSendDetailsMutation),\n/* harmony export */   useUpdateUserMutation: () => (/* binding */ useUpdateUserMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Custom baseQuery function\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            return {\n                data: response\n            };\n        } else {\n            // Handle Unauthorized and Token Expiry\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"userRole\");\n                localStorage.removeItem(\"firstName\");\n                localStorage.removeItem(\"lastName\");\n                localStorage.removeItem(\"userId\");\n                dispatch(authApi.util.resetApiState());\n                window.location.href = \"/auth\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Create the base API service using RTK Query\nconst authApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"authApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            login: builder.mutation({\n                query: (credentials)=>({\n                        url: \"user/login\",\n                        payload: credentials\n                    }),\n                onQueryStarted: async ({ email, password }, { dispatch, queryFulfilled })=>{\n                    try {\n                        const { data } = await queryFulfilled;\n                        if (data?.success && data?.data) {\n                            const { user, accessToken } = data.data;\n                            if (user && accessToken) {\n                                localStorage.setItem(\"userRole\", user.role || \"\");\n                                localStorage.setItem(\"firstName\", user.firstName || \"\");\n                                localStorage.setItem(\"lastName\", user.lastName || \"\");\n                                localStorage.setItem(\"userId\", user.id || \"\");\n                                localStorage.setItem(\"token\", accessToken);\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Login failed\", error);\n                    }\n                }\n            }),\n            logout: builder.mutation({\n                query: ()=>({\n                        url: \"user/logout\"\n                    }),\n                onQueryStarted: async (_, { dispatch, queryFulfilled })=>{\n                    try {\n                        const { data } = await queryFulfilled;\n                        if (data?.success) {\n                            localStorage.removeItem(\"userRole\");\n                            localStorage.removeItem(\"firstName\");\n                            localStorage.removeItem(\"lastName\");\n                            localStorage.removeItem(\"userId\");\n                            localStorage.removeItem(\"token\");\n                            dispatch(authApi.util.resetApiState());\n                            window.location.href = \"/auth\";\n                        }\n                    } catch (error) {\n                        console.error(\"Logout failed\", error);\n                    }\n                }\n            }),\n            checkAuth: builder.query({\n                query: ()=>({\n                        url: \"user/check/auth\",\n                        payload: {\n                            mode: \"checkauth\"\n                        }\n                    })\n            }),\n            createUser: builder.mutation({\n                query: (newUsers)=>({\n                        url: \"user/register\",\n                        payload: {\n                            mode: \"createnew\",\n                            users: newUsers.users\n                        }\n                    })\n            }),\n            getUsers: builder.query({\n                query: ({ page, limit, accessToken })=>{\n                    const params = new URLSearchParams({\n                        page: String(page || 1),\n                        limit: String(limit || 10)\n                    });\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    return {\n                        url: `user/getusers?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                },\n                transformResponse: (response)=>({\n                        ...response,\n                        data: {\n                            ...response.data,\n                            users: response.data.users\n                        }\n                    })\n            }),\n            deleteUser: builder.mutation({\n                query: ({ mode, userId, accessToken })=>({\n                        url: \"user/delete\",\n                        payload: {\n                            mode,\n                            userId,\n                            accessToken\n                        }\n                    })\n            }),\n            updateUser: builder.mutation({\n                query: ({ userId, mode, accessToken, ...updateData })=>({\n                        url: \"user/update\",\n                        payload: {\n                            userId,\n                            mode,\n                            accessToken,\n                            ...updateData\n                        }\n                    })\n            }),\n            sendDetails: builder.mutation({\n                query: ({ mode, userId, accessToken })=>{\n                    // Ensure mode is either 'senddetails' (single) or 'sendbulkdetails' (all)\n                    if (mode !== \"senddetails\" && mode !== \"sendbulkdetails\") {\n                        throw new Error(\"Invalid mode. Use 'senddetails' for a single user or 'sendbulkdetails' for all users.\");\n                    }\n                    return {\n                        url: \"user/send-details\",\n                        payload: mode === \"senddetails\" ? {\n                            mode,\n                            userId,\n                            accessToken\n                        } : {\n                            mode,\n                            accessToken\n                        }\n                    };\n                }\n            }),\n            // sendDetails: builder.mutation<\n            //   SendDetailsResponse,\n            //   { mode: string; userIds?: string[]; accessToken: string }\n            // >({\n            //   query: ({ mode, userIds, accessToken }) => {\n            //     if (!accessToken) {\n            //       throw new Error(\"Access token is required but missing.\");\n            //     }\n            //     console.log(\"Sending Payload:\", { mode, userIds, accessToken }); // Debugging\n            //     return {\n            //       url: \"user/send-details\",\n            //       payload: mode === \"senddetails\" ? { mode, userIds, accessToken } : { mode, accessToken },\n            //     };\n            //   },\n            // }),\n            resetPassword: builder.mutation({\n                query: ({ userId, accessToken })=>({\n                        url: \"user/reset-password\",\n                        payload: {\n                            userId,\n                            accessToken\n                        }\n                    }),\n                async onQueryStarted ({ userId, accessToken }, { queryFulfilled }) {\n                    try {\n                        const { data } = await queryFulfilled;\n                        if (data?.success) {\n                            // If successful, the data contains user details\n                            const userDetails = data; // This contains the user details\n                            // Store the user details in localStorage\n                            const { id, firstName, lastName, role, email } = userDetails.data;\n                            localStorage.setItem(\"userRole\", role || \"\");\n                            localStorage.setItem(\"firstName\", firstName || \"\");\n                            localStorage.setItem(\"lastName\", lastName || \"\");\n                            localStorage.setItem(\"userId\", id || \"\");\n                            localStorage.setItem(\"userEmail\", email || \"\");\n                            console.log(\"User details retrieved and stored:\", userDetails);\n                        } else {\n                            console.warn(\"Failed to retrieve user details or user mismatch\");\n                        }\n                    } catch (error) {\n                        console.error(\"Error retrieving user details:\", error?.message || \"Unknown error\");\n                    }\n                }\n            })\n        })\n});\nconst { useLoginMutation, useLogoutMutation, useCheckAuthQuery, useCreateUserMutation, useGetUsersQuery, useDeleteUserMutation, useUpdateUserMutation, useSendDetailsMutation, useResetPasswordMutation } = authApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/authApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/courseApi.ts":
/*!********************************************!*\
  !*** ./src/reduxRTK/services/courseApi.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   courseApi: () => (/* binding */ courseApi),\n/* harmony export */   useAssignCourseMutation: () => (/* binding */ useAssignCourseMutation),\n/* harmony export */   useCreateCourseMutation: () => (/* binding */ useCreateCourseMutation),\n/* harmony export */   useDeleteCourseMutation: () => (/* binding */ useDeleteCourseMutation),\n/* harmony export */   useGetCourseAssignmentsQuery: () => (/* binding */ useGetCourseAssignmentsQuery),\n/* harmony export */   useGetCourseByUserIdQuery: () => (/* binding */ useGetCourseByUserIdQuery),\n/* harmony export */   useGetSingleCourseQuery: () => (/* binding */ useGetSingleCourseQuery),\n/* harmony export */   useGetUserAssignmentsQuery: () => (/* binding */ useGetUserAssignmentsQuery),\n/* harmony export */   useRetrieveCourseQuery: () => (/* binding */ useRetrieveCourseQuery),\n/* harmony export */   useUnassignCourseMutation: () => (/* binding */ useUnassignCourseMutation),\n/* harmony export */   useUpdateCourseMutation: () => (/* binding */ useUpdateCourseMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const actualPayload = payload || {};\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(actualPayload, url);\n        console.log(\"Raw API response:\", response);\n        if (response.success) {\n            if (response.data !== undefined && response.data !== null) {\n                if (Array.isArray(response.data.courses)) {\n                    return {\n                        data: {\n                            success: response.success,\n                            message: response.message,\n                            data: {\n                                courses: response.data.courses,\n                                total: response.data.total,\n                                page: actualPayload.page || 1,\n                                limit: actualPayload.limit || response.data.courses.length\n                            }\n                        }\n                    };\n                }\n                if (response.data.id && response.data.title) {\n                    return {\n                        data: response\n                    };\n                }\n            }\n            return {\n                data: {\n                    success: response.success,\n                    message: response.message\n                }\n            };\n        } else {\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                dispatch(courseApi.util.resetApiState());\n                window.location.href = \"/\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API\nconst courseApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"courseApi\",\n    baseQuery: customBaseQuery,\n    tagTypes: [\n        \"CourseAssignments\"\n    ],\n    endpoints: (builder)=>({\n            retrieveCourse: builder.query({\n                query: ({ page, limit, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    return {\n                        url: `main-course-management?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                }\n            }),\n            getCourseByUserId: builder.query({\n                query: ({ userId, page, limit, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    return {\n                        url: `course-by-user?${params.toString()}`,\n                        payload: {\n                            mode: \"getcoursesbyuser\",\n                            userId,\n                            accessToken\n                        }\n                    };\n                }\n            }),\n            createCourse: builder.mutation({\n                query: ({ title, description, url, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"createnew\",\n                            title,\n                            description,\n                            url,\n                            accessToken\n                        }\n                    })\n            }),\n            deleteCourse: builder.mutation({\n                query: ({ courseId, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"delete\",\n                            id: courseId,\n                            accessToken\n                        }\n                    })\n            }),\n            updateCourse: builder.mutation({\n                query: ({ courseId, title, description, url, userIds, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"update\",\n                            id: courseId,\n                            title,\n                            description,\n                            url,\n                            userIds,\n                            accessToken\n                        }\n                    })\n            }),\n            getSingleCourse: builder.query({\n                query: ({ id, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"getsingle\",\n                            id,\n                            accessToken\n                        }\n                    }),\n                transformResponse: (response)=>{\n                    return {\n                        ...response,\n                        data: {\n                            ...response.data,\n                            quizzes: response.data.quizzes.map((quiz)=>({\n                                    ...quiz,\n                                    answer: quiz.answer?.trim()?.toLowerCase()\n                                }))\n                        }\n                    };\n                }\n            }),\n            // New Assignment Management Endpoints\n            assignCourse: builder.mutation({\n                query: ({ courseId, userIds, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"assign\",\n                            id: courseId,\n                            userIds,\n                            accessToken\n                        }\n                    }),\n                invalidatesTags: [\n                    \"CourseAssignments\"\n                ]\n            }),\n            unassignCourse: builder.mutation({\n                query: ({ courseId, userIds, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"unassign\",\n                            id: courseId,\n                            userIds,\n                            accessToken\n                        }\n                    }),\n                invalidatesTags: [\n                    \"CourseAssignments\"\n                ]\n            }),\n            getCourseAssignments: builder.query({\n                query: ({ courseId, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"assignments\",\n                            id: courseId,\n                            accessToken\n                        }\n                    }),\n                providesTags: [\n                    \"CourseAssignments\"\n                ]\n            }),\n            getUserAssignments: builder.query({\n                query: ({ userId, accessToken })=>({\n                        url: \"main-course-management\",\n                        payload: {\n                            mode: \"user-assignments\",\n                            userId,\n                            accessToken\n                        }\n                    })\n            })\n        })\n});\n// Export hooks\nconst { useRetrieveCourseQuery, useGetCourseByUserIdQuery, useCreateCourseMutation, useDeleteCourseMutation, useUpdateCourseMutation, useGetSingleCourseQuery, // New Assignment Hooks\nuseAssignCourseMutation, useUnassignCourseMutation, useGetCourseAssignmentsQuery, useGetUserAssignmentsQuery } = courseApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/courseApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/demoApi.ts":
/*!******************************************!*\
  !*** ./src/reduxRTK/services/demoApi.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   demoApi: () => (/* binding */ demoApi),\n/* harmony export */   useSubmitDemoRequestMutation: () => (/* binding */ useSubmitDemoRequestMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query following your existing pattern\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            return {\n                data: response\n            };\n        } else {\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API following your existing pattern\nconst demoApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"demoApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            submitDemoRequest: builder.mutation({\n                query: (demoData)=>({\n                        url: \"demo-request\",\n                        payload: demoData\n                    })\n            })\n        })\n});\n// Export hooks following your existing pattern\nconst { useSubmitDemoRequestMutation } = demoApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/demoApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/quizApi.ts":
/*!******************************************!*\
  !*** ./src/reduxRTK/services/quizApi.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   quizApi: () => (/* binding */ quizApi),\n/* harmony export */   useCreateQuizMutation: () => (/* binding */ useCreateQuizMutation),\n/* harmony export */   useDeleteQuizMutation: () => (/* binding */ useDeleteQuizMutation),\n/* harmony export */   useGetSingleQuizQuery: () => (/* binding */ useGetSingleQuizQuery),\n/* harmony export */   useRetrieveQuizQuery: () => (/* binding */ useRetrieveQuizQuery),\n/* harmony export */   useUpdateQuizMutation: () => (/* binding */ useUpdateQuizMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            if (response.data) {\n                // Handle RetrieveQuizResponse\n                if (Array.isArray(response.data.quizzes) && typeof response.data.total === \"number\") {\n                    return {\n                        data: response\n                    };\n                }\n                // Handle GetSingleQuizResponse\n                if (typeof response.data.id === \"string\" && typeof response.data.question === \"string\" && Array.isArray(response.data.options)) {\n                    return {\n                        data: response\n                    };\n                }\n            }\n            // If `data` is null or not needed, return success and message only\n            return {\n                data: {\n                    success: response.success,\n                    message: response.message\n                }\n            };\n        } else {\n            // Handle token expiration\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                dispatch(quizApi.util.resetApiState());\n                window.location.href = \"/\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API\nconst quizApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"quizApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            retrieveQuiz: builder.query({\n                query: ({ page, limit, courseId, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    if (courseId) params.append(\"courseId\", courseId);\n                    return {\n                        url: `main-quiz-management?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                },\n                transformResponse: (response)=>({\n                        ...response,\n                        data: {\n                            ...response.data,\n                            quizzes: response.data.quizzes || []\n                        }\n                    })\n            }),\n            createQuiz: builder.mutation({\n                query: ({ courseId, question, options, answer, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"createnew\",\n                            courseId,\n                            question,\n                            options,\n                            answer,\n                            accessToken\n                        }\n                    })\n            }),\n            deleteQuiz: builder.mutation({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"delete\",\n                            id,\n                            accessToken\n                        }\n                    })\n            }),\n            updateQuiz: builder.mutation({\n                query: ({ id, courseId, question, options, answer, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"update\",\n                            id,\n                            courseId,\n                            question,\n                            options,\n                            answer,\n                            accessToken\n                        }\n                    })\n            }),\n            getSingleQuiz: builder.query({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-management\",\n                        payload: {\n                            mode: \"getsingle\",\n                            id,\n                            accessToken\n                        }\n                    })\n            })\n        })\n});\n// Export hooks\nconst { useRetrieveQuizQuery, useCreateQuizMutation, useDeleteQuizMutation, useUpdateQuizMutation, useGetSingleQuizQuery } = quizApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/quizApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/reportApi.ts":
/*!********************************************!*\
  !*** ./src/reduxRTK/services/reportApi.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reportApi: () => (/* binding */ reportApi),\n/* harmony export */   useCreateReportMutation: () => (/* binding */ useCreateReportMutation),\n/* harmony export */   useDeleteReportMutation: () => (/* binding */ useDeleteReportMutation),\n/* harmony export */   useGetSingleReportQuery: () => (/* binding */ useGetSingleReportQuery),\n/* harmony export */   useRetrieveReportQuery: () => (/* binding */ useRetrieveReportQuery),\n/* harmony export */   useUpdateReportMutation: () => (/* binding */ useUpdateReportMutation),\n/* harmony export */   useUpsertReportMutation: () => (/* binding */ useUpsertReportMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n\n// Define the custom base query\nconst customBaseQuery = async ({ url, payload }, { dispatch })=>{\n    try {\n        const response = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apicaller)(payload || {}, url);\n        if (response.success) {\n            if (response.data) {\n                // Handle RetrieveReportResponse\n                if (Array.isArray(response.data.reports) && typeof response.data.total === \"number\") {\n                    return {\n                        data: response\n                    };\n                }\n                // Handle GetSingleReportResponse\n                if (typeof response.data.id === \"string\" && typeof response.data.courseId === \"string\" && typeof response.data.userId === \"string\") {\n                    return {\n                        data: response\n                    };\n                }\n            }\n            // If `data` is null or not needed, return success and message only\n            return {\n                data: {\n                    success: response.success,\n                    message: response.message\n                }\n            };\n        } else {\n            // Handle token expiration\n            if (response.message === \"Invalid or expired token\") {\n                localStorage.removeItem(\"token\");\n                dispatch(reportApi.util.resetApiState());\n                window.location.href = \"/\";\n            }\n            return {\n                error: {\n                    message: response.message\n                }\n            };\n        }\n    } catch (error) {\n        return {\n            error: {\n                message: error.message || \"An unknown error occurred\"\n            }\n        };\n    }\n};\n// Define the API\nconst reportApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.createApi)({\n    reducerPath: \"reportApi\",\n    baseQuery: customBaseQuery,\n    endpoints: (builder)=>({\n            retrieveReport: builder.query({\n                query: ({ page, limit, courseId, userId, accessToken })=>{\n                    const params = new URLSearchParams();\n                    if (page !== undefined) params.append(\"page\", page.toString());\n                    if (limit !== undefined) params.append(\"limit\", limit.toString());\n                    if (courseId) params.append(\"courseId\", courseId);\n                    if (userId) params.append(\"userId\", userId);\n                    return {\n                        url: `main-quiz-report?${params.toString()}`,\n                        payload: {\n                            mode: \"retrieve\",\n                            accessToken\n                        }\n                    };\n                },\n                transformResponse: (response)=>({\n                        ...response,\n                        data: {\n                            ...response.data,\n                            reports: response.data.reports || []\n                        }\n                    })\n            }),\n            createReport: builder.mutation({\n                query: ({ courseId, userId, completed, score, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"createnew\",\n                            courseId,\n                            userId,\n                            completed,\n                            score,\n                            accessToken\n                        }\n                    })\n            }),\n            upsertReport: builder.mutation({\n                query: ({ courseId, userId, completed, score, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"upsert\",\n                            courseId,\n                            userId,\n                            completed,\n                            score,\n                            accessToken\n                        }\n                    })\n            }),\n            deleteReport: builder.mutation({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"delete\",\n                            id,\n                            accessToken\n                        }\n                    })\n            }),\n            updateReport: builder.mutation({\n                query: ({ id, courseId, userId, completed, score, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"update\",\n                            id,\n                            courseId,\n                            userId,\n                            completed,\n                            score,\n                            accessToken\n                        }\n                    })\n            }),\n            getSingleReport: builder.query({\n                query: ({ id, accessToken })=>({\n                        url: \"main-quiz-report\",\n                        payload: {\n                            mode: \"getsingle\",\n                            id,\n                            accessToken\n                        }\n                    })\n            })\n        })\n});\n// Export hooks\nconst { useRetrieveReportQuery, useCreateReportMutation, useUpsertReportMutation, useDeleteReportMutation, useUpdateReportMutation, useGetSingleReportQuery } = reportApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/reportApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/store/store.ts":
/*!*************************************!*\
  !*** ./src/reduxRTK/store/store.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: () => (/* binding */ store)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var _services_authApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services/authApi */ \"(ssr)/./src/reduxRTK/services/authApi.ts\");\n/* harmony import */ var _services_courseApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/courseApi */ \"(ssr)/./src/reduxRTK/services/courseApi.ts\");\n/* harmony import */ var _services_quizApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/quizApi */ \"(ssr)/./src/reduxRTK/services/quizApi.ts\");\n/* harmony import */ var _services_reportApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/reportApi */ \"(ssr)/./src/reduxRTK/services/reportApi.ts\");\n/* harmony import */ var _services_demoApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/demoApi */ \"(ssr)/./src/reduxRTK/services/demoApi.ts\");\n// store/index.ts\n\n\n\n\n\n\n// Create and configure the Redux store\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_5__.configureStore)({\n    reducer: {\n        [_services_authApi__WEBPACK_IMPORTED_MODULE_0__.authApi.reducerPath]: _services_authApi__WEBPACK_IMPORTED_MODULE_0__.authApi.reducer,\n        [_services_courseApi__WEBPACK_IMPORTED_MODULE_1__.courseApi.reducerPath]: _services_courseApi__WEBPACK_IMPORTED_MODULE_1__.courseApi.reducer,\n        [_services_quizApi__WEBPACK_IMPORTED_MODULE_2__.quizApi.reducerPath]: _services_quizApi__WEBPACK_IMPORTED_MODULE_2__.quizApi.reducer,\n        [_services_reportApi__WEBPACK_IMPORTED_MODULE_3__.reportApi.reducerPath]: _services_reportApi__WEBPACK_IMPORTED_MODULE_3__.reportApi.reducer,\n        [_services_demoApi__WEBPACK_IMPORTED_MODULE_4__.demoApi.reducerPath]: _services_demoApi__WEBPACK_IMPORTED_MODULE_4__.demoApi.reducer\n    },\n    middleware: (getDefaultMiddleware)=>getDefaultMiddleware().concat(_services_authApi__WEBPACK_IMPORTED_MODULE_0__.authApi.middleware, _services_courseApi__WEBPACK_IMPORTED_MODULE_1__.courseApi.middleware, _services_quizApi__WEBPACK_IMPORTED_MODULE_2__.quizApi.middleware, _services_reportApi__WEBPACK_IMPORTED_MODULE_3__.reportApi.middleware, _services_demoApi__WEBPACK_IMPORTED_MODULE_4__.demoApi.middleware)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/store/store.ts\n");

/***/ }),

/***/ "(ssr)/./src/css/satoshi.css":
/*!*****************************!*\
  !*** ./src/css/satoshi.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2216757f3ab5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY3NzL3NhdG9zaGkuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8uL3NyYy9jc3Mvc2F0b3NoaS5jc3M/ZWJkNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIyMTY3NTdmM2FiNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/css/satoshi.css\n");

/***/ }),

/***/ "(ssr)/./src/css/style.css":
/*!***************************!*\
  !*** ./src/css/style.css ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d5f3da26a5e8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY3NzL3N0eWxlLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9zcmMvY3NzL3N0eWxlLmNzcz9hMmZlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZDVmM2RhMjZhNWU4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/css/style.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\PROJECTS\FRANCIS ASANTE\nyansapo_\src\app\dashboard\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Dashboard_E_commerce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Dashboard/E-commerce */ \"(rsc)/./src/components/Dashboard/E-commerce.tsx\");\n/* harmony import */ var _components_Layouts_DefaultLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layouts/DefaultLayout */ \"(rsc)/./src/components/Layouts/DefaultLayout.tsx\");\n\n\n\nconst metadata = {\n    title: \"Nyansapo Admin Dashboard\",\n    description: \"Nyansapo Learning Portal\"\n};\nfunction Home() {\n    // useCheckAuthRedirect()\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layouts_DefaultLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_E_commerce__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 18,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 17,\n        columnNumber: 7\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2Rhc2hib2FyZC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBEO0FBRUs7QUFJeEQsTUFBTUUsV0FBcUI7SUFDaENDLE9BQ0U7SUFDRkMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQztJQUN0Qix5QkFBeUI7SUFDekIscUJBRUksOERBQUNKLHlFQUFhQTtrQkFDWiw0RUFBQ0Qsd0VBQVNBOzs7Ozs7Ozs7O0FBSWxCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4P2MxNWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEVDb21tZXJjZSBmcm9tIFwiQC9jb21wb25lbnRzL0Rhc2hib2FyZC9FLWNvbW1lcmNlXCI7XHJcbmltcG9ydCB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcclxuaW1wb3J0IERlZmF1bHRMYXlvdXQgZnJvbSBcIkAvY29tcG9uZW50cy9MYXlvdXRzL0RlZmF1bHRMYXlvdXRcIjtcclxuXHJcblxyXG5cclxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcclxuICB0aXRsZTpcclxuICAgIFwiTnlhbnNhcG8gQWRtaW4gRGFzaGJvYXJkXCIsXHJcbiAgZGVzY3JpcHRpb246IFwiTnlhbnNhcG8gTGVhcm5pbmcgUG9ydGFsXCIsXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xyXG4gIC8vIHVzZUNoZWNrQXV0aFJlZGlyZWN0KClcclxuICByZXR1cm4gKFxyXG4gICBcclxuICAgICAgPERlZmF1bHRMYXlvdXQ+XHJcbiAgICAgICAgPEVDb21tZXJjZSAvPlxyXG4gICAgICA8L0RlZmF1bHRMYXlvdXQ+XHJcbiAgXHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJFQ29tbWVyY2UiLCJEZWZhdWx0TGF5b3V0IiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiSG9tZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\PROJECTS\FRANCIS ASANTE\nyansapo_\src\app\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/Dashboard/E-commerce.tsx":
/*!*************************************************!*\
  !*** ./src/components/Dashboard/E-commerce.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\PROJECTS\FRANCIS ASANTE\nyansapo_\src\components\Dashboard\E-commerce.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/Layouts/DefaultLayout.tsx":
/*!**************************************************!*\
  !*** ./src/components/Layouts/DefaultLayout.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\PROJECTS\FRANCIS ASANTE\nyansapo_\src\components\Layouts\DefaultLayout.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"32x32\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLy4vc3JjL2FwcC9mYXZpY29uLmljbz9hZjc1Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjMyeDMyXCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@ant-design","vendor-chunks/@reduxjs","vendor-chunks/rc-util","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/@babel","vendor-chunks/reselect","vendor-chunks/redux","vendor-chunks/stylis","vendor-chunks/use-sync-external-store","vendor-chunks/@emotion","vendor-chunks/redux-thunk","vendor-chunks/jsvectormap","vendor-chunks/flatpickr","vendor-chunks/antd","vendor-chunks/@rc-component","vendor-chunks/rc-field-form","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-motion","vendor-chunks/rc-notification","vendor-chunks/rc-pagination","vendor-chunks/rc-textarea","vendor-chunks/rc-input","vendor-chunks/rc-collapse","vendor-chunks/rc-resize-observer","vendor-chunks/rc-tooltip","vendor-chunks/throttle-debounce","vendor-chunks/classnames","vendor-chunks/rc-picker","vendor-chunks/rc-table","vendor-chunks/rc-select","vendor-chunks/rc-menu","vendor-chunks/rc-tabs","vendor-chunks/rc-virtual-list","vendor-chunks/rc-tree","vendor-chunks/rc-progress","vendor-chunks/rc-overflow","vendor-chunks/rc-dropdown","vendor-chunks/toggle-selection","vendor-chunks/rc-checkbox","vendor-chunks/copy-to-clipboard"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=E%3A%5CPROJECTS%5CFRANCIS%20ASANTE%5Cnyansapo_%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CPROJECTS%5CFRANCIS%20ASANTE%5Cnyansapo_&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
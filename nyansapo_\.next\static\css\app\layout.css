/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/css/style.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: currentColor; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

body{
  position: relative;
  z-index: 1;
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
  font-family: Satoshi, sans-serif;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.visible{
  visibility: visible;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.sticky{
  position: sticky;
}
.inset-0{
  inset: 0px;
}
.\!right-\[3px\]{
  right: 3px !important;
}
.-bottom-4{
  bottom: -1rem;
}
.-bottom-6{
  bottom: -1.5rem;
}
.-left-4{
  left: -1rem;
}
.-left-6{
  left: -1.5rem;
}
.-right-0\.5{
  right: -0.125rem;
}
.-right-16{
  right: -4rem;
}
.-right-27{
  right: -6.75rem;
}
.-right-3{
  right: -0.75rem;
}
.-right-4{
  right: -1rem;
}
.-top-0\.5{
  top: -0.125rem;
}
.-top-3{
  top: -0.75rem;
}
.-top-4{
  top: -1rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-8{
  bottom: 2rem;
}
.left-0{
  left: 0px;
}
.left-2\.5{
  left: 0.625rem;
}
.left-\[3px\]{
  left: 3px;
}
.right-0{
  right: 0px;
}
.right-3{
  right: 0.75rem;
}
.right-4{
  right: 1rem;
}
.right-8{
  right: 2rem;
}
.top-0{
  top: 0px;
}
.top-1\/2{
  top: 50%;
}
.top-2\.5{
  top: 0.625rem;
}
.top-full{
  top: 100%;
}
.-z-1{
  z-index: -1;
}
.z-0{
  z-index: 0;
}
.z-1{
  z-index: 1;
}
.z-10{
  z-index: 10;
}
.z-20{
  z-index: 20;
}
.z-40{
  z-index: 40;
}
.z-50{
  z-index: 50;
}
.z-999{
  z-index: 999;
}
.z-9999{
  z-index: 9999;
}
.z-99999{
  z-index: 99999;
}
.col-span-12{
  grid-column: span 12 / span 12;
}
.m-0{
  margin: 0px;
}
.-mx-8{
  margin-left: -2rem;
  margin-right: -2rem;
}
.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4{
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.-mb-9{
  margin-bottom: -2.25rem;
}
.-ml-5{
  margin-left: -1.25rem;
}
.-mt-16{
  margin-top: -4rem;
}
.mb-0{
  margin-bottom: 0px;
}
.mb-10{
  margin-bottom: 2.5rem;
}
.mb-16{
  margin-bottom: 4rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-5\.5{
  margin-bottom: 1.375rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-4{
  margin-left: 1rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mr-3{
  margin-right: 0.75rem;
}
.mt-0\.5{
  margin-top: 0.125rem;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-10{
  margin-top: 2.5rem;
}
.mt-12{
  margin-top: 3rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-2\.5{
  margin-top: 0.625rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-8{
  margin-top: 2rem;
}
.line-clamp-2{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.hidden{
  display: none;
}
.size-6{
  width: 1.5rem;
  height: 1.5rem;
}
.\!h-0{
  height: 0px !important;
}
.h-0\.5{
  height: 0.125rem;
}
.h-10{
  height: 2.5rem;
}
.h-11\.5{
  height: 2.875rem;
}
.h-12{
  height: 3rem;
}
.h-12\.5{
  height: 3.125rem;
}
.h-14{
  height: 3.5rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-2\.5{
  height: 0.625rem;
}
.h-3{
  height: 0.75rem;
}
.h-3\.5{
  height: 0.875rem;
}
.h-4{
  height: 1rem;
}
.h-5{
  height: 1.25rem;
}
.h-5\.5{
  height: 1.375rem;
}
.h-6{
  height: 1.5rem;
}
.h-64{
  height: 16rem;
}
.h-7\.5{
  height: 1.875rem;
}
.h-8{
  height: 2rem;
}
.h-8\.5{
  height: 2.125rem;
}
.h-90{
  height: 22.5rem;
}
.h-auto{
  height: auto;
}
.h-full{
  height: 100%;
}
.h-screen{
  height: 100vh;
}
.min-h-\[400px\]{
  min-height: 400px;
}
.min-h-screen{
  min-height: 100vh;
}
.\!w-full{
  width: 100% !important;
}
.w-0{
  width: 0px;
}
.w-0\.5{
  width: 0.125rem;
}
.w-10{
  width: 2.5rem;
}
.w-11\.5{
  width: 2.875rem;
}
.w-12{
  width: 3rem;
}
.w-12\.5{
  width: 3.125rem;
}
.w-14{
  width: 3.5rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-3{
  width: 0.75rem;
}
.w-3\.5{
  width: 0.875rem;
}
.w-40{
  width: 10rem;
}
.w-5{
  width: 1.25rem;
}
.w-5\.5{
  width: 1.375rem;
}
.w-6{
  width: 1.5rem;
}
.w-62\.5{
  width: 15.625rem;
}
.w-72\.5{
  width: 18.125rem;
}
.w-75{
  width: 18.75rem;
}
.w-8{
  width: 2rem;
}
.w-8\.5{
  width: 2.125rem;
}
.w-full{
  width: 100%;
}
.min-w-47\.5{
  min-width: 11.875rem;
}
.min-w-full{
  min-width: 100%;
}
.max-w-2\.5{
  max-width: 0.625rem;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-3{
  max-width: 0.75rem;
}
.max-w-3xl{
  max-width: 48rem;
}
.max-w-4{
  max-width: 1rem;
}
.max-w-45{
  max-width: 11.25rem;
}
.max-w-4xl{
  max-width: 56rem;
}
.max-w-7xl{
  max-width: 80rem;
}
.max-w-full{
  max-width: 100%;
}
.max-w-lg{
  max-width: 32rem;
}
.max-w-md{
  max-width: 28rem;
}
.max-w-none{
  max-width: none;
}
.max-w-screen-2xl{
  max-width: 1536px;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.flex-grow{
  flex-grow: 1;
}
.grow{
  flex-grow: 1;
}
.table-auto{
  table-layout: auto;
}
.border-collapse{
  border-collapse: collapse;
}
.\!translate-x-full{
  --tw-translate-x: 100% !important;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;
}
.-translate-x-full{
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes ping{

  75%, 100%{
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping{
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes spin{

  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-pointer{
  cursor: pointer;
}
.list-disc{
  list-style-type: disc;
}
.appearance-none{
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12{
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.flex-col{
  flex-direction: column;
}
.flex-col-reverse{
  flex-direction: column-reverse;
}
.flex-wrap{
  flex-wrap: wrap;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-1{
  gap: 0.25rem;
}
.gap-12{
  gap: 3rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-2\.5{
  gap: 0.625rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-4{
  gap: 1rem;
}
.gap-4\.5{
  gap: 1.125rem;
}
.gap-5{
  gap: 1.25rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-8{
  gap: 2rem;
}
.gap-y-3{
  row-gap: 0.75rem;
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.overflow-y-hidden{
  overflow-y: hidden;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-3xl{
  border-radius: 1.5rem;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: 0.5rem;
}
.rounded-md{
  border-radius: 0.375rem;
}
.rounded-sm{
  border-radius: 0.125rem;
}
.rounded-xl{
  border-radius: 0.75rem;
}
.border{
  border-width: 1px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-8{
  border-width: 8px;
}
.border-\[0\.5px\]{
  border-width: 0.5px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-l-2{
  border-left-width: 2px;
}
.border-l-4{
  border-left-width: 4px;
}
.border-r{
  border-right-width: 1px;
}
.border-t{
  border-top-width: 1px;
}
.border-solid{
  border-style: solid;
}
.border-blue-200{
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-300{
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.border-blue-500{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-blue-800\/30{
  border-color: rgb(30 64 175 / 0.3);
}
.border-orange-500{
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.border-primary{
  --tw-border-opacity: 1;
  border-color: rgb(60 80 224 / var(--tw-border-opacity, 1));
}
.border-secondary{
  --tw-border-opacity: 1;
  border-color: rgb(128 202 238 / var(--tw-border-opacity, 1));
}
.border-stroke{
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-yellow-500{
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}
.border-t-transparent{
  border-top-color: transparent;
}
.bg-\[\#0FADCF\]{
  --tw-bg-opacity: 1;
  background-color: rgb(15 173 207 / var(--tw-bg-opacity, 1));
}
.bg-\[\#6577F3\]{
  --tw-bg-opacity: 1;
  background-color: rgb(101 119 243 / var(--tw-bg-opacity, 1));
}
.bg-\[\#8FD0EF\]{
  --tw-bg-opacity: 1;
  background-color: rgb(143 208 239 / var(--tw-bg-opacity, 1));
}
.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(28 36 52 / var(--tw-bg-opacity, 1));
}
.bg-blue-400\/20{
  background-color: rgb(96 165 250 / 0.2);
}
.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-current{
  background-color: currentColor;
}
.bg-cyan-500{
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));
}
.bg-gray{
  --tw-bg-opacity: 1;
  background-color: rgb(239 244 251 / var(--tw-bg-opacity, 1));
}
.bg-green-100{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-meta-1{
  --tw-bg-opacity: 1;
  background-color: rgb(220 53 69 / var(--tw-bg-opacity, 1));
}
.bg-meta-2{
  --tw-bg-opacity: 1;
  background-color: rgb(239 242 247 / var(--tw-bg-opacity, 1));
}
.bg-meta-6{
  --tw-bg-opacity: 1;
  background-color: rgb(255 186 0 / var(--tw-bg-opacity, 1));
}
.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-primary{
  --tw-bg-opacity: 1;
  background-color: rgb(60 80 224 / var(--tw-bg-opacity, 1));
}
.bg-primary\/10{
  background-color: rgb(60 80 224 / 0.1);
}
.bg-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(128 202 238 / var(--tw-bg-opacity, 1));
}
.bg-stroke{
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}
.bg-transparent{
  background-color: transparent;
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/95{
  background-color: rgb(255 255 255 / 0.95);
}
.bg-whiter{
  --tw-bg-opacity: 1;
  background-color: rgb(245 247 253 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-opacity-90{
  --tw-bg-opacity: 0.9;
}
.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-blue-50{
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500{
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-950{
  --tw-gradient-from: #172554 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(23 37 84 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-400{
  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-50{
  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 247 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500{
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-blue-900{
  --tw-gradient-to: rgb(30 58 138 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1e3a8a var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-blue-600{
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}
.to-blue-950{
  --tw-gradient-to: #172554 var(--tw-gradient-to-position);
}
.to-orange-600{
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}
.to-sky-500{
  --tw-gradient-to: #0ea5e9 var(--tw-gradient-to-position);
}
.to-white{
  --tw-gradient-to: #FFFFFF var(--tw-gradient-to-position);
}
.fill-current{
  fill: currentColor;
}
.fill-meta-3{
  fill: #10B981;
}
.fill-meta-5{
  fill: #259AE6;
}
.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}
.p-1\.5{
  padding: 0.375rem;
}
.p-2{
  padding: 0.5rem;
}
.p-3{
  padding: 0.75rem;
}
.p-4{
  padding: 1rem;
}
.p-6{
  padding: 1.5rem;
}
.p-7\.5{
  padding: 1.875rem;
}
.p-8{
  padding: 2rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-4\.5{
  padding-left: 1.125rem;
  padding-right: 1.125rem;
}
.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-7\.5{
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-7\.5{
  padding-top: 1.875rem;
  padding-bottom: 1.875rem;
}
.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-16{
  padding-bottom: 4rem;
}
.pb-5{
  padding-bottom: 1.25rem;
}
.pl-3{
  padding-left: 0.75rem;
}
.pl-6{
  padding-left: 1.5rem;
}
.pr-8{
  padding-right: 2rem;
}
.pt-20{
  padding-top: 5rem;
}
.pt-24{
  padding-top: 6rem;
}
.pt-7\.5{
  padding-top: 1.875rem;
}
.pt-8{
  padding-top: 2rem;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl{
  font-size: 3rem;
  line-height: 1;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-title-md{
  font-size: 24px;
  line-height: 30px;
}
.text-title-md2{
  font-size: 26px;
  line-height: 30px;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold{
  font-weight: 700;
}
.font-extrabold{
  font-weight: 800;
}
.font-medium{
  font-weight: 500;
}
.font-semibold{
  font-weight: 600;
}
.uppercase{
  text-transform: uppercase;
}
.italic{
  font-style: italic;
}
.leading-relaxed{
  line-height: 1.625;
}
.leading-tight{
  line-height: 1.25;
}
.tracking-tight{
  letter-spacing: -0.025em;
}
.tracking-wide{
  letter-spacing: 0.025em;
}
.tracking-wider{
  letter-spacing: 0.05em;
}
.tracking-widest{
  letter-spacing: 0.1em;
}
.text-\[\#98A6AD\]{
  --tw-text-opacity: 1;
  color: rgb(152 166 173 / var(--tw-text-opacity, 1));
}
.text-black{
  --tw-text-opacity: 1;
  color: rgb(28 36 52 / var(--tw-text-opacity, 1));
}
.text-blue-100{
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.text-blue-200{
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.text-blue-300{
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-blue-900{
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.text-bodydark2{
  --tw-text-opacity: 1;
  color: rgb(138 153 175 / var(--tw-text-opacity, 1));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-meta-3{
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}
.text-meta-5{
  --tw-text-opacity: 1;
  color: rgb(37 154 230 / var(--tw-text-opacity, 1));
}
.text-orange-600{
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-orange-800{
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.text-primary{
  --tw-text-opacity: 1;
  color: rgb(60 80 224 / var(--tw-text-opacity, 1));
}
.text-purple-700{
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.text-secondary{
  --tw-text-opacity: 1;
  color: rgb(128 202 238 / var(--tw-text-opacity, 1));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.underline{
  text-decoration-line: underline;
}
.opacity-0{
  opacity: 0;
}
.opacity-20{
  opacity: 0.2;
}
.opacity-60{
  opacity: 0.6;
}
.opacity-75{
  opacity: 0.75;
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2{
  --tw-shadow: 0px 1px 4px rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0px 1px 4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-card{
  --tw-shadow: 0px 1px 3px rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0px 1px 3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-default{
  --tw-shadow: 0px 8px 13px -3px rgba(0, 0, 0, 0.07);
  --tw-shadow-colored: 0px 8px 13px -3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-switcher{
  --tw-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2), inset 0px 2px 2px #FFFFFF, inset 0px -1px 1px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0px 2px 4px var(--tw-shadow-color), inset 0px 2px 2px var(--tw-shadow-color), inset 0px -1px 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.ring-2{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-blue-300{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));
}
.ring-orange-200{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 215 170 / var(--tw-ring-opacity, 1));
}
.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-1{
  --tw-drop-shadow: drop-shadow(0px 1px 0px #E2E8F0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-md{
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.\!delay-200{
  transition-delay: 200ms !important;
}
.\!delay-\[0\]{
  transition-delay: 0 !important;
}
.delay-150{
  transition-delay: 150ms;
}
.delay-200{
  transition-delay: 200ms;
}
.delay-300{
  transition-delay: 300ms;
}
.delay-500{
  transition-delay: 500ms;
}
.delay-\[0\]{
  transition-delay: 0;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.duration-75{
  transition-duration: 75ms;
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear{
  transition-timing-function: linear;
}
/* Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
        display: none;
    }
.no-scrollbar {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }

/* third-party libraries CSS */

.tableCheckbox:checked ~ div span{
  opacity: 1;
}

.tableCheckbox:checked ~ div{
  --tw-border-opacity: 1;
  border-color: rgb(60 80 224 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(60 80 224 / var(--tw-bg-opacity, 1));
}

.apexcharts-legend-text{
  --tw-text-opacity: 1 !important;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-legend-text:is(.dark *){
  --tw-text-opacity: 1 !important;
  color: rgb(174 183 192 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-text{
  fill: #64748B !important;
}

.apexcharts-text:is(.dark *){
  fill: #AEB7C0 !important;
}

.apexcharts-xcrosshairs{
  fill: #E2E8F0 !important;
}

.apexcharts-xcrosshairs:is(.dark *){
  fill: #2E3A47 !important;
}

.apexcharts-gridline{
  stroke: #E2E8F0 !important;
}

.apexcharts-gridline:is(.dark *){
  stroke: #2E3A47 !important;
}

.apexcharts-series.apexcharts-pie-series path:is(.dark *){
  stroke: transparent !important;
}

.apexcharts-legend-series{
  display: inline-flex !important;
  gap: 0.375rem;
}

.apexcharts-tooltip.apexcharts-theme-light:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(46 58 71 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(36 48 63 / var(--tw-bg-opacity, 1)) !important;
}

.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(46 58 71 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(49 61 74 / var(--tw-bg-opacity, 1)) !important;
}

.apexcharts-xaxistooltip:is(.dark *),
.apexcharts-yaxistooltip:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(49 61 74 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(49 61 74 / var(--tw-bg-opacity, 1)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(222 228 238 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom:after{
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(239 244 251 / var(--tw-border-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom:is(.dark *):after{
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(49 61 74 / var(--tw-border-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom:before{
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(239 244 251 / var(--tw-border-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom:is(.dark *):before{
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(49 61 74 / var(--tw-border-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom{
  border-radius: 0.25rem !important;
  border-style: none !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(239 244 251 / var(--tw-bg-opacity, 1)) !important;
  font-size: 0.75rem !important;
  line-height: 1rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(28 36 52 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-xaxistooltip-bottom:is(.dark *){
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-tooltip-series-group{
  padding-left: 0.375rem !important;
}

.flatpickr-wrapper{
  width: 100%;
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg{
  fill: #3C50E0 !important;
}

.flatpickr-calendar.arrowTop:is(.dark *):before{
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(36 48 63 / var(--tw-border-opacity, 1)) !important;
}

.flatpickr-calendar.arrowTop:is(.dark *):after{
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(36 48 63 / var(--tw-border-opacity, 1)) !important;
}

.flatpickr-calendar{
  padding: 1.5rem !important;
}

.flatpickr-calendar:is(.dark *){
  --tw-bg-opacity: 1 !important;
  background-color: rgb(36 48 63 / var(--tw-bg-opacity, 1)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(174 183 192 / var(--tw-text-opacity, 1)) !important;
  --tw-shadow: 1px 0 0 #313D4A, -1px 0 0 #313D4A, 0 1px 0 #313D4A, 0 -1px 0 #313D4A, 0 3px 13px rgb(0 0 0 / 8%) !important;
  --tw-shadow-colored: 1px 0 0 var(--tw-shadow-color), -1px 0 0 var(--tw-shadow-color), 0 1px 0 var(--tw-shadow-color), 0 -1px 0 var(--tw-shadow-color), 0 3px 13px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

@media (min-width: 375px){

  .flatpickr-calendar{
    width: auto !important;
  }
}

.flatpickr-day:is(.dark *){
  --tw-text-opacity: 1 !important;
  color: rgb(174 183 192 / var(--tw-text-opacity, 1)) !important;
}

.flatpickr-day:hover:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(49 61 74 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(49 61 74 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month{
  top: 1.75rem !important;
}

.flatpickr-months .flatpickr-prev-month:is(.dark *),
.flatpickr-months .flatpickr-next-month:is(.dark *){
  fill: #FFFFFF !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{
  left: 1.75rem !important;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month{
  right: 1.75rem !important;
}

span.flatpickr-weekday:is(.dark *),
.flatpickr-months .flatpickr-month:is(.dark *){
  fill: #FFFFFF !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.flatpickr-day.inRange {
    box-shadow: -5px 0 0 #f3f4f6, 5px 0 0 #f3f4f6 !important;
}

.flatpickr-day.inRange:is(.dark *){
  --tw-shadow: -5px 0 0 #313D4A, 5px 0 0 #313D4A !important;
  --tw-shadow-colored: -5px 0 0 var(--tw-shadow-color), 5px 0 0 var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus{
  --tw-border-opacity: 1 !important;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-day.inRange:is(.dark *),
.flatpickr-day.prevMonthDay.inRange:is(.dark *),
.flatpickr-day.nextMonthDay.inRange:is(.dark *),
.flatpickr-day.today.inRange:is(.dark *),
.flatpickr-day.prevMonthDay.today.inRange:is(.dark *),
.flatpickr-day.nextMonthDay.today.inRange:is(.dark *),
.flatpickr-day:hover:is(.dark *),
.flatpickr-day.prevMonthDay:hover:is(.dark *),
.flatpickr-day.nextMonthDay:hover:is(.dark *),
.flatpickr-day:focus:is(.dark *),
.flatpickr-day.prevMonthDay:focus:is(.dark *),
.flatpickr-day.nextMonthDay:focus:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(49 61 74 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(49 61 74 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-day.selected:is(.dark *),
.flatpickr-day.startRange:is(.dark *),
.flatpickr-day.selected:is(.dark *),
.flatpickr-day.endRange:is(.dark *){
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
    background: #3c50e0;
    --tw-border-opacity: 1 !important;
    border-color: rgb(60 80 224 / var(--tw-border-opacity, 1)) !important;
    --tw-bg-opacity: 1 !important;
    background-color: rgb(60 80 224 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.inRange:hover,
.flatpickr-day.startRange.inRange:hover,
.flatpickr-day.endRange.inRange:hover,
.flatpickr-day.selected:focus:hover,
.flatpickr-day.startRange:focus:hover,
.flatpickr-day.endRange:focus:hover,
.flatpickr-day.selected:hover:hover,
.flatpickr-day.startRange:hover:hover,
.flatpickr-day.endRange:hover:hover,
.flatpickr-day.selected.prevMonthDay:hover,
.flatpickr-day.startRange.prevMonthDay:hover,
.flatpickr-day.endRange.prevMonthDay:hover,
.flatpickr-day.selected.nextMonthDay:hover,
.flatpickr-day.startRange.nextMonthDay:hover,
.flatpickr-day.endRange.nextMonthDay:hover{
  --tw-border-opacity: 1 !important;
  border-color: rgb(60 80 224 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(60 80 224 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)) {
    box-shadow: -10px 0 0 #3c50e0;
}

.map-btn .jvm-zoom-btn{
  display: flex;
  height: 1.875rem;
  width: 1.875rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 0px;
  padding-right: 0px;
  padding-bottom: 0.125rem;
  padding-top: 0px;
  font-size: 1.5rem;
  line-height: 2rem;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}

.map-btn .jvm-zoom-btn:hover{
  --tw-border-opacity: 1;
  border-color: rgb(60 80 224 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(60 80 224 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.map-btn .jvm-zoom-btn:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(46 58 71 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(49 61 74 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(174 183 192 / var(--tw-text-opacity, 1));
}

.map-btn .jvm-zoom-btn:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(60 80 224 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(60 80 224 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.mapOne .jvm-zoom-btn{
  bottom: 0px !important;
  left: auto !important;
  top: auto !important;
}

.mapOne .jvm-zoom-btn.jvm-zoomin{
  right: 2.5rem !important;
}

.mapOne .jvm-zoom-btn.jvm-zoomout{
  right: 0px !important;
}

.taskCheckbox:checked ~ .box span{
  opacity: 1;
}

.taskCheckbox:checked ~ p{
  text-decoration-line: line-through;
}

.taskCheckbox:checked ~ .box{
  --tw-border-opacity: 1;
  border-color: rgb(60 80 224 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(60 80 224 / var(--tw-bg-opacity, 1));
}

.taskCheckbox:checked ~ .box:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(60 80 224 / var(--tw-border-opacity, 1));
}

.custom-input-date::-webkit-calendar-picker-indicator {
    background: transparent;
}

input[type="search"]::-webkit-search-cancel-button{
  -webkit-appearance: none;
          appearance: none;
}

.custom-input-date::-webkit-calendar-picker-indicator {
    background-position: center;
    background-repeat: no-repeat;
    background-size: 20px;
}

[x-cloak] {
    display: none !important;
}

.hover\:scale-105:hover{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-primary:hover{
  --tw-border-opacity: 1;
  border-color: rgb(60 80 224 / var(--tw-border-opacity, 1));
}

.hover\:border-primary\/90:hover{
  border-color: rgb(60 80 224 / 0.9);
}

.hover\:bg-blue-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-800:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-800\/50:hover{
  background-color: rgb(30 64 175 / 0.5);
}

.hover\:bg-gray:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(239 244 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-2:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(247 249 252 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-3:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary\/90:hover{
  background-color: rgb(60 80 224 / 0.9);
}

.hover\:bg-white:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}

.hover\:text-body:hover{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover{
  --tw-text-opacity: 1;
  color: rgb(60 80 224 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:shadow-card:hover{
  --tw-shadow: 0px 1px 3px rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0px 1px 3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group:hover .group-hover\:scale-105{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:opacity-100{
  opacity: 1;
}

.dark\:inline-block:is(.dark *){
  display: inline-block;
}

.dark\:hidden:is(.dark *){
  display: none;
}

.dark\:border-strokedark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(46 58 71 / var(--tw-border-opacity, 1));
}

.dark\:bg-black:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(28 36 52 / var(--tw-bg-opacity, 1));
}

.dark\:bg-boxdark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(36 48 63 / var(--tw-bg-opacity, 1));
}

.dark\:bg-boxdark-2:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(26 34 44 / var(--tw-bg-opacity, 1));
}

.dark\:bg-meta-4:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(49 61 74 / var(--tw-bg-opacity, 1));
}

.dark\:bg-white:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.dark\:text-bodydark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(174 183 192 / var(--tw-text-opacity, 1));
}

.dark\:text-white:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:drop-shadow-none:is(.dark *){
  --tw-drop-shadow: drop-shadow(0 0 #0000);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.dark\:invert:is(.dark *){
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.dark\:filter:is(.dark *){
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.dark\:hover\:bg-boxdark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(36 48 63 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-meta-4:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(49 61 74 / var(--tw-bg-opacity, 1));
}

@media (min-width: 375px){

  .\32xsm\:gap-4{
    gap: 1rem;
  }

  .\32xsm\:gap-7{
    gap: 1.75rem;
  }
}

@media (min-width: 640px){

  .sm\:right-0{
    right: 0px;
  }

  .sm\:block{
    display: block;
  }

  .sm\:flex{
    display: flex;
  }

  .sm\:w-1\/2{
    width: 50%;
  }

  .sm\:w-72{
    width: 18rem;
  }

  .sm\:w-80{
    width: 20rem;
  }

  .sm\:w-auto{
    width: auto;
  }

  .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:flex-nowrap{
    flex-wrap: nowrap;
  }

  .sm\:items-center{
    align-items: center;
  }

  .sm\:justify-between{
    justify-content: space-between;
  }

  .sm\:gap-2{
    gap: 0.5rem;
  }

  .sm\:gap-4{
    gap: 1rem;
  }

  .sm\:gap-5{
    gap: 1.25rem;
  }

  .sm\:px-12{
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:px-7\.5{
    padding-left: 1.875rem;
    padding-right: 1.875rem;
  }

  .sm\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:py-12{
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .sm\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }
}

@media (min-width: 768px){

  .md\:mt-0{
    margin-top: 0px;
  }

  .md\:flex{
    display: flex;
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:flex-row{
    flex-direction: row;
  }

  .md\:justify-end{
    justify-content: flex-end;
  }

  .md\:gap-6{
    gap: 1.5rem;
  }

  .md\:p-6{
    padding: 1.5rem;
  }

  .md\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px){

  .lg\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .lg\:ml-72\.5{
    margin-left: 18.125rem;
  }

  .lg\:block{
    display: block;
  }

  .lg\:hidden{
    display: none;
  }

  .lg\:w-1\/2{
    width: 50%;
  }

  .lg\:translate-x-0{
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:flex-row{
    flex-direction: row;
  }

  .lg\:justify-start{
    justify-content: flex-start;
  }

  .lg\:gap-16{
    gap: 4rem;
  }

  .lg\:p-12{
    padding: 3rem;
  }

  .lg\:px-16{
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .lg\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-20{
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .lg\:pb-20{
    padding-bottom: 5rem;
  }

  .lg\:pt-24{
    padding-top: 6rem;
  }

  .lg\:text-left{
    text-align: left;
  }

  .lg\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }
}

@media (min-width: 1280px){

  .xl\:col-span-4{
    grid-column: span 4 / span 4;
  }

  .xl\:col-span-5{
    grid-column: span 5 / span 5;
  }

  .xl\:col-span-8{
    grid-column: span 8 / span 8;
  }

  .xl\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1536px){

  .\32xl\:gap-7\.5{
    gap: 1.875rem;
  }

  .\32xl\:p-10{
    padding: 2.5rem;
  }

  .\32xl\:px-11{
    padding-left: 2.75rem;
    padding-right: 2.75rem;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./node_modules/jsvectormap/dist/jsvectormap.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
svg {
  touch-action: none;
}

image, text, .jvm-zoomin, .jvm-zoomout {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.jvm-container {
  touch-action: none;
  position: relative;
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.jvm-tooltip {
  border-radius: 3px;
  background-color: #5c5cff;
  font-family: sans-serif, Verdana;
  font-size: smaller;
  box-shadow: 1px 2px 12px rgba(0, 0, 0, 0.2);
  padding: 3px 5px;
  white-space: nowrap;
  position: absolute;
  display: none;
  color: #FFF;
}
.jvm-tooltip.active {
  display: block;
}

.jvm-zoom-btn {
  border-radius: 3px;
  background-color: #292929;
  padding: 3px;
  box-sizing: border-box;
  position: absolute;
  line-height: 10px;
  cursor: pointer;
  color: #FFF;
  height: 15px;
  width: 15px;
  left: 10px;
}
.jvm-zoom-btn.jvm-zoomout {
  top: 30px;
}
.jvm-zoom-btn.jvm-zoomin {
  top: 10px;
}

.jvm-series-container {
  right: 15px;
  position: absolute;
}
.jvm-series-container.jvm-series-h {
  bottom: 15px;
}
.jvm-series-container.jvm-series-v {
  top: 15px;
}
.jvm-series-container .jvm-legend {
  background-color: #FFF;
  border: 1px solid #e5e7eb;
  margin-left: 0.75rem;
  border-radius: 0.25rem;
  border-color: #e5e7eb;
  padding: 0.6rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  float: left;
}
.jvm-series-container .jvm-legend .jvm-legend-title {
  line-height: 1;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;
  margin-bottom: 0.575rem;
  text-align: left;
}
.jvm-series-container .jvm-legend .jvm-legend-inner {
  overflow: hidden;
}
.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick {
  overflow: hidden;
  min-width: 40px;
}
.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick:not(:first-child) {
  margin-top: 0.575rem;
}
.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick .jvm-legend-tick-sample {
  border-radius: 4px;
  margin-right: 0.65rem;
  height: 16px;
  width: 16px;
  float: left;
}
.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick .jvm-legend-tick-text {
  font-size: 12px;
  text-align: center;
  float: left;
}

.jvm-line[animation=true] {
  animation: jvm-line-animation 10s linear forwards infinite;
}
@keyframes jvm-line-animation {
  from {
    stroke-dashoffset: 250;
  }
}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./node_modules/flatpickr/dist/flatpickr.min.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;background:#fff;-webkit-box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,0.08);box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,0.08)}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible}.flatpickr-calendar.open{display:inline-block;z-index:99999}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown 300ms cubic-bezier(.23,1,.32,1);animation:fpFadeInDown 300ms cubic-bezier(.23,1,.32,1)}.flatpickr-calendar.inline{display:block;position:relative;top:2px}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px)}.flatpickr-calendar.static.open{z-index:999;display:block}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none !important;box-shadow:none !important}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #e6e6e6}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:'';height:0;width:0;left:22px}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%}.flatpickr-calendar:before{border-width:5px;margin:0 -5px}.flatpickr-calendar:after{border-width:4px;margin:0 -4px}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%}.flatpickr-calendar.arrowTop:before{border-bottom-color:#e6e6e6}.flatpickr-calendar.arrowTop:after{border-bottom-color:#fff}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%}.flatpickr-calendar.arrowBottom:before{border-top-color:#e6e6e6}.flatpickr-calendar.arrowBottom:after{border-top-color:#fff}.flatpickr-calendar:focus{outline:0}.flatpickr-wrapper{position:relative;display:inline-block}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-months .flatpickr-month{background:transparent;color:rgba(0,0,0,0.9);fill:rgba(0,0,0,0.9);height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:rgba(0,0,0,0.9);fill:rgba(0,0,0,0.9)}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{/*
      /*rtl:begin:ignore*/left:0/*
      /*rtl:end:ignore*/}/*
      /*rtl:begin:ignore*/
/*
      /*rtl:end:ignore*/
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{/*
      /*rtl:begin:ignore*/right:0/*
      /*rtl:end:ignore*/}/*
      /*rtl:begin:ignore*/
/*
      /*rtl:end:ignore*/
.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#959ea9}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill .1s;transition:fill .1s;fill:inherit}.numInputWrapper{position:relative;height:auto}.numInputWrapper input,.numInputWrapper span{display:inline-block}.numInputWrapper input{width:100%}.numInputWrapper input::-ms-clear{display:none}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(57,57,57,0.15);-webkit-box-sizing:border-box;box-sizing:border-box}.numInputWrapper span:hover{background:rgba(0,0,0,0.1)}.numInputWrapper span:active{background:rgba(0,0,0,0.2)}.numInputWrapper span:after{display:block;content:"";position:absolute}.numInputWrapper span.arrowUp{top:0;border-bottom:0}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(57,57,57,0.6);top:26%}.numInputWrapper span.arrowDown{top:50%}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(57,57,57,0.6);top:40%}.numInputWrapper span svg{width:inherit;height:auto}.numInputWrapper span svg path{fill:rgba(0,0,0,0.5)}.numInputWrapper:hover{background:rgba(0,0,0,0.05)}.numInputWrapper:hover span{opacity:1}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:.5ch;padding:0}.flatpickr-current-month span.cur-month:hover{background:rgba(0,0,0,0.05)}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\0;display:inline-block}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:rgba(0,0,0,0.9)}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:rgba(0,0,0,0.9)}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 .5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-current-month input.cur-year:focus{outline:0}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:rgba(0,0,0,0.5);background:transparent;pointer-events:none}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:transparent;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0 0;outline:none;padding:0 0 0 .5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:rgba(0,0,0,0.05)}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:transparent;outline:none;padding:0}.flatpickr-weekdays{background:transparent;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}span.flatpickr-weekday{cursor:default;font-size:90%;background:transparent;color:rgba(0,0,0,0.54);line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder}.dayContainer,.flatpickr-weeks{padding:1px 0 0 0}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px}.flatpickr-days:focus{outline:0}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0);opacity:1}.dayContainer + .dayContainer{-webkit-box-shadow:-1px 0 0 #e6e6e6;box-shadow:-1px 0 0 #e6e6e6}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#393939;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e6e6e6;border-color:#e6e6e6}.flatpickr-day.today{border-color:#959ea9}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#959ea9;background:#959ea9;color:#fff}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#569ff7;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#569ff7}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0}.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #569ff7;box-shadow:-10px 0 0 #569ff7}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:rgba(57,57,57,0.3);background:transparent;border-color:transparent;cursor:default}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:rgba(57,57,57,0.1)}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7;box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7}.flatpickr-day.hidden{visibility:hidden}.rangeMode .flatpickr-day{margin-top:1px}.flatpickr-weekwrapper{float:left}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;-webkit-box-shadow:1px 0 0 #e6e6e6;box-shadow:1px 0 0 #e6e6e6}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:rgba(57,57,57,0.3);background:transparent;cursor:default;border:none}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-time:after{content:"";display:table;clear:both}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#393939}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#393939}.flatpickr-time.hasSeconds .numInputWrapper{width:26%}.flatpickr-time.time24hr .numInputWrapper{width:49%}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#393939;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-time input.flatpickr-hour{font-weight:bold}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400}.flatpickr-time input:focus{outline:0;border:0}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#393939;font-weight:bold;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eee}.flatpickr-input[readonly]{cursor:pointer}@-webkit-keyframes fpFadeInDown{from{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}@keyframes fpFadeInDown{from{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/css/satoshi.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/**
 * @license
 *
 * Font Family: Satoshi
 * Designed by: Deni Anggara
 * URL: https://www.fontshare.com/fonts/satoshi
 * © 2023 Indian Type Foundry
 *
 * Font Styles:
 * Satoshi Light
 * Satoshi Light Italic
 * Satoshi Regular
 * Satoshi Italic
 * Satoshi Medium
 * Satoshi Medium Italic
 * Satoshi Bold
 * Satoshi Bold Italic
 * Satoshi Black
 * Satoshi Black Italic
 *
*/

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Light.d3f699ab.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Light.ce217c5d.woff) format("woff"),
    url(/_next/static/media/Satoshi-Light.121b151d.ttf) format("truetype");
    font-weight: 300;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-LightItalic.0d87c97a.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-LightItalic.51efbee6.woff) format("woff"),
    url(/_next/static/media/Satoshi-LightItalic.58b0e971.ttf) format("truetype");
    font-weight: 300;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Regular.b1dca2a5.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Regular.bb2accee.woff) format("woff"),
    url(/_next/static/media/Satoshi-Regular.a12eb4fb.ttf) format("truetype");
    font-weight: 400;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Italic.3eb4bb53.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Italic.43440d31.woff) format("woff"),
    url(/_next/static/media/Satoshi-Italic.84cd9c1d.ttf) format("truetype");
    font-weight: 400;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Medium.22539d17.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Medium.f3941e68.woff) format("woff"),
    url(/_next/static/media/Satoshi-Medium.8217b72e.ttf) format("truetype");
    font-weight: 500;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-MediumItalic.17afee50.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-MediumItalic.5450477c.woff) format("woff"),
    url(/_next/static/media/Satoshi-MediumItalic.14c46485.ttf) format("truetype");
    font-weight: 500;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Bold.12084922.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Bold.b28a04c4.woff) format("woff"),
    url(/_next/static/media/Satoshi-Bold.c60efc8f.ttf) format("truetype");
    font-weight: 700;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-BoldItalic.e51fcc53.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-BoldItalic.b59cf06f.woff) format("woff"),
    url(/_next/static/media/Satoshi-BoldItalic.c1d97e57.ttf) format("truetype");
    font-weight: 700;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-Black.c6d20a6b.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-Black.28873509.woff) format("woff"),
    url(/_next/static/media/Satoshi-Black.12d5a2e3.ttf) format("truetype");
    font-weight: 900;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src: url(/_next/static/media/Satoshi-BlackItalic.5400951d.woff2) format("woff2"),
    url(/_next/static/media/Satoshi-BlackItalic.22c3e8d9.woff) format("woff"),
    url(/_next/static/media/Satoshi-BlackItalic.33bc16b8.ttf) format("truetype");
    font-weight: 900;
    font-display: swap;
    font-style: italic;
}


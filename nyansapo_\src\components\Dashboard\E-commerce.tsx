"use client"
import React, { useEffect, useState } from "react";
import { BookOutlined, UserOutlined, CheckCircleOutlined, FileDoneOutlined, TrophyOutlined, TeamOutlined } from "@ant-design/icons";
import { useGetUsersQuery } from "../../reduxRTK/services/authApi";
import { useRetrieveCourseQuery } from "../../reduxRTK/services/courseApi";
import { useRetrieveReportQuery } from "../../reduxRTK/services/reportApi";
import CardDataStats from "../CardDataStats";
import ChartOne from "../Charts/ChartOne";
import { Spin, Card, Row, Col, Progress, Table, Tag, Typography } from "antd";

const { Title, Text } = Typography;

const ECommerce: React.FC = () => {
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [prevTotalUsers, setPrevTotalUsers] = useState<number>(0);
  const [prevTotalCourses, setPrevTotalCourses] = useState<number>(0);
  const [prevCompletedCourses, setPrevCompletedCourses] = useState<number>(0);
  const [prevAverageCourseRating, setPrevAverageCourseRating] = useState<number>(0);

  // Retrieve the access token on component mount
  useEffect(() => {
    const token = localStorage.getItem("token");
    setAccessToken(token);
  }, []);

  // Fetch users, courses, and reports
  const { data: usersData, isLoading: usersLoading } = useGetUsersQuery(
    { accessToken: accessToken || "" },
    { skip: !accessToken }
  );

  const { data: coursesData, isLoading: coursesLoading } = useRetrieveCourseQuery(
    { accessToken: accessToken || "" },
    { skip: !accessToken }
  );

  const { data: reportsData, isLoading: reportsLoading } = useRetrieveReportQuery(
    { accessToken: accessToken || "" },
    { skip: !accessToken }
  );

  // Calculate comprehensive analytics
  const totalUsers = usersData?.data?.total || 0;
  const totalCourses = coursesData?.data?.total || 0;
  const reports = reportsData?.data?.reports || [];
  const completedCourses = reports.filter((report) => report.completed).length;
  const totalReports = reports.length;
  const averageCourseRating = totalReports > 0
    ? (reports.reduce((sum, report) => sum + (report.score || 0), 0) / totalReports).toFixed(1)
    : "0.0";

  // Calculate completion rate
  const completionRate = totalReports > 0 ? ((completedCourses / totalReports) * 100).toFixed(1) : "0.0";

  // Calculate course performance data
  const coursePerformance = coursesData?.data?.courses?.map((course: any) => {
    const courseReports = reports.filter((report) => report.courseId === course.id);
    const courseCompleted = courseReports.filter((report) => report.completed).length;
    const courseTotal = courseReports.length;
    const courseCompletionRate = courseTotal > 0 ? ((courseCompleted / courseTotal) * 100).toFixed(1) : "0.0";
    const courseAvgScore = courseTotal > 0
      ? (courseReports.reduce((sum, report) => sum + (report.score || 0), 0) / courseTotal).toFixed(1)
      : "0.0";

    return {
      key: course.id,
      title: course.title,
      totalAssigned: courseTotal,
      completed: courseCompleted,
      completionRate: parseFloat(courseCompletionRate),
      averageScore: parseFloat(courseAvgScore),
    };
  }) || [];

  // Recent activity data
  const recentActivity = reports
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 10)
    .map((report, index) => ({
      key: index,
      user: `${report.userFirstName} ${report.userLastName}`,
      course: report.courseTitle,
      status: report.completed ? 'Completed' : 'In Progress',
      score: report.score || 0,
      date: new Date(report.createdAt).toLocaleDateString(),
    }));

  // Set previous values when the current values change
  useEffect(() => {
    if (usersData) setPrevTotalUsers(totalUsers);
    if (coursesData) setPrevTotalCourses(totalCourses);
    if (reportsData) setPrevCompletedCourses(completedCourses);
    if (reportsData) setPrevAverageCourseRating(parseFloat(averageCourseRating));
  }, [usersData, coursesData, reportsData, totalUsers, totalCourses, completedCourses, averageCourseRating]);

  // Loading state
  if (usersLoading || coursesLoading || reportsLoading || !accessToken) {
    return <div className="flex justify-center items-center w-full">
    <Spin size="large" />
  </div>;
  }

  // Data for ChartOne
  const chartData = {
    totalCoursesData: [23, 11, 22, 27, 13, 22, 37, 21, 44, 22, 30, 45], // Example dynamic data
    completedCoursesData: [30, 25, 36, 30, 45, 35, 64, 52, 59, 36, 39, 51], // Example dynamic data
    averageRatingData: [4.2, 4.5, 4.1, 4.3, 4.4, 4.0, 4.6, 4.3, 4.2, 4.5, 4.6, 4.7], // Example dynamic data
    months: ["Sep", "Oct", "Nov", "Dec", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug"],
  };

  // Table columns for course performance
  const courseColumns = [
    {
      title: 'Course Title',
      dataIndex: 'title',
      key: 'title',
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: 'Total Assigned',
      dataIndex: 'totalAssigned',
      key: 'totalAssigned',
      align: 'center' as const,
    },
    {
      title: 'Completed',
      dataIndex: 'completed',
      key: 'completed',
      align: 'center' as const,
    },
    {
      title: 'Completion Rate',
      dataIndex: 'completionRate',
      key: 'completionRate',
      align: 'center' as const,
      render: (rate: number) => (
        <Progress
          percent={rate}
          size="small"
          status={rate >= 80 ? 'success' : rate >= 50 ? 'active' : 'exception'}
        />
      ),
    },
    {
      title: 'Avg Score',
      dataIndex: 'averageScore',
      key: 'averageScore',
      align: 'center' as const,
      render: (score: number) => (
        <Tag color={score >= 80 ? 'green' : score >= 60 ? 'orange' : 'red'}>
          {score.toFixed(1)}%
        </Tag>
      ),
    },
  ];

  // Table columns for recent activity
  const activityColumns = [
    {
      title: 'User',
      dataIndex: 'user',
      key: 'user',
    },
    {
      title: 'Course',
      dataIndex: 'course',
      key: 'course',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'Completed' ? 'green' : 'blue'}>
          {status}
        </Tag>
      ),
    },
    {
      title: 'Score',
      dataIndex: 'score',
      key: 'score',
      render: (score: number) => `${score}%`,
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5">
        <CardDataStats
          title="Total Users"
          total={totalUsers.toString()}
          rate={`${totalUsers > prevTotalUsers ? '+' : ''}${totalUsers - prevTotalUsers}`}
          levelUp={totalUsers > prevTotalUsers}
          levelDown={totalUsers < prevTotalUsers}
        >
          <UserOutlined style={{ fontSize: "22px", color: "#1890ff" }} />
        </CardDataStats>

        <CardDataStats
          title="Total Courses"
          total={totalCourses.toString()}
          rate={`${totalCourses > prevTotalCourses ? '+' : ''}${totalCourses - prevTotalCourses}`}
          levelUp={totalCourses > prevTotalCourses}
          levelDown={totalCourses < prevTotalCourses}
        >
          <BookOutlined style={{ fontSize: "22px", color: "#52c41a" }} />
        </CardDataStats>

        <CardDataStats
          title="Completion Rate"
          total={`${completionRate}%`}
          rate={`${completedCourses}/${totalReports} completed`}
          levelUp={parseFloat(completionRate) >= 70}
          levelDown={parseFloat(completionRate) < 50}
        >
          <CheckCircleOutlined style={{ fontSize: "22px", color: "#faad14" }} />
        </CardDataStats>

        <CardDataStats
          title="Average Score"
          total={`${averageCourseRating}%`}
          rate={`From ${totalReports} reports`}
          levelUp={parseFloat(averageCourseRating) >= 75}
          levelDown={parseFloat(averageCourseRating) < 60}
        >
          <TrophyOutlined style={{ fontSize: "22px", color: "#f5222d" }} />
        </CardDataStats>
      </div>

      {/* Charts Section */}
      <Card title="Performance Overview" className="shadow-lg">
        <ChartOne
          totalCoursesData={chartData.totalCoursesData}
          completedCoursesData={chartData.completedCoursesData}
          averageRatingData={chartData.averageRatingData}
          months={chartData.months}
        />
      </Card>

      {/* Course Performance Table */}
      <Card
        title={
          <div className="flex items-center gap-2">
            <BookOutlined />
            <span>Course Performance Analytics</span>
          </div>
        }
        className="shadow-lg"
      >
        <Table
          columns={courseColumns}
          dataSource={coursePerformance}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 800 }}
        />
      </Card>

      {/* Recent Activity Table */}
      <Card
        title={
          <div className="flex items-center gap-2">
            <TeamOutlined />
            <span>Recent Learning Activity</span>
          </div>
        }
        className="shadow-lg"
      >
        <Table
          columns={activityColumns}
          dataSource={recentActivity}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 600 }}
        />
      </Card>
    </div>
  );
};

export default ECommerce;

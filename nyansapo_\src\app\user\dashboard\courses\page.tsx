"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";

// Simple course page component without external dependencies that might cause DOM issues
interface CourseData {
  title: string;
  description: string;
  [key: string]: any;
}

interface AuthData {
  token: string | null;
  userId: string | null;
}

export default function CoursePage() {
  const [mounted, setMounted] = useState(false);
  const [courseData, setCourseData] = useState<CourseData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [authData, setAuthData] = useState<AuthData>({ token: null, userId: null });

  const router = useRouter();
  const searchParams = useSearchParams();
  const courseid = searchParams?.get("courseid");

  // Ensure component is mounted before accessing localStorage
  useEffect(() => {
    setMounted(true);
  }, []);

  // Get auth data after component mounts
  useEffect(() => {
    if (!mounted) return;

    try {
      const token = localStorage.getItem("token");
      const userId = localStorage.getItem("userId");

      if (!token || !userId) {
        setError("Authentication required. Please log in.");
        setLoading(false);
        return;
      }

      setAuthData({ token, userId });
    } catch (err) {
      setError("Failed to retrieve authentication data.");
      setLoading(false);
    }
  }, [mounted]);

  // Fetch course data
  const fetchCourseData = useCallback(async () => {
    if (!authData.token || !courseid) return;

    try {
      setLoading(true);

      // Use the correct API base URL
      const BASEURL = 'https://nyansapo-api.vercel.app/api/';

      const response = await fetch(`${BASEURL}main-course-management`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: 'getsingle',
          id: courseid,
          accessToken: authData.token,
        }),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.data) {
        setCourseData(result.data);

        // Mark course as started
        try {
          await fetch(`${BASEURL}main-quiz-report`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              mode: 'upsert',
              userId: authData.userId,
              courseId: courseid,
              completed: false,
              score: null,
              accessToken: authData.token,
            }),
            credentials: 'include',
          });
        } catch (reportErr) {
          console.log("Could not mark course as started:", reportErr);
        }
      } else {
        throw new Error(result.message || "Failed to load course data");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch course data");
    } finally {
      setLoading(false);
    }
  }, [authData.token, authData.userId, courseid]);

  // Fetch data when auth is ready
  useEffect(() => {
    if (authData.token && courseid) {
      fetchCourseData();
    }
  }, [authData.token, courseid, fetchCourseData]);

  // Handle navigation
  const handleBackClick = useCallback(() => {
    router.push("/user/dashboard/lessons");
  }, [router]);

  // Don't render anything until mounted (prevents hydration issues)
  if (!mounted) {
    return null;
  }

  // Show error if no course ID
  if (!courseid) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px'
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <h2 style={{ color: '#ff4d4f', marginBottom: '16px' }}>Error</h2>
          <p>Course ID is missing from URL parameters.</p>
          <button
            onClick={handleBackClick}
            style={{
              marginTop: '20px',
              padding: '8px 16px',
              backgroundColor: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Back to Lessons
          </button>
        </div>
      </div>
    );
  }

  // Show loading state
  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px'
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid #f3f3f3',
            borderTop: '4px solid #1890ff',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 20px'
          }}></div>
          <p>Loading course data...</p>
          <p style={{ fontSize: '14px', color: '#666', marginTop: '10px' }}>
            Course ID: {courseid}
          </p>
          <p style={{ fontSize: '14px', color: '#666' }}>
            Auth: {authData.token ? 'Ready' : 'Checking...'}
          </p>
        </div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px'
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <h2 style={{ color: '#ff4d4f', marginBottom: '16px' }}>Error</h2>
          <p>{error}</p>
          <button
            onClick={handleBackClick}
            style={{
              marginTop: '20px',
              padding: '8px 16px',
              backgroundColor: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Back to Lessons
          </button>
        </div>
      </div>
    );
  }

  // Show course content
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f5f5f5',
      padding: '20px'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        padding: '40px'
      }}>
        <h1 style={{
          fontSize: '32px',
          fontWeight: 'bold',
          marginBottom: '20px',
          color: '#333'
        }}>
          {courseData?.title || 'Course Title'}
        </h1>

        <div style={{ marginBottom: '30px' }}>
          <p style={{
            fontSize: '18px',
            color: '#666',
            lineHeight: '1.6',
            marginBottom: '20px'
          }}>
            {courseData?.description || 'Course description will appear here.'}
          </p>
        </div>

        <button
          onClick={handleBackClick}
          style={{
            padding: '12px 24px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: '500'
          }}
          onMouseOver={(e) => (e.target as HTMLButtonElement).style.backgroundColor = '#40a9ff'}
          onMouseOut={(e) => (e.target as HTMLButtonElement).style.backgroundColor = '#1890ff'}
        >
          Back to Lessons
        </button>
      </div>
    </div>
  );
}

"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON>ton, <PERSON>, message } from "antd";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { useGetSingleCourseQuery } from "@/reduxRTK/services/courseApi";
import { useUpsertReportMutation } from "@/reduxRTK/services/reportApi";

export default function CoursePage({ params }: { params: { id: string } }) {
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const courseid = searchParams.get("courseid");

  // Retrieve access token and userId on component mount
  useEffect(() => {
    const token = localStorage.getItem("token");
    const userIdFromStorage = localStorage.getItem("userId");

    if (!token || !userIdFromStorage) {
      message.error("Access token or userId is missing. Please log in.");
      return;
    }

    setAccessToken(token);
    setUserId(userIdFromStorage);
  }, []);

  // Fetch course data using RTK Query
  const { data, isLoading, error } = useGetSingleCourseQuery(
    { id: courseid as string, accessToken: accessToken || "" },
    { skip: !accessToken },
  );

  const [upsertReport] = useUpsertReportMutation();
  const [hasMarkedAsStarted, setHasMarkedAsStarted] = useState(false);

  // Mark course as started when user first opens it
  useEffect(() => {
    if (!isLoading && data && userId && accessToken && courseid && !hasMarkedAsStarted) {
      const startedReportPayload = {
        userId,
        courseId: courseid,
        completed: false,
        score: null,
        accessToken,
      };

      upsertReport(startedReportPayload)
        .unwrap()
        .then(() => {
          console.log("Course marked as started");
          setHasMarkedAsStarted(true);
        })
        .catch((err) => {
          console.log("Error marking course as started:", err.message);
          setHasMarkedAsStarted(true);
        });
    }
  }, [data, isLoading, userId, accessToken, courseid, hasMarkedAsStarted]);

  if (isLoading) {
    return (
      <DefaultLayout>
        <div className="flex items-center justify-center">
          <Spin size="large" />
        </div>
      </DefaultLayout>
    );
  }

  if (error || !data?.success || !data?.data) {
    return (
      <DefaultLayout>
        <div className="flex items-center justify-center">
          <p>{error?.message || "Failed to load course data."}</p>
        </div>
      </DefaultLayout>
    );
  }

  const course = data?.data;

  return (
    <DefaultLayout>
      <div className="bg-white p-6">
        <h1 className="mb-4 text-3xl font-bold">{course.title}</h1>
        <div className="mb-6">
          <p className="text-gray-600 mb-4 text-lg">
            {course.description}
          </p>
        </div>
        <Button
          type="primary"
          onClick={() => router.push("/user/dashboard/lessons")}
        >
          Back to Lessons
        </Button>
      </div>
    </DefaultLayout>
  );
}

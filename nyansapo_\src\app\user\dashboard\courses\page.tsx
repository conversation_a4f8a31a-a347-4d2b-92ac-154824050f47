"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button, Spin, Modal, message, Progress } from "antd";
import { LeftOutlined, RightOutlined, FileOutlined } from "@ant-design/icons";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { useGetSingleCourseQuery } from "@/reduxRTK/services/courseApi";
import { useCreateReportMutation, useUpsertReportMutation } from "@/reduxRTK/services/reportApi";
import Image from "next/image";

import DOMPurify from "dompurify";

function splitContentByHeaders(content: string): string[] {
  const regex = /(<p>.*?<\/p>|<[^p][^>]*>.*?<\/[^>]+>)/gs; // Match <p> tags and non-<p> tags
  const splitContent: string[] = [];
  let match;

  while ((match = regex.exec(content))) {
    const tag = match[0]; // Entire match (either <p>, non-<p> tag, etc.)

    // Check if the tag is exactly <p>&nbsp;</p> (this should be treated as a page break)
    if (tag === "<p>&nbsp;</p>" || tag === "<p>&nbsp;</p> <p>&nbsp;</p>") {
      // Treat <p>&nbsp;</p> as a page break, separate content at this point
      splitContent.push('<div class="page-break"></div>');
      continue; // Skip adding <p>&nbsp;</p> itself, it's only for page break
    }

    // Add the current tag's content to the split content array
    splitContent.push(tag);
  }

  return splitContent;
}

export default function CoursePage({ params }: { params: { id: string } }) {
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [firstName, setFirstName] = useState<string | null>(null); // State for first name
  const [lastName, setLastName] = useState<string | null>(null); // State for last name
  const router = useRouter();
  const searchParams = useSearchParams();

  const courseid = searchParams.get("courseid");
  // Retrieve access token, userId, firstName, and lastName on component mount
  useEffect(() => {
    const token = localStorage.getItem("token");
    const userIdFromStorage = localStorage.getItem("userId");
    const firstNameFromStorage = localStorage.getItem("firstName");
    const lastNameFromStorage = localStorage.getItem("lastName");

    if (
      !token ||
      !userIdFromStorage ||
      !firstNameFromStorage ||
      !lastNameFromStorage
    ) {
      message.error(
        "Access token, userId, firstName, or lastName is missing. Please log in.",
      );
      return;
    }

    setAccessToken(token);
    setUserId(userIdFromStorage);
    setFirstName(firstNameFromStorage); // Set first name from localStorage
    setLastName(lastNameFromStorage); // Set last name from localStorage
  }, []);

  // Fetch course data using RTK Query
  const { data, isLoading, error } = useGetSingleCourseQuery(
    { id: courseid as string, accessToken: accessToken || "" },
    { skip: !accessToken },
  );

  const [currentParagraph, setCurrentParagraph] = useState(0);
  const [quizVisible, setQuizVisible] = useState(false);
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [score, setScore] = useState(0);
  const [timer, setTimer] = useState(0);
  const [timerInterval, setTimerInterval] = useState<any>(null);
  const [noQuizModalVisible, setNoQuizModalVisible] = useState(false);

  // Hook for creating a report
  const [createReport, { isLoading: isReportLoading, error: reportError }] =
    useCreateReportMutation();

  // Hook for upserting a report (create or update)
  const [upsertReport, { isLoading: isUpsertLoading }] = useUpsertReportMutation();

  // Track if we've already marked this course as started
  const [hasMarkedAsStarted, setHasMarkedAsStarted] = useState(false);

  useEffect(() => {
    if (!isLoading && data) {
      console.log("Fetched Course Data:", data);
    }
  }, [data, isLoading]);

  // Mark course as started when user first opens it
  useEffect(() => {
    if (!isLoading && data && userId && accessToken && courseid && !hasMarkedAsStarted) {
      // Use upsert to create a "started" report entry or update existing one
      const startedReportPayload = {
        userId,
        courseId: courseid,
        completed: false,
        score: null,
        accessToken,
      };

      upsertReport(startedReportPayload)
        .unwrap()
        .then(() => {
          console.log("Course marked as started");
          setHasMarkedAsStarted(true);
        })
        .catch((err) => {
          console.log("Error marking course as started:", err.message);
          setHasMarkedAsStarted(true); // Still mark as attempted to avoid infinite loops
        });
    }
  }, [data, isLoading, userId, accessToken, courseid, hasMarkedAsStarted, upsertReport]);

  if (isLoading) {
    return (
      <DefaultLayout>
        <div className="flex items-center justify-center">
          <Spin size="large" />
        </div>
      </DefaultLayout>
    );
  }

  if (error || !data?.success) {
    return (
      <DefaultLayout>
        <div className="flex items-center justify-center">
          <p>{error?.message || "Failed to load course data."}</p>
        </div>
      </DefaultLayout>
    );
  }

  const course = data?.data;

  const descriptionHtml = DOMPurify.sanitize(course.description);
  const paragraphs = splitContentByHeaders(descriptionHtml);
  const quizQuestions = course.quizzes || [];

  // Split content into pages based on <p>&nbsp;</p> markers

  const pages: string[][] = [];
  let currentPage: string[] = [];

  paragraphs.forEach((para) => {
    // Treat <div class="page-break"></div> or <p>&nbsp;</p> as page breaks
    if (
      para === '<div class="page-break"></div>' ||
      para === "<p>&nbsp;</p><p>&nbsp;</p>" ||
      para === "<p>&nbsp;</p>" ||
      para === '<div class="page-break"></div><div class="page-break"></div>'
    ) {
      if (
        currentPage.length > 0 &&
        currentPage.some((content) => content.trim() !== "")
      ) {
        // Push current page if it contains content
        pages.push([...currentPage]);
        currentPage = []; // Start a new page
      }
    } else {
      // Add non-page break content to the current page
      currentPage.push(para);
    }
  });

  // After loop, push the last page if it contains meaningful content
  if (
    currentPage.length > 0 &&
    currentPage.some((content) => content.trim() !== "")
  ) {
    pages.push(currentPage);
  }

  // Handle pagination
  const currentPageIndex = currentParagraph; // Current page index (use state or props)
  const currentContent = pages[currentPageIndex] || []; // Get content for the current page

  const hasNextPage = currentPageIndex < pages.length - 1;
  const hasPrevPage = currentPageIndex > 0;

  // When all content is displayed, show the Take Quiz button
  const isLastPage = currentPageIndex === pages.length - 1;

  const nextParagraph = () => {
    if (hasNextPage) {
      setCurrentParagraph(currentParagraph + 1);
    }
  };

  const prevParagraph = () => {
    if (hasPrevPage) {
      setCurrentParagraph(currentParagraph - 1);
    }
  };

  const handleAnswerChange = (questionIndex: number, answer: string) => {
    const updatedAnswers = [...selectedAnswers];
    updatedAnswers[questionIndex] = answer.trim(); // Normalize selected answer by trimming
    setSelectedAnswers(updatedAnswers);
  };

  const handleQuizSubmit = () => {
    let totalScore = 0;

    quizQuestions.forEach((q, index) => {
      // Normalize both the correct answer and the selected answer
      const correctAnswer = q.answer.trim().toLowerCase();
      const userAnswer = selectedAnswers[index]?.trim().toLowerCase();

      if (userAnswer === correctAnswer) {
        totalScore++;
      }
    });

    setScore(totalScore);
    setQuizCompleted(true);
    clearInterval(timerInterval);

    const scorePercentage = quizQuestions.length
      ? (totalScore / quizQuestions.length) * 100
      : 0;

    if (scorePercentage > 49) {
      if (userId && accessToken && courseid) {
        const reportPayload = {
          userId,
          courseId: courseid,
          completed: true,
          score: scorePercentage,
          accessToken,
        };
        createReport(reportPayload)
          .unwrap()
          .then(() => {
            message.success("Quiz results submitted successfully.");
          })
          .catch((err) => {
            message.error(`Failed to submit quiz results: ${err.message}`);
          });
      }
    } else {
      message.warning(
        "Your score is less than 50%. Please try again to improve your performance.",
      );
    }
  };

  const scorePercentage = quizQuestions.length
    ? (score / quizQuestions.length) * 100
    : 0;

  const startTimer = () => {
    if (quizCompleted) return;

    const totalTime = quizQuestions.length * 40;
    setTimer(totalTime);

    if (!timerInterval) {
      const interval = setInterval(() => {
        setTimer((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            setQuizCompleted(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      setTimerInterval(interval);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? "0" : ""}${remainingSeconds}`;
  };

  const closeQuiz = () => {
    router.replace("/user/dashboard/lessons");
    // setQuizVisible(false);
  };

  const handlePrintCertificate = () => {
    const printWindow = window.open("", "", "width=800,height=600");
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Print Certificate</title>
            <style>
              @page { size: A4 landscape; margin: 0; }
              body { font-family: 'Georgia', serif; margin: 0; padding: 0; -webkit-print-color-adjust: exact; }
              .certificate { 
                width: 297mm; 
                height: 210mm; 
                display: flex; 
                flex-direction: column; 
                align-items: center; 
                justify-content: center; 
                background: #ffffff; 
                border: 8px solid #3b82f6; 
                border-radius: 8px; 
                padding: 0mm 10mm; 
                box-sizing: border-box; 
                overflow: hidden; 
                text-align: center; 
              }
              .certificate h1 { 
                color: #1e40af; 
                font-size: 50px; 
                font-weight: bold; 
                margin-bottom: 20px; 
                letter-spacing: 3px; 
              }
              .certificate h2 { 
                color: #9333ea; 
                font-size: 36px; 
                font-weight: bold; 
                margin-top: 20px; 
                letter-spacing: 2px; 
              }
              .certificate p { 
                color: #374151; 
                font-size: 20px; 
                margin: 12px 0; 
              }
              .certificate span { font-weight: bold; }
              .certificate .status { 
                color: #1e40af; 
                font-weight: bold; 
                font-size: 26px; 
                margin-top: 15px; 
              }
              .certificate .seal { 
                width: 100px; 
                height: 100px; 
                background: radial-gradient(circle, #FFD700, #FFA500); 
                border: 5px solid #3b82f6; 
                border-radius: 50%; 
                position: absolute; 
                bottom: 10mm; 
                right: 25mm; 
                display: flex; 
                align-items: center; 
                justify-content: center; 
                font-size: 16px; 
                font-weight: bold; 
                color: #1e40af; 
              }
              .certificate .footer { 
                display: flex; 
                justify-content: space-between; 
                margin-top: 50px; 
                width: 100%; 
              }
              .certificate .footer div { text-align: left; }
              .certificate .footer p { margin: 0; }
            </style>
          </head>
          <body>
            <div class="certificate">
              <!-- Certificate Header -->
              <div class="text-center mb-6">
                <h1>Certificate of Completion</h1>
                <p>This certifies that <span class="font-semibold">${firstName} ${lastName}</span></p>
              </div>
  
              <!-- Certificate Body -->
              <div class="text-center mb-6">
                <p>has successfully completed the course:</p>
                <h2>${course.title}</h2>
                <p class="score">
                  with a score of <span class="font-bold text-blue-600">${scorePercentage}</span> 
                  and completion status: 
                  <span class="font-bold text-green-600">Completed </span>.
                </p>
              </div>
  
              <!-- Certificate Footer -->
              <div class="footer">
                <div>
                  <p>Date of Issue:</p>
                  <p>${new Date().toLocaleDateString()}</p>
                </div>
                <div>
                  <p>Authorized By:</p>
                  <p>Nyansapo Instructor Team</p>
                </div>
              </div>
  
              <!-- Seal -->
              <div class="seal">OFFICIAL</div>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  // Function to get congratulatory or motivational message
  const getCongratulatoryMessage = (percentage: number) => {
    if (percentage >= 90) {
      return "Excellent! You're a pro!";
    } else if (percentage >= 75) {
      return "Great job! You're almost there!";
    } else if (percentage >= 50) {
      return "Good effort! Keep going!";
    } else {
      return "Try again! Don't give up!";
    }
  };

  return (
    <DefaultLayout>
      <div className="bg-white p-6">
        {!quizVisible ? (
          <>
            <h1 className="mb-4 text-3xl font-bold">{course.title}</h1>

            {/* Render the image and description only on the first page */}
            {currentPageIndex === 0 && (
              <div className="mb-6">
                <div className="mb-6 flex flex-col items-center md:flex-row md:items-start">
                  {/* Image Section */}
                  <div className="relative h-64 w-full md:mr-6 md:h-40 md:w-1/3">
                    <Image
                      src={course.url}
                      alt={course.title}
                      fill={true}
                      style={{ objectFit: 'contain' }}
                      className="rounded-lg"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Render current page content */}
            {currentContent.length > 0 &&
              currentContent.map((para, index) => (
                <div
                  key={index}
                  className="text-gray-600 mb-4 text-lg"
                  dangerouslySetInnerHTML={{
                    __html: para,
                  }}
                />
              ))}

            {/* Navigation buttons */}
            <div className="mr-4 mt-6 flex items-center justify-end space-x-4">
              {/* Previous Button */}
              <Button
                type="primary"
                onClick={prevParagraph}
                disabled={currentPageIndex === 0}
                icon={<LeftOutlined />}
                className="mr-4"
              >
                Previous
              </Button>

              {/* Page number display */}
              <span className="text-sm">
                Page {currentPageIndex + 1} of {pages.length}
              </span>

              {/* Next Button or Take Quiz Button */}
              {hasNextPage ? (
                <Button
                  type="primary"
                  onClick={nextParagraph}
                  icon={<RightOutlined />}
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="primary"
                  onClick={() => {
                    if (quizQuestions.length === 0) {
                      setNoQuizModalVisible(true); // Show modal if no quizzes are available
                    } else {
                      setQuizVisible(true); // Display the quiz
                      startTimer(); // Start quiz timer
                    }
                  }}
                >
                  Take Quiz
                </Button>
              )}
            </div>
          </>
        ) : null}
        {/* Full-Screen Quiz Modal */}
        {quizVisible && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 200000000,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <div
              onClick={(e) => e.stopPropagation()}
              style={{
                backgroundColor: "#fff",
                padding: "0px 0px 40px",
                borderRadius: "8px",
                maxWidth: "900px",
                width: "100%",
                maxHeight: "90vh",
                overflowY: "auto",
              }}
            >
              <div className="sticky top-0 z-10 mb-4 mr-0 flex items-center justify-between bg-blue-500 p-4 shadow-md ">
                <h2 className="text-2xl font-bold text-white">
                  {course.title} Quiz
                </h2>
                <div className="text-2xl font-bold text-black">
                  Time {formatTime(timer)}
                </div>
              </div>

              {!quizCompleted ? (
                <>
                  {quizQuestions.map((question, index) => (
                    <div key={index} className="mb-8">
                      {/* Display Question Number */}
                      <p className="mb-4 pl-10 pr-10 text-lg font-bold">
                        {index + 1}. {question.question}
                      </p>

                      {/* Display Lettered Options */}
                      {question.options.map((option, optionIndex) => {
                        const optionLetter = String.fromCharCode(
                          65 + optionIndex,
                        ); // Convert to A, B, C, etc.

                        return (
                          <label
                            key={optionIndex}
                            className="mb-4 block flex cursor-pointer items-center space-x-2 pl-14 pr-14 text-lg"
                          >
                            <input
                              type="radio"
                              name={`question-${index}`}
                              value={option}
                              checked={selectedAnswers[index] === option}
                              onChange={() => handleAnswerChange(index, option)}
                              className="mr-2"
                            />
                            <span>
                              <strong>{optionLetter}.</strong> {option}
                            </span>
                          </label>
                        );
                      })}
                    </div>
                  ))}

                  {/* Submit Button */}
                  <div className="mr-4 mt-8 flex justify-end">
                    <Button
                      type="primary"
                      onClick={handleQuizSubmit}
                      disabled={selectedAnswers.length !== quizQuestions.length}
                    >
                      Submit Quiz
                    </Button>
                  </div>
                </>
              ) : (
                <>
                  <div className="text-center">
                    <Progress
                      type="circle"
                      percent={scorePercentage}
                      width={120}
                      format={(percent) => `${Math.round(percent ?? 0)}%`}
                    />
                    <h3 className="mt-4 text-xl font-bold">
                      {getCongratulatoryMessage(scorePercentage)}
                    </h3>
                    {scorePercentage >= 75 && (
                      <div className="mt-6">
                        <Button type="primary" onClick={handlePrintCertificate}>
                          Print Certificate
                        </Button>
                      </div>
                    )}
                    <Button
                      className="ml-2 mt-4"
                      type="default"
                      onClick={() => closeQuiz()}
                    >
                      Close
                    </Button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
        {/* Modal for No Quiz Available */}
        <Modal
          title={null} // Removed the default title for a custom layout
          visible={noQuizModalVisible}
          onOk={() => setNoQuizModalVisible(false)}
          onCancel={() => setNoQuizModalVisible(false)}
          footer={[
            <button
              key="close"
              onClick={() => setNoQuizModalVisible(false)}
              style={{
                backgroundColor: "#1890ff",
                color: "white",
                border: "none",
                borderRadius: "4px",
                padding: "8px 16px",
                cursor: "pointer",
              }}
            >
              Close
            </button>,
          ]}
          centered
        >
          <div style={{ textAlign: "center", padding: "20px" }}>
            <FileOutlined
              style={{
                fontSize: "48px",
                color: "#1890ff",
                marginBottom: "16px",
              }}
            />
            <h2
              style={{
                margin: "0 0 16px",
                fontWeight: "600",
                fontSize: "20px",
              }}
            >
              No Quiz Found
            </h2>
            <p
              style={{ margin: "0 0 24px", fontSize: "16px", color: "#595959" }}
            >
              This course does not contain any quizzes at the moment.
            </p>
          </div>
        </Modal>
      </div>
    </DefaultLayout>
  );
}

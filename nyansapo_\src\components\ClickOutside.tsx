import React, { useRef, useEffect, useCallback, useState } from "react";

interface Props {
  children: React.ReactNode;
  exceptionRef?: React.RefObject<HTMLElement>;
  onClick: () => void;
  className?: string;
}

const ClickOutside: React.FC<Props> = ({
  children,
  exceptionRef,
  onClick,
  className,
}) => {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const [mounted, setMounted] = useState(false);

  // Ensure component is mounted before adding event listeners
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Memoize the click handler to prevent unnecessary re-renders
  const handleClickListener = useCallback((event: MouseEvent) => {
    // Early return if component is not mounted
    if (!mounted) return;

    const target = event.target as Node;

    // Check if target is valid
    if (!target || !target.nodeType) return;

    let clickedInside = false;

    try {
      // Check if click is inside wrapper
      if (wrapperRef.current && wrapperRef.current.contains(target)) {
        clickedInside = true;
      }

      // Check exception ref if provided
      if (exceptionRef?.current) {
        if (exceptionRef.current === target || exceptionRef.current.contains(target)) {
          clickedInside = true;
        }
      }

      // Only trigger onClick if clicked outside and component is still mounted
      if (!clickedInside && mounted) {
        onClick();
      }
    } catch (error) {
      // Silently handle any DOM-related errors
      console.warn('ClickOutside: DOM operation failed', error);
    }
  }, [mounted, onClick, exceptionRef]);

  useEffect(() => {
    // Only add event listener if component is mounted
    if (!mounted) return;

    // Use a small delay to ensure DOM is ready
    const timeoutId = setTimeout(() => {
      if (mounted) {
        document.addEventListener("mousedown", handleClickListener, { passive: true });
      }
    }, 0);

    return () => {
      clearTimeout(timeoutId);
      // Safe cleanup - check if document and removeEventListener exist
      if (typeof document !== 'undefined' && document.removeEventListener) {
        document.removeEventListener("mousedown", handleClickListener);
      }
    };
  }, [mounted, handleClickListener]);

  return (
    <div ref={wrapperRef} className={className || ""}>
      {children}
    </div>
  );
};

export default ClickOutside;

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCoursesByUserController = void 0;
const services_1 = require("../services");
const getCoursesByUserController = async (req, res) => {
    const { mode, userId, accessToken } = req.body; // Extract mode, userId, and accessToken from body
    const { page = 1, limit = 50 } = req.query; // Extract page and limit from query params
    try {
        if (mode !== "getcoursesbyuser") {
            return res.status(400).json({
                success: false,
                message: "Invalid mode specified. Use 'getcoursesbyuser'.",
            });
        }
        if (!userId) {
            return res.status(400).json({
                success: false,
                message: "User ID is required to fetch courses.",
            });
        }
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);
        if (isNaN(pageNumber) || isNaN(limitNumber) || pageNumber < 1 || limitNumber < 1) {
            return res.status(400).json({
                success: false,
                message: "Invalid page or limit. Page and limit must be positive numbers.",
            });
        }
        const { courses, total } = await (0, services_1.getCoursesByUserId)(userId, pageNumber, limitNumber, accessToken);
        return res.status(200).json({
            success: true,
            message: "Courses retrieved successfully for the specified user.",
            data: { courses, total, page: pageNumber, limit: limitNumber },
        });
    }
    catch (error) {
        console.error("Error in getCoursesByUserController:", error);
        return res.status(500).json({
            success: false,
            message: "Failed to fetch courses for the user. Please try again later.",
        });
    }
};
exports.getCoursesByUserController = getCoursesByUserController;

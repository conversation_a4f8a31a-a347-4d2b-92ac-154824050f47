"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mainQuizManagementController = void 0;
const services_1 = require("../services"); // Import necessary services
// Main Quiz Management Controller
const mainQuizManagementController = async (req, res) => {
    const { mode, id, courseId, question, options, answer, accessToken } = req.body; // Extract mode and quiz details from the request body
    try {
        // Ensure accessToken is present and a string
        if (!accessToken || typeof accessToken !== "string") {
            return res.status(401).json({
                success: false,
                message: "Access token is required in the request body.",
            });
        }
        if (mode === "createnew") {
            // Create a new quiz
            const input = { courseId, question, options, answer }; // Prepare input for quiz creation
            const newQuiz = await (0, services_1.addQuiz)(input, accessToken); // Pass accessToken to service
            return res.status(201).json({
                success: true,
                message: "Quiz created successfully.",
                data: newQuiz,
            });
        }
        else if (mode === "retrieve") {
            // Retrieve quizzes with optional courseId filter
            const { page = 1, limit = 10 } = req.query;
            const pageNumber = parseInt(page, 10);
            const limitNumber = parseInt(limit, 10);
            // Validate pagination input
            if (isNaN(pageNumber) || isNaN(limitNumber) || pageNumber < 1 || limitNumber < 1) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid page or limit. Both must be positive numbers.",
                });
            }
            // Ensure courseId is a string or undefined
            const courseIdString = courseId ? courseId : undefined;
            // Call service to retrieve quizzes with optional courseId
            const { quizzes, total } = await (0, services_1.getQuizzes)(accessToken, courseIdString, pageNumber, limitNumber);
            return res.status(200).json({
                success: true,
                message: "Quizzes retrieved successfully.",
                data: {
                    quizzes,
                    total,
                    page: pageNumber,
                    limit: limitNumber,
                },
            });
        }
        else if (mode === "update") {
            // Update a quiz
            if (!id) {
                return res.status(400).json({
                    success: false,
                    message: "Quiz ID is required for update.",
                });
            }
            // Prepare input for quiz update
            const input = { id, courseId, question, options, answer };
            const updatedQuiz = await (0, services_1.updateQuiz)(input, accessToken); // Pass accessToken to service
            return res.status(200).json({
                success: true,
                message: "Quiz updated successfully.",
                data: updatedQuiz,
            });
        }
        else if (mode === "delete") {
            // Delete a quiz
            if (!id) {
                return res.status(400).json({
                    success: false,
                    message: "Quiz ID is required for deletion.",
                });
            }
            // Call service to delete quiz
            const isDeleted = await (0, services_1.deleteQuiz)(id, accessToken); // Pass accessToken to service
            return res.status(200).json({
                success: isDeleted,
                message: isDeleted ? "Quiz deleted successfully." : "Quiz deletion failed.",
            });
        }
        else {
            // Handle invalid mode
            return res.status(400).json({
                success: false,
                message: "Invalid mode specified. Supported modes: createnew, retrieve, update, delete.",
            });
        }
    }
    catch (error) {
        console.error("Error in mainQuizManagementController:", error);
        return res.status(500).json({
            success: false,
            message: "Failed to process request. Please try again later.",
        });
    }
};
exports.mainQuizManagementController = mainQuizManagementController;

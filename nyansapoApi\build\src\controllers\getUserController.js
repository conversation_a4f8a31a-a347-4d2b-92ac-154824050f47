"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserController = void 0;
const services_1 = require("../services");
const getUserController = async (req, res) => {
    const { mode, accessToken } = req.body; // Extract mode and accessToken from the request body
    const { page = 1, limit = 300 } = req.query; // Get page and limit from query params
    // Validate mode
    if (mode !== "retrieve") {
        return res.status(400).json({
            success: false,
            message: "Invalid mode for this operation.",
        });
    }
    try {
        // Convert page and limit to numbers and ensure valid inputs
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);
        if (isNaN(pageNumber) || isNaN(limitNumber) || pageNumber < 1 || limitNumber < 1) {
            return res.status(400).json({
                success: false,
                message: "Invalid page or limit. Page and limit must be positive numbers.",
            });
        }
        // Fetch users and total count (passing accessToken to the service)
        const { users, total } = await (0, services_1.getUsers)(pageNumber, limitNumber, accessToken);
        // Respond with a single 'data' object containing all details
        return res.status(200).json({
            success: true,
            message: "Users retrieved successfully.",
            data: {
                users,
                total,
                page: pageNumber,
                limit: limitNumber,
            },
        });
    }
    catch (error) {
        console.error("Error retrieving users:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error.",
        });
    }
};
exports.getUserController = getUserController;

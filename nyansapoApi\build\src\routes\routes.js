"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const middleware_1 = require("../middleware/middleware");
const router = express_1.default.Router();
const controllers_1 = require("../controllers");
router.post("/user/login", controllers_1.LoginController);
router.post("/user/register", controllers_1.CreateUserController);
router.post("/user/logout", controllers_1.Logout);
router.post('/user/check/auth', controllers_1.checkAuthController);
router.post('/user/getusers', middleware_1.authenticate, controllers_1.getUserController);
router.post('/user/delete', middleware_1.authenticate, controllers_1.deleteUserController);
router.post('/user/update', middleware_1.authenticate, controllers_1.updateUserController);
//course routes
router.post('/main-course-management', middleware_1.authenticate, controllers_1.mainCourseManagementController);
router.post('/course-by-user', middleware_1.authenticate, controllers_1.getCoursesByUserController);
//quiz routes
router.post('/main-quiz-management', middleware_1.authenticate, controllers_1.mainQuizManagementController);
//report routes
router.post('/main-quiz-report', middleware_1.authenticate, controllers_1.mainReportManagementController);
router.post("/user/send-details", middleware_1.authenticate, controllers_1.sendDetailsController);
router.post("/user/reset-password", controllers_1.resetPasswordController);
// Demo request route (no authentication needed)
router.post("/demo-request", controllers_1.demoRequestController);
exports.default = router;
// import express from "express";
// import { authenticate } from "../middleware/middleware";
// const router = express.Router();
// import { CreateUserController, Logout, LoginController, checkAuthController, getUserController, deleteUserController, updateUserController, mainCourseManagementController, mainQuizManagementController } from "../controllers";
// router.post("/user/login", LoginController);
// router.post("/user/register", CreateUserController);
// router.post("/user/logout", Logout);
// router.post('/user/check/auth', checkAuthController);
// router.post('/user/getusers', getUserController)
// router.post('/user/delete', deleteUserController);
// router.post('/user/update', updateUserController)
// //course routes
// router.post('/main-course-management',authenticate, mainCourseManagementController)
// //course routes
// router.post('/main-quiz-management',mainQuizManagementController)
// export default router;

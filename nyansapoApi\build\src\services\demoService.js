"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendDemoRequest = void 0;
const nodemailer_1 = __importDefault(require("nodemailer"));
const sendDemoRequest = async (data) => {
    const { firstName, lastName, email, company, jobTitle, phone, employees, message } = data;
    const fullName = `${firstName} ${lastName}`;
    // Configure Nodemailer using the exact same settings as auth service
    const transporter = nodemailer_1.default.createTransport({
        host: 'mail.nyansa-po.com',
        port: 587,
        secure: false, // Use STARTTLS instead of SSL
        auth: {
            user: process.env.EMAIL_USERNAME,
            pass: process.env.EMAIL_PASSWORD,
        },
        tls: {
            rejectUnauthorized: false // Accept self-signed certificates
        }
    });
    // Email to the company (notification) - using EMAIL_SUPPORT from .env
    const companyMailOptions = {
        from: process.env.EMAIL_USERNAME,
        to: process.env.EMAIL_SUPPORT || '<EMAIL>',
        subject: 'New Demo Request - NyansaPo',
        html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2563eb;">NyansaPo</h1>
          <h2 style="color: #1e293b;">New Demo Request</h2>
        </div>
        
        <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1e293b; margin-top: 0;">Contact Information</h3>
          <p><strong>Name:</strong> ${fullName}</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>
        </div>

        <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1e293b; margin-top: 0;">Company Information</h3>
          <p><strong>Company:</strong> ${company}</p>
          <p><strong>Job Title:</strong> ${jobTitle}</p>
          <p><strong>Number of Employees:</strong> ${employees || 'Not specified'}</p>
        </div>

        ${message ? `
        <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1e293b; margin-top: 0;">Message</h3>
          <p>${message}</p>
        </div>
        ` : ''}

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
          <p style="color: #64748b; font-size: 14px;">
            This demo request was submitted through the NyansaPo website.
            Please follow up with the prospect within 24 hours.
          </p>
        </div>
      </div>
    `,
    };
    // Email to the prospect (confirmation)
    const prospectMailOptions = {
        from: process.env.EMAIL_USERNAME,
        to: email,
        subject: 'Demo Request Received - NyansaPo',
        html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2563eb;">NyansaPo</h1>
        </div>
        
        <h2 style="color: #1e293b;">Thank you for your demo request!</h2>
        
        <p>Hello ${fullName},</p>
        
        <p>Thank you for your interest in NyansaPo's cybersecurity nano-learning platform. We've received your demo request and our team will contact you within 24 hours to schedule a personalized demonstration.</p>
        
        <div style="background-color: #dbeafe; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1e40af; margin-top: 0;">What to expect:</h3>
          <ul style="color: #1e293b;">
            <li>A personalized demo tailored to your organization's needs</li>
            <li>Discussion of how nano-learning can benefit your team</li>
            <li>Pricing and implementation options</li>
            <li>Q&A session with our cybersecurity experts</li>
          </ul>
        </div>
        
        <p>In the meantime, feel free to explore our website to learn more about our innovative approach to cybersecurity education.</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.FRONTEND_URL}" style="
            display: inline-block;
            padding: 12px 24px;
            background-color: #2563eb;
            color: #ffffff;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
          ">
            Visit Our Website
          </a>
        </div>
        
        <p>If you have any immediate questions, please don't hesitate to contact us at ${process.env.EMAIL_SUPPORT}.</p>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e2e8f0; text-align: center;">
          <p style="color: #64748b; font-size: 14px;">
            Best regards,<br>
            The NyansaPo Team<br>
            <a href="mailto:${process.env.EMAIL_SUPPORT}" style="color: #2563eb;">${process.env.EMAIL_SUPPORT}</a>
          </p>
        </div>
      </div>
    `,
    };
    // Log demo request for manual follow-up (always succeeds)
    console.log('=== DEMO REQUEST RECEIVED ===');
    console.log(`Name: ${fullName}`);
    console.log(`Email: ${email}`);
    console.log(`Company: ${company}`);
    console.log(`Job Title: ${jobTitle}`);
    console.log(`Phone: ${phone || 'Not provided'}`);
    console.log(`Employees: ${employees || 'Not specified'}`);
    console.log(`Message: ${message || 'No message'}`);
    console.log('============================');
    // Try to send emails, but don't fail if they don't work
    try {
        // Send company notification
        await transporter.sendMail(companyMailOptions);
        console.log(`✅ Company notification sent for demo request from ${email}`);
    }
    catch (error) {
        console.log(`⚠️ Company notification failed for ${email}:`, error.message);
    }
    // Wait a moment before sending the second email
    await new Promise(resolve => setTimeout(resolve, 1000));
    try {
        // Send prospect confirmation
        await transporter.sendMail(prospectMailOptions);
        console.log(`✅ Prospect confirmation sent to ${email}`);
    }
    catch (error) {
        console.log(`⚠️ Prospect confirmation failed for ${email}:`, error.message);
    }
    // Always succeed - the demo request is logged for manual follow-up
    console.log(`Demo request from ${email} processed successfully (manual follow-up may be required)`);
};
exports.sendDemoRequest = sendDemoRequest;

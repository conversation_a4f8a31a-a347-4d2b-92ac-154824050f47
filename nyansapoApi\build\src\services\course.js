"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAdminAnalytics = exports.getUserAssignments = exports.getCourseAssignments = exports.unassignCourseFromUsers = exports.assignCourseToUsers = exports.getSingleCourseWithQuiz = exports.getCoursesByUserId = exports.deleteCourse = exports.updateCourse = exports.getAllCourses = exports.addCourse = void 0;
const db_1 = require("../db/db");
const uuid_1 = require("uuid");
const schema_1 = require("../schema");
const nodemailer_1 = __importDefault(require("nodemailer"));
const validateUserIds = async (userIds) => {
    const existingUserIds = await db_1.db
        .selectFrom("User")
        .select("id")
        .where("id", "in", userIds)
        .execute();
    return userIds.every((id) => existingUserIds.some((user) => user.id === id));
};
/**
 * Send email notifications to users when courses are assigned
 */
const sendCourseAssignmentEmails = async (userIds, courseId) => {
    try {
        // Get user details and course details
        const users = await db_1.db
            .selectFrom("User")
            .select(["id", "firstName", "lastName", "email"])
            .where("id", "in", userIds)
            .execute();
        const course = await db_1.db
            .selectFrom("Course")
            .select(["title", "description"])
            .where("id", "=", courseId)
            .executeTakeFirst();
        if (!course || users.length === 0) {
            console.log("No users or course found for email notification");
            return;
        }
        // Configure Nodemailer
        const transporter = nodemailer_1.default.createTransport({
            host: 'mail.nyansa-po.com',
            port: 587,
            secure: false, // Use STARTTLS instead of SSL
            auth: {
                user: process.env.EMAIL_USERNAME,
                pass: process.env.EMAIL_PASSWORD,
            },
            tls: {
                rejectUnauthorized: false // Accept self-signed certificates
            }
        });
        // Send email to each user
        for (const user of users) {
            const email = user.email;
            const username = `${user.firstName} ${user.lastName}`;
            const mailOptions = {
                from: process.env.EMAIL_USERNAME,
                to: email,
                subject: `New Course Assigned: ${course.title}`,
                html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #2563eb;">NyansaPo</h1>
              <h2 style="color: #1e293b;">New Course Assignment</h2>
            </div>

            <p>Hello ${username},</p>
            <p>You have been assigned a new course: <strong>${course.title}</strong></p>

            ${course.description ? `<p><strong>Course Description:</strong><br>${course.description}</p>` : ''}

            <div style="background-color: #dbeafe; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p style="margin: 0; color: #1e40af;">
                <strong>Ready to start learning?</strong><br>
                Log in to your NyansaPo account to access your new course and begin your cybersecurity training journey.
              </p>
            </div>

            <p style="text-align: center; margin: 30px 0;">
              <a href="${process.env.FRONTEND_URL}/dashboard" target="_blank" style="
                display: inline-block;
                padding: 12px 24px;
                background-color: #2563eb;
                color: #ffffff;
                text-decoration: none;
                border-radius: 6px;
                font-weight: bold;
              ">
                Access Your Course
              </a>
            </p>

            <p>If you have any questions, feel free to contact our support team: ${process.env.EMAIL_SUPPORT}</p>

            <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e2e8f0; text-align: center;">
              <p style="color: #64748b; font-size: 14px;">
                Best regards,<br>
                The NyansaPo Team<br>
                <a href="mailto:${process.env.EMAIL_SUPPORT}" style="color: #2563eb;">${process.env.EMAIL_SUPPORT}</a>
              </p>
            </div>
          </div>
        `,
            };
            try {
                await transporter.sendMail(mailOptions);
                console.log(`✅ Course assignment notification sent to ${email}`);
            }
            catch (error) {
                console.log(`⚠️ Failed to send course assignment notification to ${email}:`, error.message);
            }
        }
    }
    catch (error) {
        console.error("Error sending course assignment emails:", error);
    }
};
const addCourse = async (input, accessToken // Access token is included but not validated
) => {
    const parsedInput = schema_1.addCourseSchema.safeParse(input);
    if (!parsedInput.success) {
        throw new Error("Invalid input: " +
            parsedInput.error.errors.map((err) => err.message).join(", "));
    }
    const { title, description, url, userIds } = parsedInput.data;
    const courseId = (0, uuid_1.v4)();
    try {
        // If userIds are provided, validate them
        if (userIds && userIds.length > 0) {
            const areUserIdsValid = await validateUserIds(userIds);
            if (!areUserIdsValid) {
                throw new Error("One or more provided userIds do not exist.");
            }
        }
        // Insert into the Course table
        const [newCourse] = await db_1.db
            .insertInto("Course")
            .values({
            id: courseId,
            title,
            description,
            url,
            createdAt: new Date(),
            updatedAt: new Date(),
        })
            .returning([
            "id",
            "title",
            "description",
            "url",
            "createdAt",
            "updatedAt",
        ])
            .execute();
        // Insert into the UserCourse table if userIds are provided
        if (userIds && userIds.length > 0) {
            const userCourseEntries = userIds.map((userId) => ({
                id: (0, uuid_1.v4)(),
                userId,
                courseId,
                createdAt: new Date(),
            }));
            await db_1.db.insertInto("UserCourse").values(userCourseEntries).execute();
        }
        // Return the newly created course
        return newCourse;
    }
    catch (error) {
        console.error("Error adding course:", error);
        throw new Error("Unable to add course. Please try again later.");
    }
};
exports.addCourse = addCourse;
const getAllCourses = async (page, limit = 10, // Default limit to 10 if not provided
accessToken // Access token included as part of the request body
) => {
    try {
        // Ensure page and limit are positive integers
        if (page < 1 || limit < 1) {
            throw new Error("Page and limit must be greater than 0.");
        }
        // Fetch the courses data with pagination
        const courses = await db_1.db
            .selectFrom("Course")
            .select(["id", "title", "description", "url", "createdAt", "updatedAt"])
            .orderBy("createdAt", "desc") // Order courses by creation date (desc)
            .offset((page - 1) * limit) // Calculate the offset for pagination
            .limit(limit) // Limit to the specified number of records per page
            .execute();
        // Get the total number of courses using COUNT (more efficient than fetching all rows)
        const totalResult = await db_1.db
            .selectFrom("Course")
            .select([db_1.db.fn.count("id").as("total")]) // Use COUNT to get the total number of courses
            .execute();
        // Convert total count to number
        const total = Number(totalResult[0].total); // Convert the total count to a number
        // Return courses data and total course count
        return {
            courses: courses, // Return the courses data
            total, // Total count for pagination
        };
    }
    catch (error) {
        console.error("Error fetching courses:", error);
        throw new Error("Unable to fetch courses. Please try again later.");
    }
};
exports.getAllCourses = getAllCourses;
const updateCourse = async (input, accessToken // Accept accessToken as part of the request body
) => {
    // Validate input using schema
    const parsedInput = schema_1.updateCourseSchema.safeParse(input);
    if (!parsedInput.success) {
        throw new Error("Invalid input: " +
            parsedInput.error.errors.map((err) => err.message).join(", "));
    }
    const { id, title, description, url, userIds } = parsedInput.data;
    try {
        // Validate userIds if provided
        if (userIds && userIds.length > 0) {
            const areUserIdsValid = await validateUserIds(userIds);
            if (!areUserIdsValid) {
                throw new Error("One or more provided userIds do not exist.");
            }
        }
        // Check if the course exists
        const existingCourse = await db_1.db
            .selectFrom("Course")
            .where("id", "=", id)
            .select(["id"])
            .executeTakeFirst();
        if (!existingCourse) {
            throw new Error("Course not found");
        }
        // Start a transaction to ensure atomic updates
        const updatedCourse = await db_1.db.transaction().execute(async (trx) => {
            // Update the course in the database
            const [updated] = await trx
                .updateTable("Course")
                .set({
                title,
                description,
                url,
                updatedAt: new Date(),
            })
                .where("id", "=", id)
                .returning(["id", "title", "description", "url", "createdAt", "updatedAt"])
                .execute();
            // Update UserCourse associations if userIds are provided
            if (userIds && userIds.length > 0) {
                // Delete existing UserCourse associations for this course
                await trx.deleteFrom("UserCourse").where("courseId", "=", id).execute();
                // Insert new UserCourse associations
                const userCourseEntries = userIds.map((userId) => ({
                    id: (0, uuid_1.v4)(),
                    userId,
                    courseId: id,
                    createdAt: new Date(),
                }));
                await trx.insertInto("UserCourse").values(userCourseEntries).execute();
            }
            // Fetch associated users after updating the UserCourse table
            const associatedUsers = await trx
                .selectFrom("UserCourse")
                .innerJoin("User", "UserCourse.userId", "User.id")
                .where("UserCourse.courseId", "=", id)
                .select("User.id") // Correct way to specify fields
                .execute();
            // Include associated users in the returned course data
            return { ...updated, users: associatedUsers };
        });
        // Return the updated course including associated users
        return updatedCourse;
    }
    catch (error) {
        console.error("Error updating course:", error);
        throw new Error("Unable to update course. Please try again later.");
    }
};
exports.updateCourse = updateCourse;
const deleteCourse = async (id, accessToken // Accept accessToken as part of the request body
) => {
    try {
        const courseExists = await db_1.db
            .selectFrom("Course")
            .where("id", "=", id)
            .select(["id"])
            .execute();
        if (courseExists.length === 0) {
            throw new Error("Course not found");
        }
        // Delete UserCourse associations first
        await db_1.db.deleteFrom("UserCourse").where("courseId", "=", id).execute();
        // Delete the course
        await db_1.db.deleteFrom("Course").where("id", "=", id).executeTakeFirst();
    }
    catch (error) {
        console.error("Error deleting course:", error);
        throw new Error("Unable to delete course. Please try again later.");
    }
};
exports.deleteCourse = deleteCourse;
const getCoursesByUserId = async (userId, pageNumber, limitNumber = 10, accessToken // Accept accessToken as part of the request body
) => {
    try {
        if (!userId) {
            throw new Error("User ID is required to fetch courses.");
        }
        // Log pagination inputs
        console.log("Pagination Inputs:", { pageNumber, limitNumber });
        // Fetch courses with completion status and associated quizzes with pagination
        const result = await db_1.db
            .selectFrom("UserCourse")
            .innerJoin("Course", "UserCourse.courseId", "Course.id")
            .leftJoin("Quiz", "Quiz.courseId", "Course.id")
            .leftJoin("Report", (join) => join
            .onRef("Report.courseId", "=", "Course.id")
            .onRef("Report.userId", "=", "UserCourse.userId"))
            .where("UserCourse.userId", "=", userId)
            .limit(limitNumber) // Apply limit for pagination
            .offset((pageNumber - 1) * limitNumber) // Apply offset for pagination
            .select([
            "Course.id as courseId",
            "Course.title as courseTitle",
            "Course.description as courseDescription",
            "Course.url as courseUrl",
            "Course.createdAt as courseCreatedAt",
            "Course.updatedAt as courseUpdatedAt",
            "UserCourse.createdAt as assignedAt",
            "Quiz.id as quizId",
            "Quiz.question as quizQuestion",
            "Quiz.options as quizOptions",
            "Quiz.answer as quizAnswer",
            "Report.completed as isCompleted",
            "Report.score as score",
        ])
            .execute();
        console.log("Paginated Query Results:", result);
        console.log("Sample row isCompleted values:", result.slice(0, 3).map(r => ({ title: r.courseTitle, isCompleted: r.isCompleted, score: r.score })));
        // Debug: Show all unique course-report combinations
        const uniqueCourses = [...new Set(result.map(r => r.courseId))];
        console.log("Unique courses and their report status:");
        uniqueCourses.forEach(courseId => {
            const courseRows = result.filter(r => r.courseId === courseId);
            const firstRow = courseRows[0];
            console.log(`- ${firstRow.courseTitle}: isCompleted=${firstRow.isCompleted}, score=${firstRow.score}`);
        });
        // Fetch total count of courses for the user
        const totalResult = await db_1.db
            .selectFrom("UserCourse")
            .innerJoin("Course", "UserCourse.courseId", "Course.id")
            .where("UserCourse.userId", "=", userId)
            .select([db_1.db.fn.count("Course.id").as("total")])
            .execute();
        const total = Number(totalResult[0].total);
        // Map results to CustomCourseWithStatus format
        const courseMap = {};
        result.forEach((row) => {
            const courseId = row.courseId;
            if (!courseMap[courseId]) {
                courseMap[courseId] = {
                    id: courseId,
                    title: row.courseTitle,
                    description: row.courseDescription,
                    url: row.courseUrl,
                    createdAt: new Date(row.courseCreatedAt),
                    updatedAt: new Date(row.courseUpdatedAt),
                    assignedAt: new Date(row.assignedAt),
                    isCompleted: row.isCompleted, // Keep null if no report exists, boolean if report exists
                    score: row.score,
                    quizzes: [],
                };
            }
            if (row.quizId) {
                courseMap[courseId].quizzes.push({
                    id: row.quizId,
                    question: row.quizQuestion || "",
                    options: row.quizOptions ?? [],
                    answer: row.quizAnswer || "",
                });
            }
        });
        const courses = Object.values(courseMap);
        // Sort courses by assignment date (most recent first)
        courses.sort((a, b) => new Date(b.assignedAt).getTime() - new Date(a.assignedAt).getTime());
        // Filter out completed courses (hide them completely)
        const filteredCourses = courses.filter(course => {
            // Hide completed courses
            return !course.isCompleted;
        });
        // Categorize remaining courses with proper logic
        filteredCourses.forEach(course => {
            // A course is "in progress" if:
            // 1. A report exists (meaning user has opened/started the course) AND
            // 2. It's not completed yet
            //
            // Note: course.isCompleted will be null if no report exists, false if report exists but not completed
            const hasReport = course.isCompleted !== null; // Report exists if isCompleted is not null
            const isInProgress = hasReport && course.isCompleted === false; // Explicitly check for false (started but not completed)
            if (isInProgress) {
                // In progress courses - user has started (report exists) but not completed
                course.isLatest = false;
                course.isInProgress = true;
                console.log(`Course ${course.title} marked as IN PROGRESS (hasReport: ${hasReport}, completed: ${course.isCompleted}, score: ${course.score})`);
            }
            else {
                // New/untaken courses - no report exists yet (never opened)
                course.isLatest = true;
                course.isInProgress = false;
                console.log(`Course ${course.title} marked as NEW (hasReport: ${hasReport}, completed: ${course.isCompleted}, score: ${course.score})`);
            }
        });
        // Final debug: Show the categorization results
        console.log("Final course categorization:");
        filteredCourses.forEach(course => {
            console.log(`- ${course.title}: isLatest=${course.isLatest}, isInProgress=${course.isInProgress}, isCompleted=${course.isCompleted}`);
        });
        return { courses: filteredCourses, total: filteredCourses.length };
    }
    catch (error) {
        console.error("Error fetching courses by user ID:", error);
        throw new Error("Unable to fetch courses for this user.");
    }
};
exports.getCoursesByUserId = getCoursesByUserId;
const getSingleCourseWithQuiz = async (courseId, accessToken // Accept accessToken as part of the request body
) => {
    try {
        if (!courseId) {
            throw new Error("Course ID is required to fetch the course.");
        }
        const result = await db_1.db
            .selectFrom("Course")
            .leftJoin("Quiz", "Quiz.courseId", "Course.id")
            .where("Course.id", "=", courseId)
            .select([
            "Course.id as courseId",
            "Course.title as courseTitle",
            "Course.description as courseDescription",
            "Course.url as courseUrl",
            "Course.createdAt as courseCreatedAt",
            "Course.updatedAt as courseUpdatedAt",
            "Quiz.id as quizId",
            "Quiz.question as quizQuestion",
            "Quiz.options as quizOptions",
            "Quiz.answer as quizAnswer",
        ])
            .execute();
        if (result.length === 0) {
            return null; // Return null if no course is found
        }
        // Helper function to shuffle an array
        const shuffleArray = (array) => {
            for (let i = array.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [array[i], array[j]] = [array[j], array[i]];
            }
            return array;
        };
        // Map the course data with quizzes
        const courseData = {
            id: result[0].courseId,
            title: result[0].courseTitle,
            description: result[0].courseDescription,
            url: result[0].courseUrl,
            createdAt: new Date(result[0].courseCreatedAt),
            updatedAt: new Date(result[0].courseUpdatedAt),
            quizzes: shuffleArray(result
                .filter((row) => row.quizId) // Filter out rows with no quiz data
                .map((row) => ({
                id: row.quizId, // Ensure id is treated as a string
                question: row.quizQuestion || "",
                options: row.quizOptions ?? [],
                answer: row.quizAnswer || "",
            }))),
        };
        return courseData;
    }
    catch (error) {
        console.error("Error fetching course with quiz:", error);
        throw new Error("Unable to fetch course and quiz. Please try again later.");
    }
};
exports.getSingleCourseWithQuiz = getSingleCourseWithQuiz;
// ============================================================================
// NEW ASSIGNMENT MANAGEMENT FUNCTIONS
// ============================================================================
/**
 * Assign a course to multiple users (prevents duplicates)
 * @param courseId - The course ID to assign
 * @param userIds - Array of user IDs to assign the course to
 * @param accessToken - Access token for authentication
 * @returns Success status and message with assignment details
 */
const assignCourseToUsers = async (courseId, userIds, accessToken) => {
    try {
        // Validate course exists
        const courseExists = await db_1.db
            .selectFrom("Course")
            .where("id", "=", courseId)
            .executeTakeFirst();
        if (!courseExists) {
            throw new Error("Course not found");
        }
        // Validate users exist
        const areUserIdsValid = await validateUserIds(userIds);
        if (!areUserIdsValid) {
            throw new Error("One or more users do not exist");
        }
        // Get existing assignments to prevent duplicates
        const existingAssignments = await db_1.db
            .selectFrom("UserCourse")
            .where("courseId", "=", courseId)
            .where("userId", "in", userIds)
            .select("userId")
            .execute();
        const existingUserIds = existingAssignments.map(a => a.userId);
        const newUserIds = userIds.filter(id => !existingUserIds.includes(id));
        if (newUserIds.length === 0) {
            return {
                success: false,
                message: "All selected users are already assigned to this course",
                assignedCount: 0,
                duplicateCount: existingUserIds.length
            };
        }
        // Create new assignments only for users who don't have the course
        const userCourseEntries = newUserIds.map((userId) => ({
            id: (0, uuid_1.v4)(),
            userId,
            courseId,
            createdAt: new Date(),
        }));
        await db_1.db.insertInto("UserCourse").values(userCourseEntries).execute();
        // Send email notifications to newly assigned users
        if (newUserIds.length > 0) {
            // Don't await this to avoid blocking the response
            sendCourseAssignmentEmails(newUserIds, courseId).catch(error => {
                console.error("Failed to send assignment emails:", error);
            });
        }
        const message = existingUserIds.length > 0
            ? `Successfully assigned course to ${newUserIds.length} users. ${existingUserIds.length} users were already assigned.`
            : `Successfully assigned course to ${newUserIds.length} users.`;
        return {
            success: true,
            message,
            assignedCount: newUserIds.length,
            duplicateCount: existingUserIds.length
        };
    }
    catch (error) {
        console.error("Error assigning course to users:", error);
        throw new Error("Unable to assign course. Please try again later.");
    }
};
exports.assignCourseToUsers = assignCourseToUsers;
/**
 * Unassign a course from multiple users
 * @param courseId - The course ID to unassign
 * @param userIds - Array of user IDs to unassign the course from
 * @param accessToken - Access token for authentication
 * @returns Success status and message with unassignment details
 */
const unassignCourseFromUsers = async (courseId, userIds, _accessToken // Prefixed with underscore to indicate intentionally unused
) => {
    try {
        // Validate course exists
        const courseExists = await db_1.db
            .selectFrom("Course")
            .where("id", "=", courseId)
            .executeTakeFirst();
        if (!courseExists) {
            throw new Error("Course not found");
        }
        // Check which users are actually assigned to this course
        const existingAssignments = await db_1.db
            .selectFrom("UserCourse")
            .where("courseId", "=", courseId)
            .where("userId", "in", userIds)
            .select("userId")
            .execute();
        if (existingAssignments.length === 0) {
            return {
                success: false,
                message: "None of the selected users are assigned to this course",
                unassignedCount: 0
            };
        }
        // Remove assignments
        await db_1.db
            .deleteFrom("UserCourse")
            .where("courseId", "=", courseId)
            .where("userId", "in", userIds)
            .execute();
        return {
            success: true,
            message: `Successfully unassigned course from ${existingAssignments.length} users`,
            unassignedCount: existingAssignments.length
        };
    }
    catch (error) {
        console.error("Error unassigning course from users:", error);
        throw new Error("Unable to unassign course. Please try again later.");
    }
};
exports.unassignCourseFromUsers = unassignCourseFromUsers;
/**
 * Get all users assigned to a specific course
 * @param courseId - The course ID to get assignments for
 * @param accessToken - Access token for authentication
 * @returns Array of users assigned to the course
 */
const getCourseAssignments = async (courseId, _accessToken // Prefixed with underscore to indicate intentionally unused
) => {
    try {
        const assignments = await db_1.db
            .selectFrom("UserCourse")
            .innerJoin("User", "UserCourse.userId", "User.id")
            .where("UserCourse.courseId", "=", courseId)
            .select([
            "User.id",
            "User.firstName",
            "User.lastName",
            "User.email",
            "UserCourse.createdAt as assignedAt"
        ])
            .orderBy("UserCourse.createdAt", "desc")
            .execute();
        return assignments;
    }
    catch (error) {
        console.error("Error fetching course assignments:", error);
        throw new Error("Unable to fetch assignments. Please try again later.");
    }
};
exports.getCourseAssignments = getCourseAssignments;
/**
 * Get all courses assigned to a specific user
 * @param userId - The user ID to get assignments for
 * @param accessToken - Access token for authentication
 * @returns Array of courses assigned to the user
 */
const getUserAssignments = async (userId, _accessToken // Prefixed with underscore to indicate intentionally unused
) => {
    try {
        const assignments = await db_1.db
            .selectFrom("UserCourse")
            .innerJoin("Course", "UserCourse.courseId", "Course.id")
            .where("UserCourse.userId", "=", userId)
            .select([
            "Course.id",
            "Course.title",
            "Course.description",
            "Course.url",
            "UserCourse.createdAt as assignedAt"
        ])
            .orderBy("UserCourse.createdAt", "desc")
            .execute();
        return assignments;
    }
    catch (error) {
        console.error("Error fetching user assignments:", error);
        throw new Error("Unable to fetch assignments. Please try again later.");
    }
};
exports.getUserAssignments = getUserAssignments;
/**
 * Get comprehensive analytics data for admin dashboard
 */
const getAdminAnalytics = async (accessToken) => {
    try {
        // Get total counts
        const [userCount, courseCount, assignmentCount, reportCount] = await Promise.all([
            db_1.db.selectFrom("User").select([db_1.db.fn.count("id").as("total")]).executeTakeFirst(),
            db_1.db.selectFrom("Course").select([db_1.db.fn.count("id").as("total")]).executeTakeFirst(),
            db_1.db.selectFrom("UserCourse").select([db_1.db.fn.count("id").as("total")]).executeTakeFirst(),
            db_1.db.selectFrom("Report").select([db_1.db.fn.count("id").as("total")]).executeTakeFirst(),
        ]);
        // Get completion statistics
        const completionStats = await db_1.db
            .selectFrom("Report")
            .select([
            db_1.db.fn.count("id").as("totalReports"),
            db_1.db.fn.countAll().filterWhere("completed", "=", true).as("completedReports"),
            db_1.db.fn.avg("score").as("averageScore")
        ])
            .executeTakeFirst();
        // Get course performance data
        const coursePerformance = await db_1.db
            .selectFrom("Course")
            .leftJoin("UserCourse", "Course.id", "UserCourse.courseId")
            .leftJoin("Report", (join) => join
            .onRef("Report.courseId", "=", "Course.id")
            .onRef("Report.userId", "=", "UserCourse.userId"))
            .select([
            "Course.id",
            "Course.title",
            db_1.db.fn.count("UserCourse.id").as("assignedUsers"),
            db_1.db.fn.countAll().filterWhere("Report.completed", "=", true).as("completedUsers"),
            db_1.db.fn.avg("Report.score").as("averageScore")
        ])
            .groupBy(["Course.id", "Course.title"])
            .orderBy("assignedUsers", "desc")
            .limit(10)
            .execute();
        // Get user engagement data
        const userEngagement = await db_1.db
            .selectFrom("User")
            .leftJoin("UserCourse", "User.id", "UserCourse.userId")
            .leftJoin("Report", "User.id", "Report.userId")
            .select([
            "User.role",
            db_1.db.fn.count("User.id").as("userCount"),
            db_1.db.fn.count("UserCourse.id").as("totalAssignments"),
            db_1.db.fn.countAll().filterWhere("Report.completed", "=", true).as("completedCourses")
        ])
            .groupBy("User.role")
            .execute();
        // Get recent activity (last 20 reports)
        const recentActivity = await db_1.db
            .selectFrom("Report")
            .innerJoin("User", "Report.userId", "User.id")
            .innerJoin("Course", "Report.courseId", "Course.id")
            .select([
            "Report.id",
            "Report.completed",
            "Report.score",
            "Report.createdAt",
            "User.firstName",
            "User.lastName",
            "Course.title as courseTitle"
        ])
            .orderBy("Report.createdAt", "desc")
            .limit(20)
            .execute();
        return {
            success: true,
            data: {
                overview: {
                    totalUsers: Number(userCount?.total || 0),
                    totalCourses: Number(courseCount?.total || 0),
                    totalAssignments: Number(assignmentCount?.total || 0),
                    totalReports: Number(reportCount?.total || 0),
                    completedReports: Number(completionStats?.completedReports || 0),
                    averageScore: Number(completionStats?.averageScore || 0),
                    completionRate: Number(completionStats?.totalReports) > 0
                        ? (Number(completionStats?.completedReports || 0) / Number(completionStats?.totalReports)) * 100
                        : 0
                },
                coursePerformance: coursePerformance.map(course => ({
                    id: course.id,
                    title: course.title,
                    assignedUsers: Number(course.assignedUsers || 0),
                    completedUsers: Number(course.completedUsers || 0),
                    averageScore: Number(course.averageScore || 0),
                    completionRate: Number(course.assignedUsers) > 0
                        ? (Number(course.completedUsers || 0) / Number(course.assignedUsers)) * 100
                        : 0
                })),
                monthlyTrends: [], // Simplified for now - can be enhanced later
                userEngagement: userEngagement.map(engagement => ({
                    role: engagement.role,
                    userCount: Number(engagement.userCount || 0),
                    totalAssignments: Number(engagement.totalAssignments || 0),
                    completedCourses: Number(engagement.completedCourses || 0),
                    engagementRate: Number(engagement.totalAssignments) > 0
                        ? (Number(engagement.completedCourses || 0) / Number(engagement.totalAssignments)) * 100
                        : 0
                })),
                recentActivity: recentActivity.map(activity => ({
                    id: activity.id,
                    userName: `${activity.firstName} ${activity.lastName}`,
                    courseTitle: activity.courseTitle,
                    completed: activity.completed,
                    score: activity.score,
                    createdAt: activity.createdAt
                }))
            }
        };
    }
    catch (error) {
        console.error("Error fetching admin analytics:", error);
        throw new Error("Unable to fetch analytics data. Please try again later.");
    }
};
exports.getAdminAnalytics = getAdminAnalytics;

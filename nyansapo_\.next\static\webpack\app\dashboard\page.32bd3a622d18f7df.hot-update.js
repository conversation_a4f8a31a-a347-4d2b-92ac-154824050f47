"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/ClickOutside.tsx":
/*!*****************************************!*\
  !*** ./src/components/ClickOutside.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst ClickOutside = (param)=>{\n    let { children, exceptionRef, onClick, className } = param;\n    _s();\n    const wrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickListener = (event)=>{\n            let clickedInside = false;\n            if (exceptionRef) {\n                clickedInside = wrapperRef.current && wrapperRef.current.contains(event.target) || exceptionRef.current && exceptionRef.current === event.target || exceptionRef.current && exceptionRef.current.contains(event.target);\n            } else {\n                clickedInside = wrapperRef.current && wrapperRef.current.contains(event.target);\n            }\n            if (!clickedInside) onClick();\n        };\n        // Add a small delay to ensure DOM is ready\n        const timeoutId = setTimeout(()=>{\n            document.addEventListener(\"mousedown\", handleClickListener);\n        }, 0);\n        return ()=>{\n            clearTimeout(timeoutId);\n            document.removeEventListener(\"mousedown\", handleClickListener);\n        };\n    }, [\n        exceptionRef,\n        onClick\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: wrapperRef,\n        className: \"\".concat(className || \"\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\FRANCIS ASANTE\\\\nyansapo_\\\\src\\\\components\\\\ClickOutside.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClickOutside, \"S9hay92VXuy3YMd6nM+XEC/Dmxc=\");\n_c = ClickOutside;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClickOutside);\nvar _c;\n$RefreshReg$(_c, \"ClickOutside\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ClickOutside.tsx\n"));

/***/ })

});
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCourseSchema = exports.addCourseSchema = void 0;
const zod_1 = require("zod");
// Schema for adding a course, now including optional user IDs
exports.addCourseSchema = zod_1.z.object({
    title: zod_1.z.string().min(1, "Title is required"),
    description: zod_1.z.string().min(1, "Description is required"),
    url: zod_1.z.string().url("Invalid URL format"),
    userIds: zod_1.z
        .array(zod_1.z.string().uuid({ message: "Invalid user ID" }))
        .optional(), // Optional list of user IDs to associate with the course
});
// Schema for updating a course, now including optional user IDs
exports.updateCourseSchema = zod_1.z.object({
    id: zod_1.z.string().uuid("Invalid course ID"),
    title: zod_1.z.string().min(1, "Title is required").optional(),
    description: zod_1.z.string().min(1, "Description is required").optional(),
    url: zod_1.z.string().url("Invalid URL format").optional(),
    userIds: zod_1.z
        .array(zod_1.z.string().uuid({ message: "Invalid user ID" }))
        .optional(), // Optional list of user IDs to update course associations
});

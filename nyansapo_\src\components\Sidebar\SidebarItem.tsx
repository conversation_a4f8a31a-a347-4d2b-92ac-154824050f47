import React from "react";
import Link from "next/link";

interface SidebarItemProps {
  item: {
    icon: React.ReactNode;
    label: string;
    route: string;
    children?: Array<{
      label: string;
      route: string;
    }>;
  };
  pathname: string;
  expandedItems: Set<string>;
  toggleExpanded: (itemLabel: string) => void;
}

const SidebarItem: React.FC<SidebarItemProps> = ({ 
  item, 
  pathname, 
  expandedItems, 
  toggleExpanded 
}) => {
  const isActive = pathname === item.route;
  const isExpanded = expandedItems.has(item.label);
  const hasChildren = item.children && item.children.length > 0;

  const handleClick = (e: React.MouseEvent) => {
    if (hasChildren) {
      e.preventDefault();
      toggleExpanded(item.label);
    }
  };

  return (
    <li>
      <Link
        href={item.route}
        onClick={handleClick}
        className={`${
          isActive
            ? "bg-blue-600 text-white shadow-lg border-l-4 border-blue-300"
            : "text-blue-100 hover:bg-blue-800 hover:text-white"
        } group relative flex items-center gap-3 rounded-lg px-4 py-3 font-medium transition-all duration-300 ease-in-out`}
      >
        <span className={`${isActive ? "text-white" : "text-blue-200"} transition-colors duration-300`}>
          {item.icon}
        </span>
        <span className="font-medium">{item.label}</span>
        
        {hasChildren && (
          <svg
            className={`absolute right-4 top-1/2 -translate-y-1/2 fill-current transition-transform duration-300 ${
              isExpanded ? "rotate-180" : ""
            } ${isActive ? "text-white" : "text-blue-200"}`}
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M4.41107 6.9107C4.73651 6.58527 5.26414 6.58527 5.58958 6.9107L10.0003 11.3214L14.4111 6.91071C14.7365 6.58527 15.2641 6.58527 15.5896 6.91071C15.915 7.23614 15.915 7.76378 15.5896 8.08922L10.5896 13.0892C10.2641 13.4147 9.73651 13.4147 9.41107 13.0892L4.41107 8.08922C4.08563 7.76378 4.08563 7.23614 4.41107 6.9107Z"
              fill=""
            />
          </svg>
        )}
      </Link>

      {hasChildren && (
        <div
          className={`overflow-hidden transition-all duration-300 ease-in-out ${
            isExpanded ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
          }`}
        >
          <ul className="mb-5.5 mt-2 flex flex-col gap-1 pl-6">
            {item.children!.map((child, index) => (
              <li key={`${child.label}-${index}`}>
                <Link
                  href={child.route}
                  className={`group relative flex items-center gap-2.5 rounded-lg px-4 py-2 font-medium transition-all duration-300 ease-in-out ${
                    pathname === child.route
                      ? "text-white bg-blue-500 shadow-md border-l-2 border-blue-300"
                      : "text-blue-200 hover:text-white hover:bg-blue-700"
                  }`}
                >
                  <span className="w-2 h-2 rounded-full bg-current opacity-60"></span>
                  {child.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      )}
    </li>
  );
};

export default SidebarItem; 
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteQuiz = exports.updateQuiz = exports.getQuizzes = exports.addQuiz = void 0;
const db_1 = require("../db/db");
const uuid_1 = require("uuid");
const schema_1 = require("../schema");
const addQuiz = async (input, accessToken // Access token is included but not validated
) => {
    // Validate input
    const parsedInput = schema_1.addQuizSchema.safeParse(input);
    if (!parsedInput.success) {
        throw new Error("Invalid input: " +
            parsedInput.error.errors.map((err) => err.message).join(", "));
    }
    const quizId = (0, uuid_1.v4)();
    try {
        const [newQuiz] = await db_1.db
            .insertInto("Quiz")
            .values({
            id: quizId,
            courseId: parsedInput.data.courseId,
            question: parsedInput.data.question,
            options: parsedInput.data.options,
            answer: parsedInput.data.answer,
            createdAt: new Date(),
            updatedAt: new Date(),
        })
            .returning([
            "id",
            "courseId",
            "question",
            "options",
            "answer",
            "createdAt",
            "updatedAt",
        ])
            .execute();
        console.log("Request Body:", {
            accessToken,
            quiz: {
                id: quizId,
                courseId: parsedInput.data.courseId,
                question: parsedInput.data.question,
                options: parsedInput.data.options,
                answer: parsedInput.data.answer,
            },
        }); // Debug log for the request body
        return newQuiz;
    }
    catch (error) {
        console.error("Error adding quiz:", error);
        throw new Error("Unable to add quiz. Please try again later.");
    }
};
exports.addQuiz = addQuiz;
const getQuizzes = async (accessToken, courseId, // Make courseId optional
page = 1, limit = 10) => {
    try {
        // Validate page and limit values
        if (page < 1 || limit < 1) {
            throw new Error("Page and limit must be greater than 0.");
        }
        // Base query to retrieve quizzes
        let query = db_1.db
            .selectFrom("Quiz")
            .select([
            "id",
            "courseId",
            "question",
            "options",
            "answer",
            "createdAt",
            "updatedAt",
        ])
            .orderBy("createdAt", "desc")
            .offset((page - 1) * limit)
            .limit(limit);
        // Apply courseId filter only if it's provided
        if (courseId) {
            query = query.where("courseId", "=", courseId);
        }
        // Execute the query to fetch quizzes
        const quizzes = await query.execute();
        // Get total count of quizzes (with or without filtering)
        let totalQuery = db_1.db
            .selectFrom("Quiz")
            .select([db_1.db.fn.count("id").as("total")]);
        if (courseId) {
            totalQuery = totalQuery.where("courseId", "=", courseId);
        }
        const totalResult = await totalQuery.execute();
        const total = Number(totalResult[0].total); // Convert total count to number
        console.log("Request Body:", {
            courseId,
            page,
            limit,
        }); // Debug log for the request body
        return {
            quizzes: quizzes, // Return the quizzes
            total, // Total count for pagination
        };
    }
    catch (error) {
        console.error("Error fetching quizzes:", error);
        throw new Error("Unable to fetch quizzes. Please try again later.");
    }
};
exports.getQuizzes = getQuizzes;
const updateQuiz = async (input, accessToken // Access token is included but not validated
) => {
    // Validate input
    const parsedInput = schema_1.updateQuizSchema.safeParse(input);
    if (!parsedInput.success) {
        throw new Error("Invalid input: " +
            parsedInput.error.errors.map((err) => err.message).join(", "));
    }
    const { id, courseId, question, options, answer } = parsedInput.data;
    try {
        // Check if the quiz exists
        const existingQuiz = await db_1.db
            .selectFrom("Quiz")
            .selectAll()
            .where("id", "=", id)
            .executeTakeFirst();
        if (!existingQuiz) {
            throw new Error(`Quiz with ID ${id} does not exist.`);
        }
        // Update the quiz
        const [updatedQuiz] = await db_1.db
            .updateTable("Quiz")
            .set({
            courseId: courseId || existingQuiz.courseId,
            question: question || existingQuiz.question,
            options: options || existingQuiz.options,
            answer: answer || existingQuiz.answer,
            updatedAt: new Date(), // Update timestamp
        })
            .where("id", "=", id)
            .returning([
            "id",
            "courseId",
            "question",
            "options",
            "answer",
            "createdAt",
            "updatedAt",
        ])
            .execute();
        console.log("Request Body:", {
            accessToken,
            id,
            courseId,
            question,
            options,
            answer,
        }); // Debug log for the request body
        return updatedQuiz;
    }
    catch (error) {
        console.error("Error updating quiz:", error);
        throw new Error("Unable to update quiz. Please try again later.");
    }
};
exports.updateQuiz = updateQuiz;
/**
 * Service to delete a quiz by its ID
 * @param {string} quizId - The ID of the quiz to be deleted.
 * @returns {Promise<boolean>} - Returns a promise that resolves to a boolean indicating success or failure.
 */
const deleteQuiz = async (quizId, accessToken // Access token is included but not validated
) => {
    try {
        const result = await db_1.db
            .deleteFrom("Quiz")
            .where("id", "=", quizId) // Find the quiz by ID
            .execute();
        if (result.length === 0) {
            throw new Error("Quiz not found.");
        }
        console.log("Request Body:", {
            accessToken,
            quizId,
        }); // Debug log for the request body
        return true; // Return true if deletion was successful
    }
    catch (error) {
        console.error("Error deleting quiz:", error);
        throw new Error("Unable to delete quiz. Please try again later.");
    }
};
exports.deleteQuiz = deleteQuiz;

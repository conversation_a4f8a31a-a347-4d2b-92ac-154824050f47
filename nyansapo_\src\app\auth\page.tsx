"use client";
import React, { useState } from "react";
import { Button, Input, Divider, Form, Spin, message } from "antd";
import { useRouter } from "next/navigation";
import { useLoginMutation } from "../../reduxRTK/services/authApi";
import Image from "next/image";


export default function SignIn() {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [login, { isLoading: apiLoading, error }] = useLoginMutation();
  const router = useRouter();

  const showMessage = (type: "success" | "error", content: string) => {
    if (type === "success") {
      message.success(content);
    } else {
      message.error(content);
    }
  };

  const onFinish = async (values: { email: string; password: string }) => {
    setIsLoading(true);

    try {
      const response = await login({
        email: values.email,
        password: values.password,
      }).unwrap();

      if (response.success) {
        setIsLoading(false);
        showMessage("success", response.message || "Login successful.");

        const { user } = response.data || {};
        if (user?.role === "admin") {
          router.replace("/dashboard");
        } else if (user?.role === "user") {
          router.replace("/user/dashboard");
        } else {
          showMessage("error", "Role not recognized.");
          router.replace("/");
        }
      } else {
        setIsLoading(false);
        showMessage("error", response.message || "An error occurred.");
      }
    } catch (err: any) {
      setIsLoading(false);
      if (err?.data) {
        showMessage("error", err.data.message || "An unknown error occurred.");
      } else if (err?.message) {
        showMessage("error", err.message || "An error occurred. Please try again.");
      } else {
        showMessage("error", "An unknown error occurred.");
      }
    }
  };

  return (
    <div className="relative min-h-screen flex items-center justify-center py-6 sm:py-12">
      {/* Background Image */}
      <Image
        src="/images/background.jpg"
        alt="Background"
        fill={true}
        style={{ objectFit: 'cover' }}
        className="absolute inset-0 z-0"
      />
  
      {/* Content Wrapper */}
      <div className="relative z-10 flex flex-col-reverse lg:flex-row bg-white bg-opacity-90 shadow-lg rounded-3xl w-full max-w-4xl">
        {/* Left Side: Login Form */}
        <div className="w-full lg:w-1/2 p-8 lg:p-12">
          <h2 className="text-2xl font-semibold text-center lg:text-left mb-4">Login</h2>
          <Divider />
          <Form form={form} onFinish={onFinish} layout="vertical">
            <Form.Item
              name="email"
              rules={[
                { required: true, message: "Please enter your email address" },
                { type: "email", message: "Please enter a valid email address" },
              ]}
            >
              <Input placeholder="Email Address" className="h-10 w-full" />
            </Form.Item>
            <Form.Item
              name="password"
              rules={[{ required: true, message: "Please enter your password" }]}
            >
              <Input.Password placeholder="Password" className="h-10 w-full" />
            </Form.Item>
            <div>
              <Button
                type="primary"
                htmlType="submit"
                className="w-full bg-cyan-500"
                disabled={isLoading || apiLoading}
              >
                {isLoading || apiLoading ? <Spin /> : "Login"}
              </Button>
            </div>
          </Form>
        </div>
  
        {/* Right Side: Welcome Message */}
        <div className="w-full lg:w-1/2 bg-gradient-to-r from-cyan-400 to-sky-500 text-white p-8 lg:p-12 flex flex-col justify-center">
          <h1 className="text-3xl font-bold text-center">
            Welcome to NyansaPo Cybersecurity Nano Learning Portal
          </h1>
          <p className="mt-4 text-center text-lg">
            Empower Your Cybersecurity Skills, One Byte at a Time
          </p>
        </div>
      </div>
    </div>
  );
  
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUserController = void 0;
const services_1 = require("../services");
const updateUserController = async (req, res) => {
    const { userId, mode, accessToken, ...updateData } = req.body; // Extract userId, mode, accessToken, and other update data
    // Check if mode is "update"
    if (mode !== "update") {
        return res.status(400).json({
            success: false,
            message: "Invalid mode for this operation.",
        });
    }
    try {
        // Validate userId
        if (!userId) {
            return res.status(400).json({
                success: false,
                message: "User ID is required for updating.",
            });
        }
        // Call the updateUser service and pass the accessToken first, followed by res
        const updatedUser = await (0, services_1.updateUser)(userId, updateData, accessToken, res);
        // Respond with success and updated user data (excluding password)
        return res.status(200).json({
            success: true,
            message: "User updated successfully.",
            data: updatedUser,
        });
    }
    catch (error) {
        console.error("Error updating user:", error);
        return res.status(500).json({
            success: false,
            message: error.message || "Internal server error.",
        });
    }
};
exports.updateUserController = updateUserController;

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-motion";
exports.ids = ["vendor-chunks/rc-motion"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-motion/es/CSSMotion.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/CSSMotion.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   genCSSMotion: () => (/* binding */ genCSSMotion)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/Dom/findDOMNode */ \"(ssr)/./node_modules/rc-util/es/Dom/findDOMNode.js\");\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-motion/es/context.js\");\n/* harmony import */ var _DomWrapper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DomWrapper */ \"(ssr)/./node_modules/rc-motion/es/DomWrapper.js\");\n/* harmony import */ var _hooks_useStatus__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./hooks/useStatus */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js\");\n/* harmony import */ var _hooks_useStepQueue__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useStepQueue */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\");\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n\n/* eslint-disable react/default-props-match-prop-types, react/no-multi-comp, react/prop-types */\n\n\n\n\n\n\n\n\n\n\n\n/**\n * `transitionSupport` is used for none transition test case.\n * Default we use browser transition event support check.\n */\nfunction genCSSMotion(config) {\n  var transitionSupport = config;\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(config) === 'object') {\n    transitionSupport = config.transitionSupport;\n  }\n  function isSupportTransition(props, contextMotion) {\n    return !!(props.motionName && transitionSupport && contextMotion !== false);\n  }\n  var CSSMotion = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.forwardRef(function (props, ref) {\n    var _props$visible = props.visible,\n      visible = _props$visible === void 0 ? true : _props$visible,\n      _props$removeOnLeave = props.removeOnLeave,\n      removeOnLeave = _props$removeOnLeave === void 0 ? true : _props$removeOnLeave,\n      forceRender = props.forceRender,\n      children = props.children,\n      motionName = props.motionName,\n      leavedClassName = props.leavedClassName,\n      eventProps = props.eventProps;\n    var _React$useContext = react__WEBPACK_IMPORTED_MODULE_7__.useContext(_context__WEBPACK_IMPORTED_MODULE_8__.Context),\n      contextMotion = _React$useContext.motion;\n    var supportMotion = isSupportTransition(props, contextMotion);\n\n    // Ref to the react node, it may be a HTMLElement\n    var nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    // Ref to the dom wrapper in case ref can not pass to HTMLElement\n    var wrapperNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    function getDomElement() {\n      try {\n        // Here we're avoiding call for findDOMNode since it's deprecated\n        // in strict mode. We're calling it only when node ref is not\n        // an instance of DOM HTMLElement. Otherwise use\n        // findDOMNode as a final resort\n        return nodeRef.current instanceof HTMLElement ? nodeRef.current : (0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(wrapperNodeRef.current);\n      } catch (e) {\n        // Only happen when `motionDeadline` trigger but element removed.\n        return null;\n      }\n    }\n    var _useStatus = (0,_hooks_useStatus__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(supportMotion, visible, getDomElement, props),\n      _useStatus2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useStatus, 4),\n      status = _useStatus2[0],\n      statusStep = _useStatus2[1],\n      statusStyle = _useStatus2[2],\n      mergedVisible = _useStatus2[3];\n\n    // Record whether content has rendered\n    // Will return null for un-rendered even when `removeOnLeave={false}`\n    var renderedRef = react__WEBPACK_IMPORTED_MODULE_7__.useRef(mergedVisible);\n    if (mergedVisible) {\n      renderedRef.current = true;\n    }\n\n    // ====================== Refs ======================\n    var setNodeRef = react__WEBPACK_IMPORTED_MODULE_7__.useCallback(function (node) {\n      nodeRef.current = node;\n      (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.fillRef)(ref, node);\n    }, [ref]);\n\n    // ===================== Render =====================\n    var motionChildren;\n    var mergedProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, eventProps), {}, {\n      visible: visible\n    });\n    if (!children) {\n      // No children\n      motionChildren = null;\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_12__.STATUS_NONE) {\n      // Stable children\n      if (mergedVisible) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), setNodeRef);\n      } else if (!removeOnLeave && renderedRef.current && leavedClassName) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n          className: leavedClassName\n        }), setNodeRef);\n      } else if (forceRender || !removeOnLeave && !leavedClassName) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n          style: {\n            display: 'none'\n          }\n        }), setNodeRef);\n      } else {\n        motionChildren = null;\n      }\n    } else {\n      // In motion\n      var statusSuffix;\n      if (statusStep === _interface__WEBPACK_IMPORTED_MODULE_12__.STEP_PREPARE) {\n        statusSuffix = 'prepare';\n      } else if ((0,_hooks_useStepQueue__WEBPACK_IMPORTED_MODULE_11__.isActive)(statusStep)) {\n        statusSuffix = 'active';\n      } else if (statusStep === _interface__WEBPACK_IMPORTED_MODULE_12__.STEP_START) {\n        statusSuffix = 'start';\n      }\n      var motionCls = (0,_util_motion__WEBPACK_IMPORTED_MODULE_13__.getTransitionName)(motionName, \"\".concat(status, \"-\").concat(statusSuffix));\n      motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()((0,_util_motion__WEBPACK_IMPORTED_MODULE_13__.getTransitionName)(motionName, status), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, motionCls, motionCls && statusSuffix), motionName, typeof motionName === 'string')),\n        style: statusStyle\n      }), setNodeRef);\n    }\n\n    // Auto inject ref if child node not have `ref` props\n    if ( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.isValidElement(motionChildren) && (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.supportRef)(motionChildren)) {\n      var originNodeRef = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.getNodeRef)(motionChildren);\n      if (!originNodeRef) {\n        motionChildren = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.cloneElement(motionChildren, {\n          ref: setNodeRef\n        });\n      }\n    }\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_DomWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n      ref: wrapperNodeRef\n    }, motionChildren);\n  });\n  CSSMotion.displayName = 'CSSMotion';\n  return CSSMotion;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (genCSSMotion(_util_motion__WEBPACK_IMPORTED_MODULE_13__.supportTransition));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/CSSMotion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/CSSMotionList.js":
/*!****************************************************!*\
  !*** ./node_modules/rc-motion/es/CSSMotionList.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   genCSSMotionList: () => (/* binding */ genCSSMotionList)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _CSSMotion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CSSMotion */ \"(ssr)/./node_modules/rc-motion/es/CSSMotion.js\");\n/* harmony import */ var _util_diff__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./util/diff */ \"(ssr)/./node_modules/rc-motion/es/util/diff.js\");\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n\n\n\n\n\n\nvar _excluded = [\"component\", \"children\", \"onVisibleChanged\", \"onAllRemoved\"],\n  _excluded2 = [\"status\"];\n/* eslint react/prop-types: 0 */\n\n\n\n\nvar MOTION_PROP_NAMES = ['eventProps', 'visible', 'children', 'motionName', 'motionAppear', 'motionEnter', 'motionLeave', 'motionLeaveImmediately', 'motionDeadline', 'removeOnLeave', 'leavedClassName', 'onAppearPrepare', 'onAppearStart', 'onAppearActive', 'onAppearEnd', 'onEnterStart', 'onEnterActive', 'onEnterEnd', 'onLeaveStart', 'onLeaveActive', 'onLeaveEnd'];\n/**\n * Generate a CSSMotionList component with config\n * @param transitionSupport No need since CSSMotionList no longer depends on transition support\n * @param CSSMotion CSSMotion component\n */\nfunction genCSSMotionList(transitionSupport) {\n  var CSSMotion = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _CSSMotion__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n  var CSSMotionList = /*#__PURE__*/function (_React$Component) {\n    (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(CSSMotionList, _React$Component);\n    var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(CSSMotionList);\n    function CSSMotionList() {\n      var _this;\n      (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, CSSMotionList);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _super.call.apply(_super, [this].concat(args));\n      (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"state\", {\n        keyEntities: []\n      });\n      // ZombieJ: Return the count of rest keys. It's safe to refactor if need more info.\n      (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"removeKey\", function (removeKey) {\n        _this.setState(function (prevState) {\n          var nextKeyEntities = prevState.keyEntities.map(function (entity) {\n            if (entity.key !== removeKey) return entity;\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, entity), {}, {\n              status: _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED\n            });\n          });\n          return {\n            keyEntities: nextKeyEntities\n          };\n        }, function () {\n          var keyEntities = _this.state.keyEntities;\n          var restKeysCount = keyEntities.filter(function (_ref) {\n            var status = _ref.status;\n            return status !== _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED;\n          }).length;\n          if (restKeysCount === 0 && _this.props.onAllRemoved) {\n            _this.props.onAllRemoved();\n          }\n        });\n      });\n      return _this;\n    }\n    (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(CSSMotionList, [{\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        var keyEntities = this.state.keyEntities;\n        var _this$props = this.props,\n          component = _this$props.component,\n          children = _this$props.children,\n          _onVisibleChanged = _this$props.onVisibleChanged,\n          onAllRemoved = _this$props.onAllRemoved,\n          restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this$props, _excluded);\n        var Component = component || react__WEBPACK_IMPORTED_MODULE_9__.Fragment;\n        var motionProps = {};\n        MOTION_PROP_NAMES.forEach(function (prop) {\n          motionProps[prop] = restProps[prop];\n          delete restProps[prop];\n        });\n        delete restProps.keys;\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(Component, restProps, keyEntities.map(function (_ref2, index) {\n          var status = _ref2.status,\n            eventProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref2, _excluded2);\n          var visible = status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_ADD || status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_KEEP;\n          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(CSSMotion, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, motionProps, {\n            key: eventProps.key,\n            visible: visible,\n            eventProps: eventProps,\n            onVisibleChanged: function onVisibleChanged(changedVisible) {\n              _onVisibleChanged === null || _onVisibleChanged === void 0 || _onVisibleChanged(changedVisible, {\n                key: eventProps.key\n              });\n              if (!changedVisible) {\n                _this2.removeKey(eventProps.key);\n              }\n            }\n          }), function (props, ref) {\n            return children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, props), {}, {\n              index: index\n            }), ref);\n          });\n        }));\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref3, _ref4) {\n        var keys = _ref3.keys;\n        var keyEntities = _ref4.keyEntities;\n        var parsedKeyObjects = (0,_util_diff__WEBPACK_IMPORTED_MODULE_11__.parseKeys)(keys);\n        var mixedKeyEntities = (0,_util_diff__WEBPACK_IMPORTED_MODULE_11__.diffKeys)(keyEntities, parsedKeyObjects);\n        return {\n          keyEntities: mixedKeyEntities.filter(function (entity) {\n            var prevEntity = keyEntities.find(function (_ref5) {\n              var key = _ref5.key;\n              return entity.key === key;\n            });\n\n            // Remove if already mark as removed\n            if (prevEntity && prevEntity.status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED && entity.status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVE) {\n              return false;\n            }\n            return true;\n          })\n        };\n      }\n    }]);\n    return CSSMotionList;\n  }(react__WEBPACK_IMPORTED_MODULE_9__.Component);\n  (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(CSSMotionList, \"defaultProps\", {\n    component: 'div'\n  });\n  return CSSMotionList;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (genCSSMotionList(_util_motion__WEBPACK_IMPORTED_MODULE_12__.supportTransition));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL0NTU01vdGlvbkxpc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwRDtBQUNnQztBQUNyQjtBQUNHO0FBQ047QUFDb0I7QUFDMUI7QUFDTTtBQUNNO0FBQ3hFO0FBQ0E7QUFDQTtBQUMrQjtBQUNXO0FBQ2dFO0FBQ3hEO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1Asc0ZBQXNGLG1EQUFlO0FBQ3JHO0FBQ0EsSUFBSSwrRUFBUztBQUNiLGlCQUFpQixrRkFBWTtBQUM3QjtBQUNBO0FBQ0EsTUFBTSxxRkFBZTtBQUNyQiwwRUFBMEUsYUFBYTtBQUN2RjtBQUNBO0FBQ0E7QUFDQSxNQUFNLHFGQUFlLENBQUMsNEZBQXNCO0FBQzVDO0FBQ0EsT0FBTztBQUNQO0FBQ0EsTUFBTSxxRkFBZSxDQUFDLDRGQUFzQjtBQUM1QztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsb0ZBQWEsQ0FBQyxvRkFBYSxHQUFHLGFBQWE7QUFDOUQsc0JBQXNCLHVEQUFjO0FBQ3BDLGFBQWE7QUFDYixXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLDhCQUE4Qix1REFBYztBQUM1QyxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0EsSUFBSSxrRkFBWTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsOEZBQXdCO0FBQzlDLHFDQUFxQywyQ0FBYztBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLDRCQUE0QixnREFBbUI7QUFDL0M7QUFDQSx5QkFBeUIsOEZBQXdCO0FBQ2pELG1DQUFtQyxtREFBVSxlQUFlLG9EQUFXO0FBQ3ZFLDhCQUE4QixnREFBbUIsWUFBWSw4RUFBUSxHQUFHO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCw0QkFBNEIsb0ZBQWEsQ0FBQyxvRkFBYSxHQUFHLFlBQVk7QUFDdEU7QUFDQSxhQUFhO0FBQ2IsV0FBVztBQUNYLFNBQVM7QUFDVDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixzREFBUztBQUN4QywrQkFBK0IscURBQVE7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7O0FBRWI7QUFDQSxvREFBb0QsdURBQWMsc0JBQXNCLHNEQUFhO0FBQ3JHO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRyxDQUFDLDRDQUFlO0FBQ25CLEVBQUUscUZBQWU7QUFDakI7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLGlFQUFlLGlCQUFpQiw0REFBaUIsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL0NTU01vdGlvbkxpc3QuanM/MTFlNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzXCI7XG5pbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuaW1wb3J0IF9jbGFzc0NhbGxDaGVjayBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY2xhc3NDYWxsQ2hlY2tcIjtcbmltcG9ydCBfY3JlYXRlQ2xhc3MgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2NyZWF0ZUNsYXNzXCI7XG5pbXBvcnQgX2Fzc2VydFRoaXNJbml0aWFsaXplZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vYXNzZXJ0VGhpc0luaXRpYWxpemVkXCI7XG5pbXBvcnQgX2luaGVyaXRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9pbmhlcml0c1wiO1xuaW1wb3J0IF9jcmVhdGVTdXBlciBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlU3VwZXJcIjtcbmltcG9ydCBfZGVmaW5lUHJvcGVydHkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5XCI7XG52YXIgX2V4Y2x1ZGVkID0gW1wiY29tcG9uZW50XCIsIFwiY2hpbGRyZW5cIiwgXCJvblZpc2libGVDaGFuZ2VkXCIsIFwib25BbGxSZW1vdmVkXCJdLFxuICBfZXhjbHVkZWQyID0gW1wic3RhdHVzXCJdO1xuLyogZXNsaW50IHJlYWN0L3Byb3AtdHlwZXM6IDAgKi9cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBPcmlnaW5DU1NNb3Rpb24gZnJvbSBcIi4vQ1NTTW90aW9uXCI7XG5pbXBvcnQgeyBkaWZmS2V5cywgcGFyc2VLZXlzLCBTVEFUVVNfQURELCBTVEFUVVNfS0VFUCwgU1RBVFVTX1JFTU9WRSwgU1RBVFVTX1JFTU9WRUQgfSBmcm9tIFwiLi91dGlsL2RpZmZcIjtcbmltcG9ydCB7IHN1cHBvcnRUcmFuc2l0aW9uIH0gZnJvbSBcIi4vdXRpbC9tb3Rpb25cIjtcbnZhciBNT1RJT05fUFJPUF9OQU1FUyA9IFsnZXZlbnRQcm9wcycsICd2aXNpYmxlJywgJ2NoaWxkcmVuJywgJ21vdGlvbk5hbWUnLCAnbW90aW9uQXBwZWFyJywgJ21vdGlvbkVudGVyJywgJ21vdGlvbkxlYXZlJywgJ21vdGlvbkxlYXZlSW1tZWRpYXRlbHknLCAnbW90aW9uRGVhZGxpbmUnLCAncmVtb3ZlT25MZWF2ZScsICdsZWF2ZWRDbGFzc05hbWUnLCAnb25BcHBlYXJQcmVwYXJlJywgJ29uQXBwZWFyU3RhcnQnLCAnb25BcHBlYXJBY3RpdmUnLCAnb25BcHBlYXJFbmQnLCAnb25FbnRlclN0YXJ0JywgJ29uRW50ZXJBY3RpdmUnLCAnb25FbnRlckVuZCcsICdvbkxlYXZlU3RhcnQnLCAnb25MZWF2ZUFjdGl2ZScsICdvbkxlYXZlRW5kJ107XG4vKipcbiAqIEdlbmVyYXRlIGEgQ1NTTW90aW9uTGlzdCBjb21wb25lbnQgd2l0aCBjb25maWdcbiAqIEBwYXJhbSB0cmFuc2l0aW9uU3VwcG9ydCBObyBuZWVkIHNpbmNlIENTU01vdGlvbkxpc3Qgbm8gbG9uZ2VyIGRlcGVuZHMgb24gdHJhbnNpdGlvbiBzdXBwb3J0XG4gKiBAcGFyYW0gQ1NTTW90aW9uIENTU01vdGlvbiBjb21wb25lbnRcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbkNTU01vdGlvbkxpc3QodHJhbnNpdGlvblN1cHBvcnQpIHtcbiAgdmFyIENTU01vdGlvbiA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDogT3JpZ2luQ1NTTW90aW9uO1xuICB2YXIgQ1NTTW90aW9uTGlzdCA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoX1JlYWN0JENvbXBvbmVudCkge1xuICAgIF9pbmhlcml0cyhDU1NNb3Rpb25MaXN0LCBfUmVhY3QkQ29tcG9uZW50KTtcbiAgICB2YXIgX3N1cGVyID0gX2NyZWF0ZVN1cGVyKENTU01vdGlvbkxpc3QpO1xuICAgIGZ1bmN0aW9uIENTU01vdGlvbkxpc3QoKSB7XG4gICAgICB2YXIgX3RoaXM7XG4gICAgICBfY2xhc3NDYWxsQ2hlY2sodGhpcywgQ1NTTW90aW9uTGlzdCk7XG4gICAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShfbGVuKSwgX2tleSA9IDA7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgICAgYXJnc1tfa2V5XSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgICAgIH1cbiAgICAgIF90aGlzID0gX3N1cGVyLmNhbGwuYXBwbHkoX3N1cGVyLCBbdGhpc10uY29uY2F0KGFyZ3MpKTtcbiAgICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJzdGF0ZVwiLCB7XG4gICAgICAgIGtleUVudGl0aWVzOiBbXVxuICAgICAgfSk7XG4gICAgICAvLyBab21iaWVKOiBSZXR1cm4gdGhlIGNvdW50IG9mIHJlc3Qga2V5cy4gSXQncyBzYWZlIHRvIHJlZmFjdG9yIGlmIG5lZWQgbW9yZSBpbmZvLlxuICAgICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcInJlbW92ZUtleVwiLCBmdW5jdGlvbiAocmVtb3ZlS2V5KSB7XG4gICAgICAgIF90aGlzLnNldFN0YXRlKGZ1bmN0aW9uIChwcmV2U3RhdGUpIHtcbiAgICAgICAgICB2YXIgbmV4dEtleUVudGl0aWVzID0gcHJldlN0YXRlLmtleUVudGl0aWVzLm1hcChmdW5jdGlvbiAoZW50aXR5KSB7XG4gICAgICAgICAgICBpZiAoZW50aXR5LmtleSAhPT0gcmVtb3ZlS2V5KSByZXR1cm4gZW50aXR5O1xuICAgICAgICAgICAgcmV0dXJuIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgZW50aXR5KSwge30sIHtcbiAgICAgICAgICAgICAgc3RhdHVzOiBTVEFUVVNfUkVNT1ZFRFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGtleUVudGl0aWVzOiBuZXh0S2V5RW50aXRpZXNcbiAgICAgICAgICB9O1xuICAgICAgICB9LCBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgdmFyIGtleUVudGl0aWVzID0gX3RoaXMuc3RhdGUua2V5RW50aXRpZXM7XG4gICAgICAgICAgdmFyIHJlc3RLZXlzQ291bnQgPSBrZXlFbnRpdGllcy5maWx0ZXIoZnVuY3Rpb24gKF9yZWYpIHtcbiAgICAgICAgICAgIHZhciBzdGF0dXMgPSBfcmVmLnN0YXR1cztcbiAgICAgICAgICAgIHJldHVybiBzdGF0dXMgIT09IFNUQVRVU19SRU1PVkVEO1xuICAgICAgICAgIH0pLmxlbmd0aDtcbiAgICAgICAgICBpZiAocmVzdEtleXNDb3VudCA9PT0gMCAmJiBfdGhpcy5wcm9wcy5vbkFsbFJlbW92ZWQpIHtcbiAgICAgICAgICAgIF90aGlzLnByb3BzLm9uQWxsUmVtb3ZlZCgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICAgIHJldHVybiBfdGhpcztcbiAgICB9XG4gICAgX2NyZWF0ZUNsYXNzKENTU01vdGlvbkxpc3QsIFt7XG4gICAgICBrZXk6IFwicmVuZGVyXCIsXG4gICAgICB2YWx1ZTogZnVuY3Rpb24gcmVuZGVyKCkge1xuICAgICAgICB2YXIgX3RoaXMyID0gdGhpcztcbiAgICAgICAgdmFyIGtleUVudGl0aWVzID0gdGhpcy5zdGF0ZS5rZXlFbnRpdGllcztcbiAgICAgICAgdmFyIF90aGlzJHByb3BzID0gdGhpcy5wcm9wcyxcbiAgICAgICAgICBjb21wb25lbnQgPSBfdGhpcyRwcm9wcy5jb21wb25lbnQsXG4gICAgICAgICAgY2hpbGRyZW4gPSBfdGhpcyRwcm9wcy5jaGlsZHJlbixcbiAgICAgICAgICBfb25WaXNpYmxlQ2hhbmdlZCA9IF90aGlzJHByb3BzLm9uVmlzaWJsZUNoYW5nZWQsXG4gICAgICAgICAgb25BbGxSZW1vdmVkID0gX3RoaXMkcHJvcHMub25BbGxSZW1vdmVkLFxuICAgICAgICAgIHJlc3RQcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhfdGhpcyRwcm9wcywgX2V4Y2x1ZGVkKTtcbiAgICAgICAgdmFyIENvbXBvbmVudCA9IGNvbXBvbmVudCB8fCBSZWFjdC5GcmFnbWVudDtcbiAgICAgICAgdmFyIG1vdGlvblByb3BzID0ge307XG4gICAgICAgIE1PVElPTl9QUk9QX05BTUVTLmZvckVhY2goZnVuY3Rpb24gKHByb3ApIHtcbiAgICAgICAgICBtb3Rpb25Qcm9wc1twcm9wXSA9IHJlc3RQcm9wc1twcm9wXTtcbiAgICAgICAgICBkZWxldGUgcmVzdFByb3BzW3Byb3BdO1xuICAgICAgICB9KTtcbiAgICAgICAgZGVsZXRlIHJlc3RQcm9wcy5rZXlzO1xuICAgICAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQ29tcG9uZW50LCByZXN0UHJvcHMsIGtleUVudGl0aWVzLm1hcChmdW5jdGlvbiAoX3JlZjIsIGluZGV4KSB7XG4gICAgICAgICAgdmFyIHN0YXR1cyA9IF9yZWYyLnN0YXR1cyxcbiAgICAgICAgICAgIGV2ZW50UHJvcHMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMoX3JlZjIsIF9leGNsdWRlZDIpO1xuICAgICAgICAgIHZhciB2aXNpYmxlID0gc3RhdHVzID09PSBTVEFUVVNfQUREIHx8IHN0YXR1cyA9PT0gU1RBVFVTX0tFRVA7XG4gICAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KENTU01vdGlvbiwgX2V4dGVuZHMoe30sIG1vdGlvblByb3BzLCB7XG4gICAgICAgICAgICBrZXk6IGV2ZW50UHJvcHMua2V5LFxuICAgICAgICAgICAgdmlzaWJsZTogdmlzaWJsZSxcbiAgICAgICAgICAgIGV2ZW50UHJvcHM6IGV2ZW50UHJvcHMsXG4gICAgICAgICAgICBvblZpc2libGVDaGFuZ2VkOiBmdW5jdGlvbiBvblZpc2libGVDaGFuZ2VkKGNoYW5nZWRWaXNpYmxlKSB7XG4gICAgICAgICAgICAgIF9vblZpc2libGVDaGFuZ2VkID09PSBudWxsIHx8IF9vblZpc2libGVDaGFuZ2VkID09PSB2b2lkIDAgfHwgX29uVmlzaWJsZUNoYW5nZWQoY2hhbmdlZFZpc2libGUsIHtcbiAgICAgICAgICAgICAgICBrZXk6IGV2ZW50UHJvcHMua2V5XG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICBpZiAoIWNoYW5nZWRWaXNpYmxlKSB7XG4gICAgICAgICAgICAgICAgX3RoaXMyLnJlbW92ZUtleShldmVudFByb3BzLmtleSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KSwgZnVuY3Rpb24gKHByb3BzLCByZWYpIHtcbiAgICAgICAgICAgIHJldHVybiBjaGlsZHJlbihfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIHByb3BzKSwge30sIHtcbiAgICAgICAgICAgICAgaW5kZXg6IGluZGV4XG4gICAgICAgICAgICB9KSwgcmVmKTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSkpO1xuICAgICAgfVxuICAgIH1dLCBbe1xuICAgICAga2V5OiBcImdldERlcml2ZWRTdGF0ZUZyb21Qcm9wc1wiLFxuICAgICAgdmFsdWU6IGZ1bmN0aW9uIGdldERlcml2ZWRTdGF0ZUZyb21Qcm9wcyhfcmVmMywgX3JlZjQpIHtcbiAgICAgICAgdmFyIGtleXMgPSBfcmVmMy5rZXlzO1xuICAgICAgICB2YXIga2V5RW50aXRpZXMgPSBfcmVmNC5rZXlFbnRpdGllcztcbiAgICAgICAgdmFyIHBhcnNlZEtleU9iamVjdHMgPSBwYXJzZUtleXMoa2V5cyk7XG4gICAgICAgIHZhciBtaXhlZEtleUVudGl0aWVzID0gZGlmZktleXMoa2V5RW50aXRpZXMsIHBhcnNlZEtleU9iamVjdHMpO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGtleUVudGl0aWVzOiBtaXhlZEtleUVudGl0aWVzLmZpbHRlcihmdW5jdGlvbiAoZW50aXR5KSB7XG4gICAgICAgICAgICB2YXIgcHJldkVudGl0eSA9IGtleUVudGl0aWVzLmZpbmQoZnVuY3Rpb24gKF9yZWY1KSB7XG4gICAgICAgICAgICAgIHZhciBrZXkgPSBfcmVmNS5rZXk7XG4gICAgICAgICAgICAgIHJldHVybiBlbnRpdHkua2V5ID09PSBrZXk7XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgLy8gUmVtb3ZlIGlmIGFscmVhZHkgbWFyayBhcyByZW1vdmVkXG4gICAgICAgICAgICBpZiAocHJldkVudGl0eSAmJiBwcmV2RW50aXR5LnN0YXR1cyA9PT0gU1RBVFVTX1JFTU9WRUQgJiYgZW50aXR5LnN0YXR1cyA9PT0gU1RBVFVTX1JFTU9WRSkge1xuICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICB9KVxuICAgICAgICB9O1xuICAgICAgfVxuICAgIH1dKTtcbiAgICByZXR1cm4gQ1NTTW90aW9uTGlzdDtcbiAgfShSZWFjdC5Db21wb25lbnQpO1xuICBfZGVmaW5lUHJvcGVydHkoQ1NTTW90aW9uTGlzdCwgXCJkZWZhdWx0UHJvcHNcIiwge1xuICAgIGNvbXBvbmVudDogJ2RpdidcbiAgfSk7XG4gIHJldHVybiBDU1NNb3Rpb25MaXN0O1xufVxuZXhwb3J0IGRlZmF1bHQgZ2VuQ1NTTW90aW9uTGlzdChzdXBwb3J0VHJhbnNpdGlvbik7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/CSSMotionList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/DomWrapper.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-motion/es/DomWrapper.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nvar DomWrapper = /*#__PURE__*/function (_React$Component) {\n  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(DomWrapper, _React$Component);\n  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(DomWrapper);\n  function DomWrapper() {\n    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, DomWrapper);\n    return _super.apply(this, arguments);\n  }\n  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(DomWrapper, [{\n    key: \"render\",\n    value: function render() {\n      return this.props.children;\n    }\n  }]);\n  return DomWrapper;\n}(react__WEBPACK_IMPORTED_MODULE_4__.Component);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DomWrapper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL0RvbVdyYXBwZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3RTtBQUNOO0FBQ047QUFDTTtBQUNuQztBQUMvQjtBQUNBLEVBQUUsK0VBQVM7QUFDWCxlQUFlLGtGQUFZO0FBQzNCO0FBQ0EsSUFBSSxxRkFBZTtBQUNuQjtBQUNBO0FBQ0EsRUFBRSxrRkFBWTtBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsQ0FBQyxDQUFDLDRDQUFlO0FBQ2pCLGlFQUFlLFVBQVUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLy4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9Eb21XcmFwcGVyLmpzPzVmMTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9jbGFzc0NhbGxDaGVjayBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY2xhc3NDYWxsQ2hlY2tcIjtcbmltcG9ydCBfY3JlYXRlQ2xhc3MgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2NyZWF0ZUNsYXNzXCI7XG5pbXBvcnQgX2luaGVyaXRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9pbmhlcml0c1wiO1xuaW1wb3J0IF9jcmVhdGVTdXBlciBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlU3VwZXJcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbnZhciBEb21XcmFwcGVyID0gLyojX19QVVJFX18qL2Z1bmN0aW9uIChfUmVhY3QkQ29tcG9uZW50KSB7XG4gIF9pbmhlcml0cyhEb21XcmFwcGVyLCBfUmVhY3QkQ29tcG9uZW50KTtcbiAgdmFyIF9zdXBlciA9IF9jcmVhdGVTdXBlcihEb21XcmFwcGVyKTtcbiAgZnVuY3Rpb24gRG9tV3JhcHBlcigpIHtcbiAgICBfY2xhc3NDYWxsQ2hlY2sodGhpcywgRG9tV3JhcHBlcik7XG4gICAgcmV0dXJuIF9zdXBlci5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICB9XG4gIF9jcmVhdGVDbGFzcyhEb21XcmFwcGVyLCBbe1xuICAgIGtleTogXCJyZW5kZXJcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gcmVuZGVyKCkge1xuICAgICAgcmV0dXJuIHRoaXMucHJvcHMuY2hpbGRyZW47XG4gICAgfVxuICB9XSk7XG4gIHJldHVybiBEb21XcmFwcGVyO1xufShSZWFjdC5Db21wb25lbnQpO1xuZXhwb3J0IGRlZmF1bHQgRG9tV3JhcHBlcjsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/DomWrapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/context.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-motion/es/context.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context),\n/* harmony export */   \"default\": () => (/* binding */ MotionProvider)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _excluded = [\"children\"];\n\nvar Context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createContext({});\nfunction MotionProvider(_ref) {\n  var children = _ref.children,\n    props = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref, _excluded);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Context.Provider, {\n    value: props\n  }, children);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEY7QUFDMUY7QUFDK0I7QUFDeEIsMkJBQTJCLGdEQUFtQixHQUFHO0FBQ3pDO0FBQ2Y7QUFDQSxZQUFZLDhGQUF3QjtBQUNwQyxzQkFBc0IsZ0RBQW1CO0FBQ3pDO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8uL25vZGVfbW9kdWxlcy9yYy1tb3Rpb24vZXMvY29udGV4dC5qcz9hZjU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzXCI7XG52YXIgX2V4Y2x1ZGVkID0gW1wiY2hpbGRyZW5cIl07XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5leHBvcnQgdmFyIENvbnRleHQgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlQ29udGV4dCh7fSk7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNb3Rpb25Qcm92aWRlcihfcmVmKSB7XG4gIHZhciBjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW4sXG4gICAgcHJvcHMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMoX3JlZiwgX2V4Y2x1ZGVkKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KENvbnRleHQuUHJvdmlkZXIsIHtcbiAgICB2YWx1ZTogcHJvcHNcbiAgfSwgY2hpbGRyZW4pO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js":
/*!***************************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useDomMotionEvents.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (onInternalMotionEnd) {\n  var cacheElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n\n  // Remove events\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.animationEndName, onInternalMotionEnd);\n    }\n  }\n\n  // Patch events\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.transitionEndName, onInternalMotionEnd);\n      element.addEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.animationEndName, onInternalMotionEnd);\n\n      // Save as cache in case dom removed trigger by `motionDeadline`\n      cacheElementRef.current = element;\n    }\n  }\n\n  // Clean up when removed\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/./node_modules/rc-util/es/Dom/canUseDom.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n// It's safe to use `useLayoutEffect` but the warning is annoying\nvar useIsomorphicLayoutEffect = (0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__[\"default\"])() ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useIsomorphicLayoutEffect);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2hvb2tzL3VzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpRDtBQUNFOztBQUVuRDtBQUNBLGdDQUFnQyxvRUFBUyxLQUFLLGtEQUFlLEdBQUcsNENBQVM7QUFDekUsaUVBQWUseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWFkbWluLWRhc2hib2FyZC8uL25vZGVfbW9kdWxlcy9yYy1tb3Rpb24vZXMvaG9va3MvdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdC5qcz8xNzc0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjYW5Vc2VEb20gZnJvbSBcInJjLXV0aWwvZXMvRG9tL2NhblVzZURvbVwiO1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbi8vIEl0J3Mgc2FmZSB0byB1c2UgYHVzZUxheW91dEVmZmVjdGAgYnV0IHRoZSB3YXJuaW5nIGlzIGFubm95aW5nXG52YXIgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IGNhblVzZURvbSgpID8gdXNlTGF5b3V0RWZmZWN0IDogdXNlRWZmZWN0O1xuZXhwb3J0IGRlZmF1bHQgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdDsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useNextFrame.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function () {\n  var nextFrameRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  function cancelNextFrame() {\n    rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"].cancel(nextFrameRef.current);\n  }\n  function nextFrame(callback) {\n    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n    cancelNextFrame();\n    var nextFrameId = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(function () {\n      if (delay <= 1) {\n        callback({\n          isCanceled: function isCanceled() {\n            return nextFrameId !== nextFrameRef.current;\n          }\n        });\n      } else {\n        nextFrame(callback, delay - 1);\n      }\n    });\n    nextFrameRef.current = nextFrameId;\n  }\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [nextFrame, cancelNextFrame];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2hvb2tzL3VzZU5leHRGcmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWlDO0FBQ0Y7QUFDL0IsaUVBQWdCO0FBQ2hCLHFCQUFxQix5Q0FBWTtBQUNqQztBQUNBLElBQUksc0RBQUc7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBRztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLy4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9ob29rcy91c2VOZXh0RnJhbWUuanM/NDY5YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgcmFmIGZyb20gXCJyYy11dGlsL2VzL3JhZlwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuZXhwb3J0IGRlZmF1bHQgKGZ1bmN0aW9uICgpIHtcbiAgdmFyIG5leHRGcmFtZVJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgZnVuY3Rpb24gY2FuY2VsTmV4dEZyYW1lKCkge1xuICAgIHJhZi5jYW5jZWwobmV4dEZyYW1lUmVmLmN1cnJlbnQpO1xuICB9XG4gIGZ1bmN0aW9uIG5leHRGcmFtZShjYWxsYmFjaykge1xuICAgIHZhciBkZWxheSA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDogMjtcbiAgICBjYW5jZWxOZXh0RnJhbWUoKTtcbiAgICB2YXIgbmV4dEZyYW1lSWQgPSByYWYoZnVuY3Rpb24gKCkge1xuICAgICAgaWYgKGRlbGF5IDw9IDEpIHtcbiAgICAgICAgY2FsbGJhY2soe1xuICAgICAgICAgIGlzQ2FuY2VsZWQ6IGZ1bmN0aW9uIGlzQ2FuY2VsZWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gbmV4dEZyYW1lSWQgIT09IG5leHRGcmFtZVJlZi5jdXJyZW50O1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBuZXh0RnJhbWUoY2FsbGJhY2ssIGRlbGF5IC0gMSk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgbmV4dEZyYW1lUmVmLmN1cnJlbnQgPSBuZXh0RnJhbWVJZDtcbiAgfVxuICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICBjYW5jZWxOZXh0RnJhbWUoKTtcbiAgICB9O1xuICB9LCBbXSk7XG4gIHJldHVybiBbbmV4dEZyYW1lLCBjYW5jZWxOZXh0RnJhbWVdO1xufSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js":
/*!******************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useStatus.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useStatus)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util */ \"(ssr)/./node_modules/rc-util/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/hooks/useState */ \"(ssr)/./node_modules/rc-util/es/hooks/useState.js\");\n/* harmony import */ var rc_util_es_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/hooks/useSyncState */ \"(ssr)/./node_modules/rc-util/es/hooks/useSyncState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _useDomMotionEvents__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useDomMotionEvents */ \"(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useStepQueue__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./useStepQueue */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n    motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n    _ref$motionAppear = _ref.motionAppear,\n    motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n    _ref$motionLeave = _ref.motionLeave,\n    motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n    motionDeadline = _ref.motionDeadline,\n    motionLeaveImmediately = _ref.motionLeaveImmediately,\n    onAppearPrepare = _ref.onAppearPrepare,\n    onEnterPrepare = _ref.onEnterPrepare,\n    onLeavePrepare = _ref.onLeavePrepare,\n    onAppearStart = _ref.onAppearStart,\n    onEnterStart = _ref.onEnterStart,\n    onLeaveStart = _ref.onLeaveStart,\n    onAppearActive = _ref.onAppearActive,\n    onEnterActive = _ref.onEnterActive,\n    onLeaveActive = _ref.onLeaveActive,\n    onAppearEnd = _ref.onAppearEnd,\n    onEnterEnd = _ref.onEnterEnd,\n    onLeaveEnd = _ref.onLeaveEnd,\n    onVisibleChanged = _ref.onVisibleChanged;\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    asyncVisible = _useState2[0],\n    setAsyncVisible = _useState2[1];\n  var _useSyncState = (0,rc_util_es_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE),\n    _useSyncState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useSyncState, 2),\n    getStatus = _useSyncState2[0],\n    setStatus = _useSyncState2[1];\n  var _useState3 = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    style = _useState4[0],\n    setStyle = _useState4[1];\n  var currentStatus = getStatus();\n  var mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(false);\n  var deadlineRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n\n  // =========================== Dom Node ===========================\n  function getDomElement() {\n    return getElement();\n  }\n\n  // ========================== Motion End ==========================\n  var activeRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(false);\n\n  /**\n   * Clean up status & style\n   */\n  function updateMotionEndStatus() {\n    setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    setStyle(null, true);\n  }\n  var onInternalMotionEnd = (0,rc_util__WEBPACK_IMPORTED_MODULE_3__.useEvent)(function (event) {\n    var status = getStatus();\n    // Do nothing since not in any transition status.\n    // This may happen when `motionDeadline` trigger.\n    if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n      return;\n    }\n    var element = getDomElement();\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n    var currentActive = activeRef.current;\n    var canEnd;\n    if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    }\n\n    // Only update status when `canEnd` and not destroyed\n    if (currentActive && canEnd !== false) {\n      updateMotionEndStatus();\n    }\n  });\n  var _useDomMotionEvents = (0,_useDomMotionEvents__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(onInternalMotionEnd),\n    _useDomMotionEvents2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useDomMotionEvents, 1),\n    patchMotionEvents = _useDomMotionEvents2[0];\n\n  // ============================= Step =============================\n  var getEventHandlers = function getEventHandlers(targetStatus) {\n    switch (targetStatus) {\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onAppearPrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onAppearStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onAppearActive);\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onEnterPrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onEnterStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onEnterActive);\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onLeavePrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onLeaveStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onLeaveActive);\n      default:\n        return {};\n    }\n  };\n  var eventHandlers = react__WEBPACK_IMPORTED_MODULE_6__.useMemo(function () {\n    return getEventHandlers(currentStatus);\n  }, [currentStatus]);\n  var _useStepQueue = (0,_useStepQueue__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(currentStatus, !supportMotion, function (newStep) {\n      // Only prepare step can be skip\n      if (newStep === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE) {\n        var onPrepare = eventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE];\n        if (!onPrepare) {\n          return _useStepQueue__WEBPACK_IMPORTED_MODULE_10__.SkipStep;\n        }\n        return onPrepare(getDomElement());\n      }\n\n      // Rest step is sync update\n      if (step in eventHandlers) {\n        var _eventHandlers$step;\n        setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n      }\n      if (step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE && currentStatus !== _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n        // Patch events when motion needed\n        patchMotionEvents(getDomElement());\n        if (motionDeadline > 0) {\n          clearTimeout(deadlineRef.current);\n          deadlineRef.current = setTimeout(function () {\n            onInternalMotionEnd({\n              deadline: true\n            });\n          }, motionDeadline);\n        }\n      }\n      if (step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARED) {\n        updateMotionEndStatus();\n      }\n      return _useStepQueue__WEBPACK_IMPORTED_MODULE_10__.DoStep;\n    }),\n    _useStepQueue2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useStepQueue, 2),\n    startStep = _useStepQueue2[0],\n    step = _useStepQueue2[1];\n  var active = (0,_useStepQueue__WEBPACK_IMPORTED_MODULE_10__.isActive)(step);\n  activeRef.current = active;\n\n  // ============================ Status ============================\n  var visibleRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n\n  // Update with new status\n  (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function () {\n    // When use Suspense, the `visible` will repeat trigger,\n    // But not real change of the `visible`, we need to skip it.\n    // https://github.com/ant-design/ant-design/issues/44379\n    if (mountedRef.current && visibleRef.current === visible) {\n      return;\n    }\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n\n    // if (!supportMotion) {\n    //   return;\n    // }\n\n    var nextStatus;\n\n    // Appear\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR;\n    }\n\n    // Enter\n    if (isMounted && visible && motionEnter) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER;\n    }\n\n    // Leave\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE;\n    }\n    var nextEventHandlers = getEventHandlers(nextStatus);\n\n    // Update to next status\n    if (nextStatus && (supportMotion || nextEventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE])) {\n      setStatus(nextStatus);\n      startStep();\n    } else {\n      // Set back in case no motion but prev status has prepare step\n      setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    }\n    visibleRef.current = visible;\n  }, [visible]);\n\n  // ============================ Effect ============================\n  // Reset when motion changed\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    if (\n    // Cancel appear\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR && !motionAppear ||\n    // Cancel enter\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER && !motionEnter ||\n    // Cancel leave\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE && !motionLeave) {\n      setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []);\n\n  // Trigger `onVisibleChanged`\n  var firstMountChangeRef = react__WEBPACK_IMPORTED_MODULE_6__.useRef(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    // [visible & motion not end] => [!visible & motion end] still need trigger onVisibleChanged\n    if (asyncVisible) {\n      firstMountChangeRef.current = true;\n    }\n    if (asyncVisible !== undefined && currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n      // Skip first render is invisible since it's nothing changed\n      if (firstMountChangeRef.current || asyncVisible) {\n        onVisibleChanged === null || onVisibleChanged === void 0 || onVisibleChanged(asyncVisible);\n      }\n      firstMountChangeRef.current = true;\n    }\n  }, [asyncVisible, currentStatus]);\n\n  // ============================ Styles ============================\n  var mergedStyle = style;\n  if (eventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE] && step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START) {\n    mergedStyle = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      transition: 'none'\n    }, mergedStyle);\n  }\n  return [currentStatus, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useStepQueue.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DoStep: () => (/* binding */ DoStep),\n/* harmony export */   SkipStep: () => (/* binding */ SkipStep),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isActive: () => (/* binding */ isActive)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useState */ \"(ssr)/./node_modules/rc-util/es/hooks/useState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useNextFrame__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useNextFrame */ \"(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js\");\n\n\n\n\n\n\nvar FULL_STEP_QUEUE = [_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_START, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED];\nvar SIMPLE_STEP_QUEUE = [_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARED];\n\n/** Skip current step */\nvar SkipStep = false;\n/** Current step should be update in */\nvar DoStep = true;\nfunction isActive(step) {\n  return step === _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVE || step === _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (status, prepareOnly, callback) {\n  var _useState = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_NONE),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    step = _useState2[0],\n    setStep = _useState2[1];\n  var _useNextFrame = (0,_useNextFrame__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n    _useNextFrame2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useNextFrame, 2),\n    nextFrame = _useNextFrame2[0],\n    cancelNextFrame = _useNextFrame2[1];\n  function startQueue() {\n    setStep(_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, true);\n  }\n  var STEP_QUEUE = prepareOnly ? SIMPLE_STEP_QUEUE : FULL_STEP_QUEUE;\n  (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function () {\n    if (step !== _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_NONE && step !== _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED) {\n      var index = STEP_QUEUE.indexOf(step);\n      var nextStep = STEP_QUEUE[index + 1];\n      var result = callback(step);\n      if (result === SkipStep) {\n        // Skip when no needed\n        setStep(nextStep, true);\n      } else if (nextStep) {\n        // Do as frame for step update\n        nextFrame(function (info) {\n          function doNext() {\n            // Skip since current queue is ood\n            if (info.isCanceled()) return;\n            setStep(nextStep, true);\n          }\n          if (result === true) {\n            doNext();\n          } else {\n            // Only promise should be async\n            Promise.resolve(result).then(doNext);\n          }\n        });\n      }\n    }\n  }, [status, step]);\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [startQueue, step];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/index.js":
/*!********************************************!*\
  !*** ./node_modules/rc-motion/es/index.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSMotionList: () => (/* reexport safe */ _CSSMotionList__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Provider: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _CSSMotion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CSSMotion */ \"(ssr)/./node_modules/rc-motion/es/CSSMotion.js\");\n/* harmony import */ var _CSSMotionList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CSSMotionList */ \"(ssr)/./node_modules/rc-motion/es/CSSMotionList.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-motion/es/context.js\");\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_CSSMotion__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFvQztBQUNRO0FBQ0k7QUFDdkI7QUFDekIsaUVBQWUsa0RBQVMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtYWRtaW4tZGFzaGJvYXJkLy4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9pbmRleC5qcz8xNjE4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBDU1NNb3Rpb24gZnJvbSBcIi4vQ1NTTW90aW9uXCI7XG5pbXBvcnQgQ1NTTW90aW9uTGlzdCBmcm9tIFwiLi9DU1NNb3Rpb25MaXN0XCI7XG5leHBvcnQgeyBkZWZhdWx0IGFzIFByb3ZpZGVyIH0gZnJvbSBcIi4vY29udGV4dFwiO1xuZXhwb3J0IHsgQ1NTTW90aW9uTGlzdCB9O1xuZXhwb3J0IGRlZmF1bHQgQ1NTTW90aW9uOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/interface.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/interface.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_APPEAR: () => (/* binding */ STATUS_APPEAR),\n/* harmony export */   STATUS_ENTER: () => (/* binding */ STATUS_ENTER),\n/* harmony export */   STATUS_LEAVE: () => (/* binding */ STATUS_LEAVE),\n/* harmony export */   STATUS_NONE: () => (/* binding */ STATUS_NONE),\n/* harmony export */   STEP_ACTIVATED: () => (/* binding */ STEP_ACTIVATED),\n/* harmony export */   STEP_ACTIVE: () => (/* binding */ STEP_ACTIVE),\n/* harmony export */   STEP_NONE: () => (/* binding */ STEP_NONE),\n/* harmony export */   STEP_PREPARE: () => (/* binding */ STEP_PREPARE),\n/* harmony export */   STEP_PREPARED: () => (/* binding */ STEP_PREPARED),\n/* harmony export */   STEP_START: () => (/* binding */ STEP_START)\n/* harmony export */ });\nvar STATUS_NONE = 'none';\nvar STATUS_APPEAR = 'appear';\nvar STATUS_ENTER = 'enter';\nvar STATUS_LEAVE = 'leave';\nvar STEP_NONE = 'none';\nvar STEP_PREPARE = 'prepare';\nvar STEP_START = 'start';\nvar STEP_ACTIVE = 'active';\nvar STEP_ACTIVATED = 'end';\n/**\n * Used for disabled motion case.\n * Prepare stage will still work but start & active will be skipped.\n */\nvar STEP_PREPARED = 'prepared';//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2ludGVyZmFjZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2ludGVyZmFjZS5qcz81MzAyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgU1RBVFVTX05PTkUgPSAnbm9uZSc7XG5leHBvcnQgdmFyIFNUQVRVU19BUFBFQVIgPSAnYXBwZWFyJztcbmV4cG9ydCB2YXIgU1RBVFVTX0VOVEVSID0gJ2VudGVyJztcbmV4cG9ydCB2YXIgU1RBVFVTX0xFQVZFID0gJ2xlYXZlJztcbmV4cG9ydCB2YXIgU1RFUF9OT05FID0gJ25vbmUnO1xuZXhwb3J0IHZhciBTVEVQX1BSRVBBUkUgPSAncHJlcGFyZSc7XG5leHBvcnQgdmFyIFNURVBfU1RBUlQgPSAnc3RhcnQnO1xuZXhwb3J0IHZhciBTVEVQX0FDVElWRSA9ICdhY3RpdmUnO1xuZXhwb3J0IHZhciBTVEVQX0FDVElWQVRFRCA9ICdlbmQnO1xuLyoqXG4gKiBVc2VkIGZvciBkaXNhYmxlZCBtb3Rpb24gY2FzZS5cbiAqIFByZXBhcmUgc3RhZ2Ugd2lsbCBzdGlsbCB3b3JrIGJ1dCBzdGFydCAmIGFjdGl2ZSB3aWxsIGJlIHNraXBwZWQuXG4gKi9cbmV4cG9ydCB2YXIgU1RFUF9QUkVQQVJFRCA9ICdwcmVwYXJlZCc7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/interface.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/util/diff.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/util/diff.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_ADD: () => (/* binding */ STATUS_ADD),\n/* harmony export */   STATUS_KEEP: () => (/* binding */ STATUS_KEEP),\n/* harmony export */   STATUS_REMOVE: () => (/* binding */ STATUS_REMOVE),\n/* harmony export */   STATUS_REMOVED: () => (/* binding */ STATUS_REMOVED),\n/* harmony export */   diffKeys: () => (/* binding */ diffKeys),\n/* harmony export */   parseKeys: () => (/* binding */ parseKeys),\n/* harmony export */   wrapKeyToObject: () => (/* binding */ wrapKeyToObject)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n\n\nvar STATUS_ADD = 'add';\nvar STATUS_KEEP = 'keep';\nvar STATUS_REMOVE = 'remove';\nvar STATUS_REMOVED = 'removed';\nfunction wrapKeyToObject(key) {\n  var keyObj;\n  if (key && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key) === 'object' && 'key' in key) {\n    keyObj = key;\n  } else {\n    keyObj = {\n      key: key\n    };\n  }\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, keyObj), {}, {\n    key: String(keyObj.key)\n  });\n}\nfunction parseKeys() {\n  var keys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return keys.map(wrapKeyToObject);\n}\nfunction diffKeys() {\n  var prevKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var currentKeys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var list = [];\n  var currentIndex = 0;\n  var currentLen = currentKeys.length;\n  var prevKeyObjects = parseKeys(prevKeys);\n  var currentKeyObjects = parseKeys(currentKeys);\n\n  // Check prev keys to insert or keep\n  prevKeyObjects.forEach(function (keyObj) {\n    var hit = false;\n    for (var i = currentIndex; i < currentLen; i += 1) {\n      var currentKeyObj = currentKeyObjects[i];\n      if (currentKeyObj.key === keyObj.key) {\n        // New added keys should add before current key\n        if (currentIndex < i) {\n          list = list.concat(currentKeyObjects.slice(currentIndex, i).map(function (obj) {\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, obj), {}, {\n              status: STATUS_ADD\n            });\n          }));\n          currentIndex = i;\n        }\n        list.push((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, currentKeyObj), {}, {\n          status: STATUS_KEEP\n        }));\n        currentIndex += 1;\n        hit = true;\n        break;\n      }\n    }\n\n    // If not hit, it means key is removed\n    if (!hit) {\n      list.push((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, keyObj), {}, {\n        status: STATUS_REMOVE\n      }));\n    }\n  });\n\n  // Add rest to the list\n  if (currentIndex < currentLen) {\n    list = list.concat(currentKeyObjects.slice(currentIndex).map(function (obj) {\n      return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, obj), {}, {\n        status: STATUS_ADD\n      });\n    }));\n  }\n\n  /**\n   * Merge same key when it remove and add again:\n   *    [1 - add, 2 - keep, 1 - remove] -> [1 - keep, 2 - keep]\n   */\n  var keys = {};\n  list.forEach(function (_ref) {\n    var key = _ref.key;\n    keys[key] = (keys[key] || 0) + 1;\n  });\n  var duplicatedKeys = Object.keys(keys).filter(function (key) {\n    return keys[key] > 1;\n  });\n  duplicatedKeys.forEach(function (matchKey) {\n    // Remove `STATUS_REMOVE` node.\n    list = list.filter(function (_ref2) {\n      var key = _ref2.key,\n        status = _ref2.status;\n      return key !== matchKey || status !== STATUS_REMOVE;\n    });\n\n    // Update `STATUS_ADD` to `STATUS_KEEP`\n    list.forEach(function (node) {\n      if (node.key === matchKey) {\n        // eslint-disable-next-line no-param-reassign\n        node.status = STATUS_KEEP;\n      }\n    });\n  });\n  return list;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/util/diff.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/util/motion.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-motion/es/util/motion.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animationEndName: () => (/* binding */ animationEndName),\n/* harmony export */   getTransitionName: () => (/* binding */ getTransitionName),\n/* harmony export */   getVendorPrefixedEventName: () => (/* binding */ getVendorPrefixedEventName),\n/* harmony export */   getVendorPrefixes: () => (/* binding */ getVendorPrefixes),\n/* harmony export */   supportTransition: () => (/* binding */ supportTransition),\n/* harmony export */   transitionEndName: () => (/* binding */ transitionEndName)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/./node_modules/rc-util/es/Dom/canUseDom.js\");\n\n\n// ================= Transition =================\n// Event wrapper. Copy from react source code\nfunction makePrefixMap(styleProp, eventName) {\n  var prefixes = {};\n  prefixes[styleProp.toLowerCase()] = eventName.toLowerCase();\n  prefixes[\"Webkit\".concat(styleProp)] = \"webkit\".concat(eventName);\n  prefixes[\"Moz\".concat(styleProp)] = \"moz\".concat(eventName);\n  prefixes[\"ms\".concat(styleProp)] = \"MS\".concat(eventName);\n  prefixes[\"O\".concat(styleProp)] = \"o\".concat(eventName.toLowerCase());\n  return prefixes;\n}\nfunction getVendorPrefixes(domSupport, win) {\n  var prefixes = {\n    animationend: makePrefixMap('Animation', 'AnimationEnd'),\n    transitionend: makePrefixMap('Transition', 'TransitionEnd')\n  };\n  if (domSupport) {\n    if (!('AnimationEvent' in win)) {\n      delete prefixes.animationend.animation;\n    }\n    if (!('TransitionEvent' in win)) {\n      delete prefixes.transitionend.transition;\n    }\n  }\n  return prefixes;\n}\nvar vendorPrefixes = getVendorPrefixes((0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(), typeof window !== 'undefined' ? window : {});\nvar style = {};\nif ((0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__[\"default\"])()) {\n  var _document$createEleme = document.createElement('div');\n  style = _document$createEleme.style;\n}\nvar prefixedEventNames = {};\nfunction getVendorPrefixedEventName(eventName) {\n  if (prefixedEventNames[eventName]) {\n    return prefixedEventNames[eventName];\n  }\n  var prefixMap = vendorPrefixes[eventName];\n  if (prefixMap) {\n    var stylePropList = Object.keys(prefixMap);\n    var len = stylePropList.length;\n    for (var i = 0; i < len; i += 1) {\n      var styleProp = stylePropList[i];\n      if (Object.prototype.hasOwnProperty.call(prefixMap, styleProp) && styleProp in style) {\n        prefixedEventNames[eventName] = prefixMap[styleProp];\n        return prefixedEventNames[eventName];\n      }\n    }\n  }\n  return '';\n}\nvar internalAnimationEndName = getVendorPrefixedEventName('animationend');\nvar internalTransitionEndName = getVendorPrefixedEventName('transitionend');\nvar supportTransition = !!(internalAnimationEndName && internalTransitionEndName);\nvar animationEndName = internalAnimationEndName || 'animationend';\nvar transitionEndName = internalTransitionEndName || 'transitionend';\nfunction getTransitionName(transitionName, transitionType) {\n  if (!transitionName) return null;\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(transitionName) === 'object') {\n    var type = transitionType.replace(/-\\w/g, function (match) {\n      return match[1].toUpperCase();\n    });\n    return transitionName[type];\n  }\n  return \"\".concat(transitionName, \"-\").concat(transitionType);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL3V0aWwvbW90aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXdEO0FBQ1A7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxvRUFBUywrQ0FBK0M7QUFDL0Y7QUFDQSxJQUFJLG9FQUFTO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFNBQVM7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ0E7QUFDQTtBQUNBO0FBQ1A7QUFDQSxNQUFNLDZFQUFPO0FBQ2I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1hZG1pbi1kYXNoYm9hcmQvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL3V0aWwvbW90aW9uLmpzPzdjMGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF90eXBlb2YgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3R5cGVvZlwiO1xuaW1wb3J0IGNhblVzZURPTSBmcm9tIFwicmMtdXRpbC9lcy9Eb20vY2FuVXNlRG9tXCI7XG4vLyA9PT09PT09PT09PT09PT09PSBUcmFuc2l0aW9uID09PT09PT09PT09PT09PT09XG4vLyBFdmVudCB3cmFwcGVyLiBDb3B5IGZyb20gcmVhY3Qgc291cmNlIGNvZGVcbmZ1bmN0aW9uIG1ha2VQcmVmaXhNYXAoc3R5bGVQcm9wLCBldmVudE5hbWUpIHtcbiAgdmFyIHByZWZpeGVzID0ge307XG4gIHByZWZpeGVzW3N0eWxlUHJvcC50b0xvd2VyQ2FzZSgpXSA9IGV2ZW50TmFtZS50b0xvd2VyQ2FzZSgpO1xuICBwcmVmaXhlc1tcIldlYmtpdFwiLmNvbmNhdChzdHlsZVByb3ApXSA9IFwid2Via2l0XCIuY29uY2F0KGV2ZW50TmFtZSk7XG4gIHByZWZpeGVzW1wiTW96XCIuY29uY2F0KHN0eWxlUHJvcCldID0gXCJtb3pcIi5jb25jYXQoZXZlbnROYW1lKTtcbiAgcHJlZml4ZXNbXCJtc1wiLmNvbmNhdChzdHlsZVByb3ApXSA9IFwiTVNcIi5jb25jYXQoZXZlbnROYW1lKTtcbiAgcHJlZml4ZXNbXCJPXCIuY29uY2F0KHN0eWxlUHJvcCldID0gXCJvXCIuY29uY2F0KGV2ZW50TmFtZS50b0xvd2VyQ2FzZSgpKTtcbiAgcmV0dXJuIHByZWZpeGVzO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGdldFZlbmRvclByZWZpeGVzKGRvbVN1cHBvcnQsIHdpbikge1xuICB2YXIgcHJlZml4ZXMgPSB7XG4gICAgYW5pbWF0aW9uZW5kOiBtYWtlUHJlZml4TWFwKCdBbmltYXRpb24nLCAnQW5pbWF0aW9uRW5kJyksXG4gICAgdHJhbnNpdGlvbmVuZDogbWFrZVByZWZpeE1hcCgnVHJhbnNpdGlvbicsICdUcmFuc2l0aW9uRW5kJylcbiAgfTtcbiAgaWYgKGRvbVN1cHBvcnQpIHtcbiAgICBpZiAoISgnQW5pbWF0aW9uRXZlbnQnIGluIHdpbikpIHtcbiAgICAgIGRlbGV0ZSBwcmVmaXhlcy5hbmltYXRpb25lbmQuYW5pbWF0aW9uO1xuICAgIH1cbiAgICBpZiAoISgnVHJhbnNpdGlvbkV2ZW50JyBpbiB3aW4pKSB7XG4gICAgICBkZWxldGUgcHJlZml4ZXMudHJhbnNpdGlvbmVuZC50cmFuc2l0aW9uO1xuICAgIH1cbiAgfVxuICByZXR1cm4gcHJlZml4ZXM7XG59XG52YXIgdmVuZG9yUHJlZml4ZXMgPSBnZXRWZW5kb3JQcmVmaXhlcyhjYW5Vc2VET00oKSwgdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyB3aW5kb3cgOiB7fSk7XG52YXIgc3R5bGUgPSB7fTtcbmlmIChjYW5Vc2VET00oKSkge1xuICB2YXIgX2RvY3VtZW50JGNyZWF0ZUVsZW1lID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7XG4gIHN0eWxlID0gX2RvY3VtZW50JGNyZWF0ZUVsZW1lLnN0eWxlO1xufVxudmFyIHByZWZpeGVkRXZlbnROYW1lcyA9IHt9O1xuZXhwb3J0IGZ1bmN0aW9uIGdldFZlbmRvclByZWZpeGVkRXZlbnROYW1lKGV2ZW50TmFtZSkge1xuICBpZiAocHJlZml4ZWRFdmVudE5hbWVzW2V2ZW50TmFtZV0pIHtcbiAgICByZXR1cm4gcHJlZml4ZWRFdmVudE5hbWVzW2V2ZW50TmFtZV07XG4gIH1cbiAgdmFyIHByZWZpeE1hcCA9IHZlbmRvclByZWZpeGVzW2V2ZW50TmFtZV07XG4gIGlmIChwcmVmaXhNYXApIHtcbiAgICB2YXIgc3R5bGVQcm9wTGlzdCA9IE9iamVjdC5rZXlzKHByZWZpeE1hcCk7XG4gICAgdmFyIGxlbiA9IHN0eWxlUHJvcExpc3QubGVuZ3RoO1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbGVuOyBpICs9IDEpIHtcbiAgICAgIHZhciBzdHlsZVByb3AgPSBzdHlsZVByb3BMaXN0W2ldO1xuICAgICAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChwcmVmaXhNYXAsIHN0eWxlUHJvcCkgJiYgc3R5bGVQcm9wIGluIHN0eWxlKSB7XG4gICAgICAgIHByZWZpeGVkRXZlbnROYW1lc1tldmVudE5hbWVdID0gcHJlZml4TWFwW3N0eWxlUHJvcF07XG4gICAgICAgIHJldHVybiBwcmVmaXhlZEV2ZW50TmFtZXNbZXZlbnROYW1lXTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuICcnO1xufVxudmFyIGludGVybmFsQW5pbWF0aW9uRW5kTmFtZSA9IGdldFZlbmRvclByZWZpeGVkRXZlbnROYW1lKCdhbmltYXRpb25lbmQnKTtcbnZhciBpbnRlcm5hbFRyYW5zaXRpb25FbmROYW1lID0gZ2V0VmVuZG9yUHJlZml4ZWRFdmVudE5hbWUoJ3RyYW5zaXRpb25lbmQnKTtcbmV4cG9ydCB2YXIgc3VwcG9ydFRyYW5zaXRpb24gPSAhIShpbnRlcm5hbEFuaW1hdGlvbkVuZE5hbWUgJiYgaW50ZXJuYWxUcmFuc2l0aW9uRW5kTmFtZSk7XG5leHBvcnQgdmFyIGFuaW1hdGlvbkVuZE5hbWUgPSBpbnRlcm5hbEFuaW1hdGlvbkVuZE5hbWUgfHwgJ2FuaW1hdGlvbmVuZCc7XG5leHBvcnQgdmFyIHRyYW5zaXRpb25FbmROYW1lID0gaW50ZXJuYWxUcmFuc2l0aW9uRW5kTmFtZSB8fCAndHJhbnNpdGlvbmVuZCc7XG5leHBvcnQgZnVuY3Rpb24gZ2V0VHJhbnNpdGlvbk5hbWUodHJhbnNpdGlvbk5hbWUsIHRyYW5zaXRpb25UeXBlKSB7XG4gIGlmICghdHJhbnNpdGlvbk5hbWUpIHJldHVybiBudWxsO1xuICBpZiAoX3R5cGVvZih0cmFuc2l0aW9uTmFtZSkgPT09ICdvYmplY3QnKSB7XG4gICAgdmFyIHR5cGUgPSB0cmFuc2l0aW9uVHlwZS5yZXBsYWNlKC8tXFx3L2csIGZ1bmN0aW9uIChtYXRjaCkge1xuICAgICAgcmV0dXJuIG1hdGNoWzFdLnRvVXBwZXJDYXNlKCk7XG4gICAgfSk7XG4gICAgcmV0dXJuIHRyYW5zaXRpb25OYW1lW3R5cGVdO1xuICB9XG4gIHJldHVybiBcIlwiLmNvbmNhdCh0cmFuc2l0aW9uTmFtZSwgXCItXCIpLmNvbmNhdCh0cmFuc2l0aW9uVHlwZSk7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/util/motion.js\n");

/***/ })

};
;
"use client";
import React, { useState, Suspense, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { message, Spin, Divider } from "antd";
import { useRouter } from "next/navigation";
import { useResetPasswordMutation } from "../../reduxRTK/services/authApi";
import Image from "next/image";

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<Spin size="large" />}>
      <ResetPassword />
    </Suspense>
  );
}

const ResetPassword = () => {
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();

  const userId = searchParams.get("userId");
  const token = searchParams.get("token");

  const [resetPassword, { isLoading: apiLoading, error, data }] = useResetPasswordMutation();

  const showMessage = (type: "success" | "error", content: string) => {
    if (type === "success") {
      message.success(content);
    } else {
      message.error(content);
    }
  };

  useEffect(() => {
    if (!userId || !token) {
      showMessage("error", "Invalid or missing parameters.");
      router.replace("/auth");
      return;
    }

    const verifyUser = async () => {
      try {
        await resetPassword({
          userId: userId || "",
          accessToken: token || "",
        });
      } catch (err: any) {
        showMessage("error", err?.message || "An unknown error occurred.");
        router.replace("/auth");
      }
    };

    setTimeout(() => {
      verifyUser();
    }, 1500);
  }, [userId, token, router, resetPassword]);

  useEffect(() => {
    if (data?.success) {
      showMessage("success", data.message || "Verifying Account successful.");
      localStorage.setItem("token", token || "");

      setTimeout(() => {
        setIsLoading(false);
        router.replace("/user/dashboard/");
      }, 1000); // Short delay to show success message
    } else if (error) {
      showMessage("error", error?.message || "An error occurred.");
      setIsLoading(false);
      router.replace("/auth");
    }
  }, [data, error, router, token]);

  return (
    <div className="relative min-h-screen flex items-center justify-center py-6 sm:py-12">
      {/* Background Image */}
      <Image
        src="/images/background.jpg"
        alt="Background"
        fill={true}
        style={{ objectFit: 'cover' }}
        className="absolute inset-0 z-0"
      />
      <div className="relative z-10 flex flex-col-reverse lg:flex-row bg-white bg-opacity-90 shadow-lg rounded-3xl w-full max-w-4xl">
        {/* Left Side: Verifying Spinner */}
        <div className="w-full lg:w-1/2 p-8 lg:p-12 flex flex-col justify-center items-center">
          <h2 className="text-2xl font-semibold text-center mb-4">
            Verifying Your Account
          </h2>
          <Divider />
          <Spin size="large" spinning={isLoading} />
          <p className="mt-4 text-center text-xl">Please wait while we verify your account...</p>
        </div>

        {/* Right Side: Welcome Message */}
        <div className="w-full lg:w-1/2 bg-gradient-to-r from-cyan-400 to-sky-500 text-white p-8 lg:p-12 flex flex-col justify-center">
          <h1 className="text-3xl font-bold text-center">
            Welcome to NyansaPo Cybersecurity Nano Learning Portal
          </h1>
          <p className="mt-4 text-center text-xl">
            We are securely verifying your account, just a moment...
          </p>
        </div>
      </div>
    </div>
  );
};

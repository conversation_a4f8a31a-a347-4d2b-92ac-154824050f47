"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateQuizSchema = exports.addQuizSchema = exports.updateCourseSchema = exports.addCourseSchema = exports.updateUserSchema = exports.loginUserSchema = exports.createUserSchema = exports.updateReportSchema = exports.addReportSchema = void 0;
const createUserSchema_1 = require("./createUserSchema");
Object.defineProperty(exports, "createUserSchema", { enumerable: true, get: function () { return createUserSchema_1.createUserSchema; } });
const loginSchema_1 = require("./loginSchema");
Object.defineProperty(exports, "loginUserSchema", { enumerable: true, get: function () { return loginSchema_1.loginUserSchema; } });
const updateUserSchema_1 = require("./updateUserSchema");
Object.defineProperty(exports, "updateUserSchema", { enumerable: true, get: function () { return updateUserSchema_1.updateUserSchema; } });
const courseSchema_1 = require("./courseSchema");
Object.defineProperty(exports, "addCourseSchema", { enumerable: true, get: function () { return courseSchema_1.addCourseSchema; } });
Object.defineProperty(exports, "updateCourseSchema", { enumerable: true, get: function () { return courseSchema_1.updateCourseSchema; } });
const addQuizSchema_1 = require("./addQuizSchema");
Object.defineProperty(exports, "addQuizSchema", { enumerable: true, get: function () { return addQuizSchema_1.addQuizSchema; } });
Object.defineProperty(exports, "updateQuizSchema", { enumerable: true, get: function () { return addQuizSchema_1.updateQuizSchema; } });
const reportSchema_1 = require("./reportSchema");
Object.defineProperty(exports, "addReportSchema", { enumerable: true, get: function () { return reportSchema_1.addReportSchema; } });
Object.defineProperty(exports, "updateReportSchema", { enumerable: true, get: function () { return reportSchema_1.updateReportSchema; } });

// Simple test script to verify assignment functionality
const testPayload = {
  mode: "assign",
  id: "8aa0fab1-ce3c-4551-a054-fa5064043452",
  userIds: ["51bac648-6330-4de8-8b08-092ee57493b9"],
  accessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.0t1bvWoS7iJoGWq_YdHZ7qnsXEREO-YMLqFyibEcTO8"
};

async function testAssignment() {
  try {
    const response = await fetch('http://localhost:5051/api/main-course-management', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    const result = await response.json();
    console.log('Response Status:', response.status);
    console.log('Response Body:', JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('✅ Assignment successful!');
    } else {
      console.log('❌ Assignment failed:', result.message);
    }
  } catch (error) {
    console.error('❌ Network error:', error.message);
  }
}

testAssignment();
